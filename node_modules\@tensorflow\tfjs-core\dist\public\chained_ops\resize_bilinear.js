/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { resizeBilinear } from '../../ops/image/resize_bilinear';
import { getGlobalTensorClass } from '../../tensor';
getGlobalTensorClass().prototype.resizeBilinear =
    function (newShape2D, alignCorners, halfPixelCenters) {
        this.throwIfDisposed();
        return resizeBilinear(this, newShape2D, alignCorners, halfPixelCenters);
    };
//# sourceMappingURL=data:application/json;base64,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