{"version": 3, "file": "to-svg.d.ts", "sourceRoot": "", "sources": ["../../src/utilities/to-svg.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AAEhE,OAAO,EAAE,SAAS,EAAE,MAAM,cAAc,CAAC;AACzC,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACjD,OAAO,EACL,kBAAkB,EAClB,kBAAkB,EAClB,aAAa,EACd,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,IAAI,EAAE,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAC3E,OAAO,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AAGzD,UAAU,YAAY;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,MAAM,CAAC;CACf;AAED,UAAU,YAAY;IACpB,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,MAAM,CAAC;CACf;AAED,UAAU,YAAY;IACpB,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,MAAM,CAAC;IACf,GAAG,EAAE,MAAM,CAAC;IACZ,MAAM,EAAE,MAAM,CAAC;CAChB;AAED,UAAU,aAAc,SAAQ,YAAY;IAC1C,IAAI,EAAE,YAAY,CAAC;IACnB,MAAM,EAAE,YAAY,GAAG;QAAE,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;KAAE,CAAC;IACpD,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,wBAAgB,SAAS,CAAC,EACxB,MAAM,EACN,MAAM,EACN,MAAM,EACN,MAAM,EACN,GAAG,EACH,IAAI,EACJ,QAAQ,EACR,aAAa,GACd,EAAE,aAAa,GAAG,MAAM,CA0BxB;AAED,MAAM,WAAW,cAAe,SAAQ,YAAY;IAClD,MAAM,EAAE,MAAM,CAAC;IACf,MAAM,EAAE,YAAY,CAAC;CACtB;AAED,wBAAgB,UAAU,CAAC,EACzB,MAAM,EACN,MAAM,EACN,GAAG,EACH,MAAM,EACN,MAAM,EACN,MAAM,GACP,EAAE,cAAc,GAAG,MAAM,CASzB;AAED,MAAM,WAAW,cAAe,SAAQ,YAAY;IAClD,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,YAAY,CAAC;IACnB,OAAO,EAAE,YAAY,CAAC;CACvB;AAED,wBAAgB,UAAU,CAAC,EACzB,MAAM,EACN,MAAM,EACN,GAAG,EACH,MAAM,EACN,IAAI,EACJ,OAAO,EACP,MAAM,GACP,EAAE,cAAc,GAAG,MAAM,CAgBzB;AAED,MAAM,WAAW,2BAA4B,SAAQ,YAAY;IAC/D,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,uBAAuB,EAAE,MAAM,CAAC;IAChC,IAAI,EAAE,YAAY,CAAC;CACpB;AAED,wBAAgB,uBAAuB,CAAC,EACtC,MAAM,EACN,MAAM,EACN,GAAG,EACH,MAAM,EACN,MAAM,EACN,KAAK,EACL,IAAI,EACJ,uBAAuB,GACxB,EAAE,2BAA2B,GAAG,MAAM,CAQtC;AAED,MAAM,WAAW,wBAAwB;IACvC,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,MAAM,EAAE,MAAM,CAAC;IACf,IAAI,EAAE,YAAY,CAAC;IACnB,MAAM,EAAE,YAAY,GAAG;QAAE,MAAM,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;KAAE,CAAC;IACpD,MAAM,EAAE,YAAY,CAAC;IACrB,OAAO,EAAE,YAAY,CAAC;IACtB,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,EAAE,MAAM,CAAC;CACvB;AAED,wBAAgB,uBAAuB,CACrC,OAAO,EAAE,wBAAwB,GAChC,MAAM,CAqCR;AAED,MAAM,WAAW,4BAA6B,SAAQ,YAAY;IAChE,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,EAAE,YAAY,CAAC;CAC7B;AAED,wBAAgB,wBAAwB,CAAC,EACvC,MAAM,EACN,MAAM,EACN,GAAG,EACH,MAAM,EACN,MAAM,EACN,aAAa,GACd,EAAE,4BAA4B,GAAG,MAAM,CAiBvC;AAED,MAAM,WAAW,iCACf,SAAQ,wBAAwB;IAChC,aAAa,EAAE,YAAY,CAAC;CAC7B;AAED,wBAAgB,aAAa,CAC3B,OAAO,EAAE,iCAAiC,GACzC,MAAM,CAwBR;AAED,wBAAgB,oBAAoB,CAAC,OAAO,EAAE,WAAW,GAAG,UAAU,CAgCrE;AAED,wBAAgB,kBAAkB,CAAC,OAAO,EAAE,SAAS,GAAG,UAAU,CAyBjE;AAED,wBAAgB,YAAY,CAC1B,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,MAAM,EACb,MAAM,EAAE,MAAM,GACb,MAAM,CAQR;AAED,wBAAgB,yBAAyB,CAAC,IAAI,EAAE,kBAAkB,GAAG,MAAM,EAAE,CAE5E;AAED,wBAAgB,qBAAqB,CACnC,SAAS,SAAS,kBAAkB,EACpC,UAAU,SAAS,kBAAkB,EAErC,GAAG,EACC,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,GACpC,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,GAC1C,MAAM,EAAE,CAmBV;AAED,wBAAgB,WAAW,CACzB,GAAG,EAAE,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,WAAW,GAAG,YAAY,GAAG,WAAW,GAAG,QAAQ,GAC1E,MAAM,EAAE,CAIV;AAED,wBAAgB,cAAc,IAAI,iCAAiC,CAgClE;AAED,MAAM,WAAW,UAAU;IACzB,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;CACpB;AACD,MAAM,WAAW,MAAM;IACrB,KAAK,EAAE,MAAM,EAAE,CAAC;CACjB;AAED,wBAAgB,KAAK,CACnB,CAAC,SACG,UAAU,GACV,MAAM,GACN,SAAS,GACT,WAAW,GACX,gBAAgB,GAChB,WAAW,GACX,gBAAgB,GAChB,YAAY,GACZ,WAAW,GACX,GAAG,GACH,QAAQ,GACR,GAAG,GACH,IAAI,GACJ,aAAa,CAAC,SAAS,EAAE,UAAU,CAAC,GACpC,kBAAkB,GAClB,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,EAC3C,SAAS,SAAS,kBAAkB,EACpC,UAAU,SAAS,kBAAkB,EAErC,GAAG,EAAE,CAAC,EACN,OAAO,CAAC,EACJ,OAAO,CAAC,iCAAiC,CAAC,GAC1C,OAAO,CAAC,wBAAwB,CAAC,GACpC,MAAM,CAqGR;AAED,wBAAgB,UAAU,CACxB,KAAK,EAAE,MAAM,EAAE,EACf,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,GAAG,SAAS,GAClC,MAAM,EAAE,CAWV"}