"use strict";

var _class_apply_descriptor_destructure = require("./_class_apply_descriptor_destructure.cjs");
var _class_check_private_static_access = require("./_class_check_private_static_access.cjs");
var _class_check_private_static_field_descriptor = require("./_class_check_private_static_field_descriptor.cjs");

function _class_static_private_field_destructure(receiver, classConstructor, descriptor) {
    _class_check_private_static_access._(receiver, classConstructor);
    _class_check_private_static_field_descriptor._(descriptor, "set");

    return _class_apply_descriptor_destructure._(receiver, descriptor);
}
exports._ = _class_static_private_field_destructure;
