/**
 * Returns an array of given size, full of randomness
 */
export declare function randos(size: number, std?: number | null): Float32Array;
/**
 * Returns a 2D matrix of given size, full of randomness
 */
export declare function randos2D(width: number, height: number, std?: number | null): Float32Array[];
/**
 * Returns a 3D tensor of given size, full of randomness
 */
export declare function randos3D(width: number, height: number, depth: number, std?: number | null): Float32Array[][];
//# sourceMappingURL=randos.d.ts.map