"use strict";
exports.km = void 0;
var _index = require("./km/_lib/formatDistance.cjs");
var _index2 = require("./km/_lib/formatLong.cjs");
var _index3 = require("./km/_lib/formatRelative.cjs");
var _index4 = require("./km/_lib/localize.cjs");
var _index5 = require("./km/_lib/match.cjs");

/**
 * @category Locales
 * @summary Khmer locale (Cambodian).
 * @language Khmer
 * @iso-639-2 khm
 * <AUTHOR> [@seanghay](https://github.com/seanghay)
 */
const km = (exports.km = {
  code: "km",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
