/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { env } from './environment';
import * as util from './util';
export class Profiler {
    constructor(backendTimer, logger) {
        this.backendTimer = backendTimer;
        this.logger = logger;
        if (logger == null) {
            this.logger = new Logger();
        }
    }
    profileKernel(kernelName, inputs, f) {
        let outputs;
        const holdResultWrapperFn = () => {
            outputs = f();
        };
        let timer;
        const start = util.now();
        if (this.backendTimer.timerAvailable()) {
            timer = this.backendTimer.time(holdResultWrapperFn);
        }
        else {
            holdResultWrapperFn();
            for (const output of outputs) {
                output.dataSync();
            }
            timer = Promise.resolve({ kernelMs: util.now() - start });
        }
        if (env().getBool('CHECK_COMPUTATION_FOR_ERRORS')) {
            for (let i = 0; i < outputs.length; i++) {
                const output = outputs[i];
                // Dangling promise here because we don't want to propagate up
                // asynchronicity.
                output.data().then(tensorVals => {
                    checkComputationForErrors(tensorVals, output.dtype, kernelName);
                });
            }
        }
        const kernelProfile = {
            kernelName,
            outputs,
            inputs,
            timeMs: timer.then(timing => timing.kernelMs),
            extraInfo: timer.then(timing => timing.getExtraProfileInfo != null ?
                timing.getExtraProfileInfo() :
                '')
        };
        return kernelProfile;
    }
    logKernelProfile(kernelProfile) {
        const { kernelName, outputs, timeMs, inputs, extraInfo } = kernelProfile;
        outputs.forEach(result => {
            Promise.all([result.data(), timeMs, extraInfo]).then(valueContainer => {
                this.logger.logKernelProfile(kernelName, result, valueContainer[0], valueContainer[1], inputs, valueContainer[2]);
            });
        });
    }
}
export function checkComputationForErrors(vals, dtype, kernelName) {
    if (dtype !== 'float32') {
        // Only floating point computations will generate NaN values
        return false;
    }
    for (let i = 0; i < vals.length; i++) {
        const num = vals[i];
        if (isNaN(num) || !isFinite(num)) {
            // Throwing custom exception so behavior is testable.
            console.warn(`Found ${num} in the result of '${kernelName}'`);
            return true;
        }
    }
    return false;
}
export class Logger {
    logKernelProfile(name, result, vals, timeMs, inputs, extraInfo) {
        const time = typeof timeMs === 'number' ? util.rightPad(`${timeMs}ms`, 9) :
            timeMs['error'];
        const paddedName = util.rightPad(name, 25);
        const rank = result.rank;
        const size = result.size;
        const shape = util.rightPad(result.shape.toString(), 14);
        let inputShapesDescription = '';
        for (const name in inputs) {
            const input = inputs[name];
            if (input != null) {
                // The input might be a non-tensor (e.g HTMLImageElement), in which case
                // we claim the output shape as input shape.
                const inputShape = input.shape || result.shape;
                const inputRank = inputShape.length;
                inputShapesDescription +=
                    `${name}: ${inputRank}D ${inputRank > 0 ? inputShape : ''} `;
            }
        }
        console.log(`%c${paddedName}\t%c${time}\t%c${rank}D ${shape}\t%c${size}\t%c${inputShapesDescription}\t%c${extraInfo}`, 'font-weight:bold', 'color:red', 'color:blue', 'color: orange', 'color: green', 'color: steelblue');
    }
}
//# sourceMappingURL=data:application/json;base64,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