import { pool } from '../../ops/pool';
import { getGlobalTensorClass } from '../../tensor';
getGlobalTensorClass().prototype.pool = function (windowShape, poolingType, padding, dilationRate, strides, dimRoundingMode) {
    this.throwIfDisposed();
    return pool(this, windowShape, poolingType, padding, dilationRate, strides, dimRoundingMode);
};
//# sourceMappingURL=data:application/json;base64,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