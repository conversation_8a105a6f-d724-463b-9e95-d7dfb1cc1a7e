/// <amd-module name="@tensorflow/tfjs-core/dist/ops/add_n" />
import { Tensor } from '../tensor';
import { TensorLike } from '../types';
/**
 * Adds a list of `tf.Tensor`s element-wise, each with the same shape and dtype.
 *
 * ```js
 * const a = tf.tensor1d([1, 2]);
 * const b = tf.tensor1d([3, 4]);
 * const c = tf.tensor1d([5, 6]);
 *
 * tf.addN([a, b, c]).print();
 * ```
 * @param tensors A list of tensors with the same shape and dtype.
 * @doc {heading: 'Operations', subheading: 'Arithmetic'}
 */
declare function addN_<T extends Tensor>(tensors: Array<T | TensorLike>): T;
export declare const addN: typeof addN_;
export {};
