/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { separableConv2d } from '../../ops/separable_conv2d';
import { getGlobalTensorClass } from '../../tensor';
getGlobalTensorClass().prototype.separableConv2d =
    function (depthwiseFilter, pointwiseFilter, strides, pad, dilation, dataFormat) {
        this.throwIfDisposed();
        return separableConv2d(this, depthwiseFilter, pointwiseFilter, strides, pad, dilation, dataFormat);
    };
//# sourceMappingURL=data:application/json;base64,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