{"version": 3, "file": "resolvers.js", "sources": ["../src/validateFieldsNatively.ts", "../src/toNestErrors.ts"], "sourcesContent": ["import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Ref,\n  ResolverOptions,\n  get,\n} from 'react-hook-form';\n\nconst setCustomValidity = (\n  ref: Ref,\n  fieldPath: string,\n  errors: FieldErrors,\n) => {\n  if (ref && 'reportValidity' in ref) {\n    const error = get(errors, fieldPath) as FieldError | undefined;\n    ref.setCustomValidity((error && error.message) || '');\n\n    ref.reportValidity();\n  }\n};\n\n// Native validation (web only)\nexport const validateFieldsNatively = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): void => {\n  for (const fieldPath in options.fields) {\n    const field = options.fields[fieldPath];\n    if (field && field.ref && 'reportValidity' in field.ref) {\n      setCustomValidity(field.ref, fieldPath, errors);\n    } else if (field.refs) {\n      field.refs.forEach((ref: HTMLInputElement) =>\n        setCustomValidity(ref, fieldPath, errors),\n      );\n    }\n  }\n};\n", "import {\n  Field,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n  ResolverOptions,\n  get,\n  set,\n} from 'react-hook-form';\nimport { validateFieldsNatively } from './validateFieldsNatively';\n\nexport const toNestErrors = <TFieldValues extends FieldValues>(\n  errors: FieldErrors,\n  options: ResolverOptions<TFieldValues>,\n): FieldErrors<TFieldValues> => {\n  options.shouldUseNativeValidation && validateFieldsNatively(errors, options);\n\n  const fieldErrors = {} as FieldErrors<TFieldValues>;\n  for (const path in errors) {\n    const field = get(options.fields, path) as Field['_f'] | undefined;\n    const error = Object.assign(errors[path] || {}, {\n      ref: field && field.ref,\n    });\n\n    if (isNameInFieldArray(options.names || Object.keys(errors), path)) {\n      const fieldArrayErrors = Object.assign({}, get(fieldErrors, path));\n\n      set(fieldArrayErrors, 'root', error);\n      set(fieldErrors, path, fieldArrayErrors);\n    } else {\n      set(fieldErrors, path, error);\n    }\n  }\n\n  return fieldErrors;\n};\n\nconst isNameInFieldArray = (\n  names: InternalFieldName[],\n  name: InternalFieldName,\n) => names.some((n) => n.startsWith(name + '.'));\n"], "names": ["setCustomValidity", "ref", "fieldPath", "errors", "error", "get", "message", "reportValidity", "validateFieldsNatively", "options", "_loop", "field", "fields", "refs", "for<PERSON>ach", "isNameInFieldArray", "names", "name", "some", "n", "startsWith", "shouldUseNativeValidation", "fieldErrors", "path", "Object", "assign", "keys", "fieldArrayErrors", "set"], "mappings": "iCASMA,EAAoB,SACxBC,EACAC,EACAC,GAEA,GAAIF,GAAO,mBAAoBA,EAAK,CAClC,IAAMG,EAAQC,MAAIF,EAAQD,GAC1BD,EAAID,kBAAmBI,GAASA,EAAME,SAAY,IAElDL,EAAIM,gBACN,CACF,EAGaC,EAAyB,SACpCL,EACAM,GACQ,IAAAC,EAAAA,SAAAR,GAEN,IAAMS,EAAQF,EAAQG,OAAOV,GACzBS,GAASA,EAAMV,KAAO,mBAAoBU,EAAMV,IAClDD,EAAkBW,EAAMV,IAAKC,EAAWC,GAC/BQ,EAAME,MACfF,EAAME,KAAKC,QAAQ,SAACb,GAAqB,OACvCD,EAAkBC,EAAKC,EAAWC,EAAO,EAG/C,EATA,IAAK,IAAMD,KAAaO,EAAQG,OAAMF,EAAAR,EAUxC,ECAMa,EAAqB,SACzBC,EACAC,GAAuB,OACpBD,EAAME,KAAK,SAACC,GAAM,OAAAA,EAAEC,WAAWH,EAAO,IAAI,EAAC,uBA7BpB,SAC1Bd,EACAM,GAEAA,EAAQY,2BAA6Bb,EAAuBL,EAAQM,GAEpE,IAAMa,EAAc,GACpB,IAAK,IAAMC,KAAQpB,EAAQ,CACzB,IAAMQ,EAAQN,EAAGA,IAACI,EAAQG,OAAQW,GAC5BnB,EAAQoB,OAAOC,OAAOtB,EAAOoB,IAAS,CAAE,EAAE,CAC9CtB,IAAKU,GAASA,EAAMV,MAGtB,GAAIc,EAAmBN,EAAQO,OAASQ,OAAOE,KAAKvB,GAASoB,GAAO,CAClE,IAAMI,EAAmBH,OAAOC,OAAO,CAAA,EAAIpB,EAAGA,IAACiB,EAAaC,IAE5DK,EAAGA,IAACD,EAAkB,OAAQvB,GAC9BwB,EAAGA,IAACN,EAAaC,EAAMI,EACzB,MACEC,EAAGA,IAACN,EAAaC,EAAMnB,EAE3B,CAEA,OAAOkB,CACT"}