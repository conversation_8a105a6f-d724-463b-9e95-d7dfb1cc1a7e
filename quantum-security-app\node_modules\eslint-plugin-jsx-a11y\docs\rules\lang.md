# jsx-a11y/lang

<!-- end auto-generated rule header -->

The `lang` prop on the `<html>` element must be a valid IETF's BCP 47 language tag.

## Rule details

This rule takes no arguments.

### Succeed

```jsx
<html lang="en">
<html lang="en-US">
```

### Fail

```jsx
<html>
<html lang="foo">
```

## Accessibility guidelines
- [WCAG 3.1.1](https://www.w3.org/WAI/WCAG21/Understanding/language-of-page)

### Resources
- [axe-core, valid-lang](https://dequeuniversity.com/rules/axe/3.2/valid-lang)
- [Language tags in HTML and XML](https://www.w3.org/International/articles/language-tags/)
- [IANA Language Subtag Registry](https://www.iana.org/assignments/language-subtag-registry/language-subtag-registry)
