{"version": 3, "file": "recurrent.d.ts", "sourceRoot": "", "sources": ["../src/recurrent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACnE,OAAO,EACL,eAAe,EAGf,MAAM,EAEP,MAAM,SAAS,CAAC;AAcjB,OAAO,EACL,WAAW,EACX,mBAAmB,EACnB,2BAA2B,EAC3B,eAAe,EAChB,MAAM,gBAAgB,CAAC;AAExB,OAAO,EAAE,YAAY,EAAW,kBAAkB,EAAE,MAAM,QAAQ,CAAC;AAOnE,MAAM,WAAW,yBACf,SAAQ,2BAA2B;CAAG;AAIxC,MAAM,WAAW,iBAAkB,SAAQ,mBAAmB;IAC5D,YAAY,EAAE,KAAK,CACjB,CACE,UAAU,EAAE,MAAM,EAClB,cAAc,EAAE,eAAe,EAC/B,KAAK,EAAE,MAAM,KACV,MAAM,CACZ,CAAC;CACH;AAED,MAAM,WAAW,6BAA6B,CAAC,CAAC;IAC9C,MAAM,EAAE,eAAe,CAAC;IACxB,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;IACpB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,qBAAa,SAAS,CACpB,CAAC,SAAS,kBAAkB,GAAG,kBAAkB,CACjD,SAAQ,WAAW;IACnB,SAAS,EAAE,yBAAyB,CAAM;IAG1C,OAAO,EAAE,iBAAiB,CAAC;IAC3B,iBAAiB,EAAE,mBAAmB,GAAG,IAAI,CAAQ;IACrD,UAAU,EAAE,MAAM,EAAE,EAAE,CAAM;IAC5B,yBAAyB,EAAE,MAAM,EAAE,CAAM;IACzC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAQ;gBAI7B,OAAO,GAAE,OAAO,CAAC,iBAAiB,GAAG,yBAAyB,CAAM;IAOtE,cAAc,IAAI;QAChB,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,MAAM,EAAE,CAAC;QACvB,WAAW,EAAE,MAAM,CAAC;KACrB;IAoBD,kBAAkB,IAAI,MAAM,EAAE;IAsF9B,oBAAoB,CAAC,aAAa,EAAE,MAAM,GAAG,MAAM,EAAE;IAmBrD,UAAU,IAAI,IAAI;IAuBlB,cAAc,IAAI,IAAI;IAYtB,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE;IAWrB,QAAQ,CAAC,KAAK,EAAE,YAAY,GAAG,YAAY;IAI3C,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,YAAY;IAoBpC,KAAK,CACH,IAAI,EAAE,CAAC,EAAE,EAAE,EACX,OAAO,GAAE,OAAO,CAAC,yBAAyB,CAAM,GAC/C,eAAe;IAiBlB,GAAG,IAAI,IAAI;IAWX,YAAY,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE;IAMzC,aAAa,CACX,IAAI,EAAE,CAAC,EAAE,EAAE,EACX,OAAO,EAAE,OAAO,CAAC,yBAAyB,CAAC,GAC1C,6BAA6B,CAAC,CAAC,CAAC;IAsBnC,uBAAuB,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,MAAM;IAwB5C,UAAU,CAAC,IAAI,EAAE,YAAY,GAAG,YAAY;IAM5C,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,IAAI;IAgBnC,aAAa,IAAI,IAAI;IASrB,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,IAAI;IAQjC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,YAAY,EAAE,OAAO,GAAG,YAAY,GAAG,IAAI;CA2BvE"}