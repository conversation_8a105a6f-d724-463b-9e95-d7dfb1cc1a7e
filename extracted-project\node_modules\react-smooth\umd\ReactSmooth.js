/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/Animate.js":
/*!************************!*\
  !*** ./src/Animate.js ***!
  \************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ \"./node_modules/prop-types/index.js\"));\nvar _fastEquals = __webpack_require__(/*! fast-equals */ \"./node_modules/fast-equals/dist/cjs/index.cjs\");\nvar _AnimateManager = _interopRequireDefault(__webpack_require__(/*! ./AnimateManager */ \"./src/AnimateManager.js\"));\nvar _easing = __webpack_require__(/*! ./easing */ \"./src/easing.js\");\nvar _configUpdate = _interopRequireDefault(__webpack_require__(/*! ./configUpdate */ \"./src/configUpdate.js\"));\nvar _util = __webpack_require__(/*! ./util */ \"./src/util.js\");\nvar _excluded = [\"children\", \"begin\", \"duration\", \"attributeName\", \"easing\", \"isActive\", \"steps\", \"from\", \"to\", \"canBegin\", \"onAnimationEnd\", \"shouldReAnimate\", \"onAnimationReStart\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nvar Animate = /*#__PURE__*/function (_PureComponent) {\n  _inherits(Animate, _PureComponent);\n  var _super = _createSuper(Animate);\n  function Animate(props, context) {\n    var _this;\n    _classCallCheck(this, Animate);\n    _this = _super.call(this, props, context);\n    var _this$props = _this.props,\n      isActive = _this$props.isActive,\n      attributeName = _this$props.attributeName,\n      from = _this$props.from,\n      to = _this$props.to,\n      steps = _this$props.steps,\n      children = _this$props.children,\n      duration = _this$props.duration;\n    _this.handleStyleChange = _this.handleStyleChange.bind(_assertThisInitialized(_this));\n    _this.changeStyle = _this.changeStyle.bind(_assertThisInitialized(_this));\n    if (!isActive || duration <= 0) {\n      _this.state = {\n        style: {}\n      };\n\n      // if children is a function and animation is not active, set style to 'to'\n      if (typeof children === 'function') {\n        _this.state = {\n          style: to\n        };\n      }\n      return _possibleConstructorReturn(_this);\n    }\n    if (steps && steps.length) {\n      _this.state = {\n        style: steps[0].style\n      };\n    } else if (from) {\n      if (typeof children === 'function') {\n        _this.state = {\n          style: from\n        };\n        return _possibleConstructorReturn(_this);\n      }\n      _this.state = {\n        style: attributeName ? _defineProperty({}, attributeName, from) : from\n      };\n    } else {\n      _this.state = {\n        style: {}\n      };\n    }\n    return _this;\n  }\n  _createClass(Animate, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      var _this$props2 = this.props,\n        isActive = _this$props2.isActive,\n        canBegin = _this$props2.canBegin;\n      this.mounted = true;\n      if (!isActive || !canBegin) {\n        return;\n      }\n      this.runAnimation(this.props);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var _this$props3 = this.props,\n        isActive = _this$props3.isActive,\n        canBegin = _this$props3.canBegin,\n        attributeName = _this$props3.attributeName,\n        shouldReAnimate = _this$props3.shouldReAnimate,\n        to = _this$props3.to,\n        currentFrom = _this$props3.from;\n      var style = this.state.style;\n      if (!canBegin) {\n        return;\n      }\n      if (!isActive) {\n        var newState = {\n          style: attributeName ? _defineProperty({}, attributeName, to) : to\n        };\n        if (this.state && style) {\n          if (attributeName && style[attributeName] !== to || !attributeName && style !== to) {\n            // eslint-disable-next-line react/no-did-update-set-state\n            this.setState(newState);\n          }\n        }\n        return;\n      }\n      if ((0, _fastEquals.deepEqual)(prevProps.to, to) && prevProps.canBegin && prevProps.isActive) {\n        return;\n      }\n      var isTriggered = !prevProps.canBegin || !prevProps.isActive;\n      if (this.manager) {\n        this.manager.stop();\n      }\n      if (this.stopJSAnimation) {\n        this.stopJSAnimation();\n      }\n      var from = isTriggered || shouldReAnimate ? currentFrom : prevProps.to;\n      if (this.state && style) {\n        var _newState = {\n          style: attributeName ? _defineProperty({}, attributeName, from) : from\n        };\n        if (attributeName && style[attributeName] !== from || !attributeName && style !== from) {\n          // eslint-disable-next-line react/no-did-update-set-state\n          this.setState(_newState);\n        }\n      }\n      this.runAnimation(_objectSpread(_objectSpread({}, this.props), {}, {\n        from: from,\n        begin: 0\n      }));\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this.mounted = false;\n      var onAnimationEnd = this.props.onAnimationEnd;\n      if (this.unSubscribe) {\n        this.unSubscribe();\n      }\n      if (this.manager) {\n        this.manager.stop();\n        this.manager = null;\n      }\n      if (this.stopJSAnimation) {\n        this.stopJSAnimation();\n      }\n      if (onAnimationEnd) {\n        onAnimationEnd();\n      }\n    }\n  }, {\n    key: \"handleStyleChange\",\n    value: function handleStyleChange(style) {\n      this.changeStyle(style);\n    }\n  }, {\n    key: \"changeStyle\",\n    value: function changeStyle(style) {\n      if (this.mounted) {\n        this.setState({\n          style: style\n        });\n      }\n    }\n  }, {\n    key: \"runJSAnimation\",\n    value: function runJSAnimation(props) {\n      var _this2 = this;\n      var from = props.from,\n        to = props.to,\n        duration = props.duration,\n        easing = props.easing,\n        begin = props.begin,\n        onAnimationEnd = props.onAnimationEnd,\n        onAnimationStart = props.onAnimationStart;\n      var startAnimation = (0, _configUpdate.default)(from, to, (0, _easing.configEasing)(easing), duration, this.changeStyle);\n      var finalStartAnimation = function finalStartAnimation() {\n        _this2.stopJSAnimation = startAnimation();\n      };\n      this.manager.start([onAnimationStart, begin, finalStartAnimation, duration, onAnimationEnd]);\n    }\n  }, {\n    key: \"runStepAnimation\",\n    value: function runStepAnimation(props) {\n      var _this3 = this;\n      var steps = props.steps,\n        begin = props.begin,\n        onAnimationStart = props.onAnimationStart;\n      var _steps$ = steps[0],\n        initialStyle = _steps$.style,\n        _steps$$duration = _steps$.duration,\n        initialTime = _steps$$duration === void 0 ? 0 : _steps$$duration;\n      var addStyle = function addStyle(sequence, nextItem, index) {\n        if (index === 0) {\n          return sequence;\n        }\n        var duration = nextItem.duration,\n          _nextItem$easing = nextItem.easing,\n          easing = _nextItem$easing === void 0 ? 'ease' : _nextItem$easing,\n          style = nextItem.style,\n          nextProperties = nextItem.properties,\n          onAnimationEnd = nextItem.onAnimationEnd;\n        var preItem = index > 0 ? steps[index - 1] : nextItem;\n        var properties = nextProperties || Object.keys(style);\n        if (typeof easing === 'function' || easing === 'spring') {\n          return [].concat(_toConsumableArray(sequence), [_this3.runJSAnimation.bind(_this3, {\n            from: preItem.style,\n            to: style,\n            duration: duration,\n            easing: easing\n          }), duration]);\n        }\n        var transition = (0, _util.getTransitionVal)(properties, duration, easing);\n        var newStyle = _objectSpread(_objectSpread(_objectSpread({}, preItem.style), style), {}, {\n          transition: transition\n        });\n        return [].concat(_toConsumableArray(sequence), [newStyle, duration, onAnimationEnd]).filter(_util.identity);\n      };\n      return this.manager.start([onAnimationStart].concat(_toConsumableArray(steps.reduce(addStyle, [initialStyle, Math.max(initialTime, begin)])), [props.onAnimationEnd]));\n    }\n  }, {\n    key: \"runAnimation\",\n    value: function runAnimation(props) {\n      if (!this.manager) {\n        this.manager = (0, _AnimateManager.default)();\n      }\n      var begin = props.begin,\n        duration = props.duration,\n        attributeName = props.attributeName,\n        propsTo = props.to,\n        easing = props.easing,\n        onAnimationStart = props.onAnimationStart,\n        onAnimationEnd = props.onAnimationEnd,\n        steps = props.steps,\n        children = props.children;\n      var manager = this.manager;\n      this.unSubscribe = manager.subscribe(this.handleStyleChange);\n      if (typeof easing === 'function' || typeof children === 'function' || easing === 'spring') {\n        this.runJSAnimation(props);\n        return;\n      }\n      if (steps.length > 1) {\n        this.runStepAnimation(props);\n        return;\n      }\n      var to = attributeName ? _defineProperty({}, attributeName, propsTo) : propsTo;\n      var transition = (0, _util.getTransitionVal)(Object.keys(to), duration, easing);\n      manager.start([onAnimationStart, begin, _objectSpread(_objectSpread({}, to), {}, {\n        transition: transition\n      }), duration, onAnimationEnd]);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        children = _this$props4.children,\n        begin = _this$props4.begin,\n        duration = _this$props4.duration,\n        attributeName = _this$props4.attributeName,\n        easing = _this$props4.easing,\n        isActive = _this$props4.isActive,\n        steps = _this$props4.steps,\n        from = _this$props4.from,\n        to = _this$props4.to,\n        canBegin = _this$props4.canBegin,\n        onAnimationEnd = _this$props4.onAnimationEnd,\n        shouldReAnimate = _this$props4.shouldReAnimate,\n        onAnimationReStart = _this$props4.onAnimationReStart,\n        others = _objectWithoutProperties(_this$props4, _excluded);\n      var count = _react.Children.count(children);\n      // eslint-disable-next-line react/destructuring-assignment\n      var stateStyle = this.state.style;\n      if (typeof children === 'function') {\n        return children(stateStyle);\n      }\n      if (!isActive || count === 0 || duration <= 0) {\n        return children;\n      }\n      var cloneContainer = function cloneContainer(container) {\n        var _container$props = container.props,\n          _container$props$styl = _container$props.style,\n          style = _container$props$styl === void 0 ? {} : _container$props$styl,\n          className = _container$props.className;\n        var res = /*#__PURE__*/(0, _react.cloneElement)(container, _objectSpread(_objectSpread({}, others), {}, {\n          style: _objectSpread(_objectSpread({}, style), stateStyle),\n          className: className\n        }));\n        return res;\n      };\n      if (count === 1) {\n        return cloneContainer(_react.Children.only(children));\n      }\n      return /*#__PURE__*/_react.default.createElement(\"div\", null, _react.Children.map(children, function (child) {\n        return cloneContainer(child);\n      }));\n    }\n  }]);\n  return Animate;\n}(_react.PureComponent);\nAnimate.displayName = 'Animate';\nAnimate.defaultProps = {\n  begin: 0,\n  duration: 1000,\n  from: '',\n  to: '',\n  attributeName: '',\n  easing: 'ease',\n  isActive: true,\n  canBegin: true,\n  steps: [],\n  onAnimationEnd: function onAnimationEnd() {},\n  onAnimationStart: function onAnimationStart() {}\n};\nAnimate.propTypes = {\n  from: _propTypes.default.oneOfType([_propTypes.default.object, _propTypes.default.string]),\n  to: _propTypes.default.oneOfType([_propTypes.default.object, _propTypes.default.string]),\n  attributeName: _propTypes.default.string,\n  // animation duration\n  duration: _propTypes.default.number,\n  begin: _propTypes.default.number,\n  easing: _propTypes.default.oneOfType([_propTypes.default.string, _propTypes.default.func]),\n  steps: _propTypes.default.arrayOf(_propTypes.default.shape({\n    duration: _propTypes.default.number.isRequired,\n    style: _propTypes.default.object.isRequired,\n    easing: _propTypes.default.oneOfType([_propTypes.default.oneOf(['ease', 'ease-in', 'ease-out', 'ease-in-out', 'linear']), _propTypes.default.func]),\n    // transition css properties(dash case), optional\n    properties: _propTypes.default.arrayOf('string'),\n    onAnimationEnd: _propTypes.default.func\n  })),\n  children: _propTypes.default.oneOfType([_propTypes.default.node, _propTypes.default.func]),\n  isActive: _propTypes.default.bool,\n  canBegin: _propTypes.default.bool,\n  onAnimationEnd: _propTypes.default.func,\n  // decide if it should reanimate with initial from style when props change\n  shouldReAnimate: _propTypes.default.bool,\n  onAnimationStart: _propTypes.default.func,\n  onAnimationReStart: _propTypes.default.func\n};\nvar _default = exports[\"default\"] = Animate;\n\n//# sourceURL=webpack://react-smooth/./src/Animate.js?");

/***/ }),

/***/ "./src/AnimateGroup.js":
/*!*****************************!*\
  !*** ./src/AnimateGroup.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _reactTransitionGroup = __webpack_require__(/*! react-transition-group */ \"react-transition-group\");\nvar _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ \"./node_modules/prop-types/index.js\"));\nvar _AnimateGroupChild = _interopRequireDefault(__webpack_require__(/*! ./AnimateGroupChild */ \"./src/AnimateGroupChild.js\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction AnimateGroup(props) {\n  var component = props.component,\n    children = props.children,\n    appear = props.appear,\n    enter = props.enter,\n    leave = props.leave;\n  return /*#__PURE__*/_react.default.createElement(_reactTransitionGroup.TransitionGroup, {\n    component: component\n  }, _react.Children.map(children, function (child, index) {\n    return /*#__PURE__*/_react.default.createElement(_AnimateGroupChild.default, {\n      appearOptions: appear,\n      enterOptions: enter,\n      leaveOptions: leave,\n      key: \"child-\".concat(index) // eslint-disable-line\n    }, child);\n  }));\n}\nAnimateGroup.propTypes = {\n  appear: _propTypes.default.object,\n  enter: _propTypes.default.object,\n  leave: _propTypes.default.object,\n  children: _propTypes.default.oneOfType([_propTypes.default.array, _propTypes.default.element]),\n  component: _propTypes.default.any\n};\nAnimateGroup.defaultProps = {\n  component: 'span'\n};\nvar _default = exports[\"default\"] = AnimateGroup;\n\n//# sourceURL=webpack://react-smooth/./src/AnimateGroup.js?");

/***/ }),

/***/ "./src/AnimateGroupChild.js":
/*!**********************************!*\
  !*** ./src/AnimateGroupChild.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _reactTransitionGroup = __webpack_require__(/*! react-transition-group */ \"react-transition-group\");\nvar _propTypes = _interopRequireDefault(__webpack_require__(/*! prop-types */ \"./node_modules/prop-types/index.js\"));\nvar _Animate = _interopRequireDefault(__webpack_require__(/*! ./Animate */ \"./src/Animate.js\"));\nvar _excluded = [\"children\", \"appearOptions\", \"enterOptions\", \"leaveOptions\"];\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(e) { if (\"function\" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }\nfunction _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if (\"default\" !== u && Object.prototype.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nvar parseDurationOfSingleTransition = function parseDurationOfSingleTransition() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var steps = options.steps,\n    duration = options.duration;\n  if (steps && steps.length) {\n    return steps.reduce(function (result, entry) {\n      return result + (Number.isFinite(entry.duration) && entry.duration > 0 ? entry.duration : 0);\n    }, 0);\n  }\n  if (Number.isFinite(duration)) {\n    return duration;\n  }\n  return 0;\n};\nvar AnimateGroupChild = /*#__PURE__*/function (_Component) {\n  _inherits(AnimateGroupChild, _Component);\n  var _super = _createSuper(AnimateGroupChild);\n  function AnimateGroupChild() {\n    var _this;\n    _classCallCheck(this, AnimateGroupChild);\n    _this = _super.call(this);\n    _defineProperty(_assertThisInitialized(_this), \"handleEnter\", function (node, isAppearing) {\n      var _this$props = _this.props,\n        appearOptions = _this$props.appearOptions,\n        enterOptions = _this$props.enterOptions;\n      _this.handleStyleActive(isAppearing ? appearOptions : enterOptions);\n    });\n    _defineProperty(_assertThisInitialized(_this), \"handleExit\", function () {\n      var leaveOptions = _this.props.leaveOptions;\n      _this.handleStyleActive(leaveOptions);\n    });\n    _this.state = {\n      isActive: false\n    };\n    return _this;\n  }\n  _createClass(AnimateGroupChild, [{\n    key: \"handleStyleActive\",\n    value: function handleStyleActive(style) {\n      if (style) {\n        var onAnimationEnd = style.onAnimationEnd ? function () {\n          style.onAnimationEnd();\n        } : null;\n        this.setState(_objectSpread(_objectSpread({}, style), {}, {\n          onAnimationEnd: onAnimationEnd,\n          isActive: true\n        }));\n      }\n    }\n  }, {\n    key: \"parseTimeout\",\n    value: function parseTimeout() {\n      var _this$props2 = this.props,\n        appearOptions = _this$props2.appearOptions,\n        enterOptions = _this$props2.enterOptions,\n        leaveOptions = _this$props2.leaveOptions;\n      return parseDurationOfSingleTransition(appearOptions) + parseDurationOfSingleTransition(enterOptions) + parseDurationOfSingleTransition(leaveOptions);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this2 = this;\n      var _this$props3 = this.props,\n        children = _this$props3.children,\n        appearOptions = _this$props3.appearOptions,\n        enterOptions = _this$props3.enterOptions,\n        leaveOptions = _this$props3.leaveOptions,\n        props = _objectWithoutProperties(_this$props3, _excluded);\n      return /*#__PURE__*/_react.default.createElement(_reactTransitionGroup.Transition, _extends({}, props, {\n        onEnter: this.handleEnter,\n        onExit: this.handleExit,\n        timeout: this.parseTimeout()\n      }), function () {\n        return /*#__PURE__*/_react.default.createElement(_Animate.default, _this2.state, _react.Children.only(children));\n      });\n    }\n  }]);\n  return AnimateGroupChild;\n}(_react.Component);\nAnimateGroupChild.propTypes = {\n  appearOptions: _propTypes.default.object,\n  enterOptions: _propTypes.default.object,\n  leaveOptions: _propTypes.default.object,\n  children: _propTypes.default.element\n};\nvar _default = exports[\"default\"] = AnimateGroupChild;\n\n//# sourceURL=webpack://react-smooth/./src/AnimateGroupChild.js?");

/***/ }),

/***/ "./src/AnimateManager.js":
/*!*******************************!*\
  !*** ./src/AnimateManager.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = createAnimateManager;\nvar _setRafTimeout = _interopRequireDefault(__webpack_require__(/*! ./setRafTimeout */ \"./src/setRafTimeout.js\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toArray(arr) { return _arrayWithHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction createAnimateManager() {\n  var currStyle = {};\n  var handleChange = function handleChange() {\n    return null;\n  };\n  var shouldStop = false;\n  var setStyle = function setStyle(_style) {\n    if (shouldStop) {\n      return;\n    }\n    if (Array.isArray(_style)) {\n      if (!_style.length) {\n        return;\n      }\n      var styles = _style;\n      var _styles = _toArray(styles),\n        curr = _styles[0],\n        restStyles = _styles.slice(1);\n      if (typeof curr === 'number') {\n        (0, _setRafTimeout.default)(setStyle.bind(null, restStyles), curr);\n        return;\n      }\n      setStyle(curr);\n      (0, _setRafTimeout.default)(setStyle.bind(null, restStyles));\n      return;\n    }\n    if (_typeof(_style) === 'object') {\n      currStyle = _style;\n      handleChange(currStyle);\n    }\n    if (typeof _style === 'function') {\n      _style();\n    }\n  };\n  return {\n    stop: function stop() {\n      shouldStop = true;\n    },\n    start: function start(style) {\n      shouldStop = false;\n      setStyle(style);\n    },\n    subscribe: function subscribe(_handleChange) {\n      handleChange = _handleChange;\n      return function () {\n        handleChange = function handleChange() {\n          return null;\n        };\n      };\n    }\n  };\n}\n\n//# sourceURL=webpack://react-smooth/./src/AnimateManager.js?");

/***/ }),

/***/ "./src/configUpdate.js":
/*!*****************************!*\
  !*** ./src/configUpdate.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = void 0;\nvar _util = __webpack_require__(/*! ./util */ \"./src/util.js\");\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nvar alpha = function alpha(begin, end, k) {\n  return begin + (end - begin) * k;\n};\nvar needContinue = function needContinue(_ref) {\n  var from = _ref.from,\n    to = _ref.to;\n  return from !== to;\n};\n\n/*\n * @description: cal new from value and velocity in each stepper\n * @return: { [styleProperty]: { from, to, velocity } }\n */\nvar calStepperVals = function calStepperVals(easing, preVals, steps) {\n  var nextStepVals = (0, _util.mapObject)(function (key, val) {\n    if (needContinue(val)) {\n      var _easing = easing(val.from, val.to, val.velocity),\n        _easing2 = _slicedToArray(_easing, 2),\n        newX = _easing2[0],\n        newV = _easing2[1];\n      return _objectSpread(_objectSpread({}, val), {}, {\n        from: newX,\n        velocity: newV\n      });\n    }\n    return val;\n  }, preVals);\n  if (steps < 1) {\n    return (0, _util.mapObject)(function (key, val) {\n      if (needContinue(val)) {\n        return _objectSpread(_objectSpread({}, val), {}, {\n          velocity: alpha(val.velocity, nextStepVals[key].velocity, steps),\n          from: alpha(val.from, nextStepVals[key].from, steps)\n        });\n      }\n      return val;\n    }, preVals);\n  }\n  return calStepperVals(easing, nextStepVals, steps - 1);\n};\n\n// configure update function\nvar _default = exports[\"default\"] = function _default(from, to, easing, duration, render) {\n  var interKeys = (0, _util.getIntersectionKeys)(from, to);\n  var timingStyle = interKeys.reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, [from[key], to[key]]));\n  }, {});\n  var stepperStyle = interKeys.reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, {\n      from: from[key],\n      velocity: 0,\n      to: to[key]\n    }));\n  }, {});\n  var cafId = -1;\n  var preTime;\n  var beginTime;\n  var update = function update() {\n    return null;\n  };\n  var getCurrStyle = function getCurrStyle() {\n    return (0, _util.mapObject)(function (key, val) {\n      return val.from;\n    }, stepperStyle);\n  };\n  var shouldStopAnimation = function shouldStopAnimation() {\n    return !Object.values(stepperStyle).filter(needContinue).length;\n  };\n\n  // stepper timing function like spring\n  var stepperUpdate = function stepperUpdate(now) {\n    if (!preTime) {\n      preTime = now;\n    }\n    var deltaTime = now - preTime;\n    var steps = deltaTime / easing.dt;\n    stepperStyle = calStepperVals(easing, stepperStyle, steps);\n    // get union set and add compatible prefix\n    render(_objectSpread(_objectSpread(_objectSpread({}, from), to), getCurrStyle(stepperStyle)));\n    preTime = now;\n    if (!shouldStopAnimation()) {\n      cafId = requestAnimationFrame(update);\n    }\n  };\n\n  // t => val timing function like cubic-bezier\n  var timingUpdate = function timingUpdate(now) {\n    if (!beginTime) {\n      beginTime = now;\n    }\n    var t = (now - beginTime) / duration;\n    var currStyle = (0, _util.mapObject)(function (key, val) {\n      return alpha.apply(void 0, _toConsumableArray(val).concat([easing(t)]));\n    }, timingStyle);\n\n    // get union set and add compatible prefix\n    render(_objectSpread(_objectSpread(_objectSpread({}, from), to), currStyle));\n    if (t < 1) {\n      cafId = requestAnimationFrame(update);\n    } else {\n      var finalStyle = (0, _util.mapObject)(function (key, val) {\n        return alpha.apply(void 0, _toConsumableArray(val).concat([easing(1)]));\n      }, timingStyle);\n      render(_objectSpread(_objectSpread(_objectSpread({}, from), to), finalStyle));\n    }\n  };\n  update = easing.isStepper ? stepperUpdate : timingUpdate;\n\n  // return start animation method\n  return function () {\n    requestAnimationFrame(update);\n\n    // return stop animation method\n    return function () {\n      cancelAnimationFrame(cafId);\n    };\n  };\n};\n\n//# sourceURL=webpack://react-smooth/./src/configUpdate.js?");

/***/ }),

/***/ "./src/easing.js":
/*!***********************!*\
  !*** ./src/easing.js ***!
  \***********************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.configSpring = exports.configEasing = exports.configBezier = void 0;\nvar _util = __webpack_require__(/*! ./util */ \"./src/util.js\");\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nvar ACCURACY = 1e-4;\nvar cubicBezierFactor = function cubicBezierFactor(c1, c2) {\n  return [0, 3 * c1, 3 * c2 - 6 * c1, 3 * c1 - 3 * c2 + 1];\n};\nvar multyTime = function multyTime(params, t) {\n  return params.map(function (param, i) {\n    return param * Math.pow(t, i);\n  }).reduce(function (pre, curr) {\n    return pre + curr;\n  });\n};\nvar cubicBezier = function cubicBezier(c1, c2) {\n  return function (t) {\n    var params = cubicBezierFactor(c1, c2);\n    return multyTime(params, t);\n  };\n};\nvar derivativeCubicBezier = function derivativeCubicBezier(c1, c2) {\n  return function (t) {\n    var params = cubicBezierFactor(c1, c2);\n    var newParams = [].concat(_toConsumableArray(params.map(function (param, i) {\n      return param * i;\n    }).slice(1)), [0]);\n    return multyTime(newParams, t);\n  };\n};\n\n// calculate cubic-bezier using Newton's method\nvar configBezier = exports.configBezier = function configBezier() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n  var x1 = args[0],\n    y1 = args[1],\n    x2 = args[2],\n    y2 = args[3];\n  if (args.length === 1) {\n    switch (args[0]) {\n      case 'linear':\n        x1 = 0.0;\n        y1 = 0.0;\n        x2 = 1.0;\n        y2 = 1.0;\n        break;\n      case 'ease':\n        x1 = 0.25;\n        y1 = 0.1;\n        x2 = 0.25;\n        y2 = 1.0;\n        break;\n      case 'ease-in':\n        x1 = 0.42;\n        y1 = 0.0;\n        x2 = 1.0;\n        y2 = 1.0;\n        break;\n      case 'ease-out':\n        x1 = 0.42;\n        y1 = 0.0;\n        x2 = 0.58;\n        y2 = 1.0;\n        break;\n      case 'ease-in-out':\n        x1 = 0.0;\n        y1 = 0.0;\n        x2 = 0.58;\n        y2 = 1.0;\n        break;\n      default:\n        {\n          var easing = args[0].split('(');\n          if (easing[0] === 'cubic-bezier' && easing[1].split(')')[0].split(',').length === 4) {\n            var _easing$1$split$0$spl = easing[1].split(')')[0].split(',').map(function (x) {\n              return parseFloat(x);\n            });\n            var _easing$1$split$0$spl2 = _slicedToArray(_easing$1$split$0$spl, 4);\n            x1 = _easing$1$split$0$spl2[0];\n            y1 = _easing$1$split$0$spl2[1];\n            x2 = _easing$1$split$0$spl2[2];\n            y2 = _easing$1$split$0$spl2[3];\n          } else {\n            (0, _util.warn)(false, '[configBezier]: arguments should be one of ' + \"oneOf 'linear', 'ease', 'ease-in', 'ease-out', \" + \"'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s\", args);\n          }\n        }\n    }\n  }\n  (0, _util.warn)([x1, x2, y1, y2].every(function (num) {\n    return typeof num === 'number' && num >= 0 && num <= 1;\n  }), '[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s', args);\n  var curveX = cubicBezier(x1, x2);\n  var curveY = cubicBezier(y1, y2);\n  var derCurveX = derivativeCubicBezier(x1, x2);\n  var rangeValue = function rangeValue(value) {\n    if (value > 1) {\n      return 1;\n    }\n    if (value < 0) {\n      return 0;\n    }\n    return value;\n  };\n  var bezier = function bezier(_t) {\n    var t = _t > 1 ? 1 : _t;\n    var x = t;\n    for (var i = 0; i < 8; ++i) {\n      var evalT = curveX(x) - t;\n      var derVal = derCurveX(x);\n      if (Math.abs(evalT - t) < ACCURACY || derVal < ACCURACY) {\n        return curveY(x);\n      }\n      x = rangeValue(x - evalT / derVal);\n    }\n    return curveY(x);\n  };\n  bezier.isStepper = false;\n  return bezier;\n};\nvar configSpring = exports.configSpring = function configSpring() {\n  var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var _config$stiff = config.stiff,\n    stiff = _config$stiff === void 0 ? 100 : _config$stiff,\n    _config$damping = config.damping,\n    damping = _config$damping === void 0 ? 8 : _config$damping,\n    _config$dt = config.dt,\n    dt = _config$dt === void 0 ? 17 : _config$dt;\n  var stepper = function stepper(currX, destX, currV) {\n    var FSpring = -(currX - destX) * stiff;\n    var FDamping = currV * damping;\n    var newV = currV + (FSpring - FDamping) * dt / 1000;\n    var newX = currV * dt / 1000 + currX;\n    if (Math.abs(newX - destX) < ACCURACY && Math.abs(newV) < ACCURACY) {\n      return [destX, 0];\n    }\n    return [newX, newV];\n  };\n  stepper.isStepper = true;\n  stepper.dt = dt;\n  return stepper;\n};\nvar configEasing = exports.configEasing = function configEasing() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  var easing = args[0];\n  if (typeof easing === 'string') {\n    switch (easing) {\n      case 'ease':\n      case 'ease-in-out':\n      case 'ease-out':\n      case 'ease-in':\n      case 'linear':\n        return configBezier(easing);\n      case 'spring':\n        return configSpring();\n      default:\n        if (easing.split('(')[0] === 'cubic-bezier') {\n          return configBezier(easing);\n        }\n        (0, _util.warn)(false, \"[configEasing]: first argument should be one of 'ease', 'ease-in', \" + \"'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s\", args);\n    }\n  }\n  if (typeof easing === 'function') {\n    return easing;\n  }\n  (0, _util.warn)(false, '[configEasing]: first argument type should be function or string, instead received %s', args);\n  return null;\n};\n\n//# sourceURL=webpack://react-smooth/./src/easing.js?");

/***/ }),

/***/ "./src/index.js":
/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nObject.defineProperty(exports, \"AnimateGroup\", ({\n  enumerable: true,\n  get: function get() {\n    return _AnimateGroup.default;\n  }\n}));\nObject.defineProperty(exports, \"configBezier\", ({\n  enumerable: true,\n  get: function get() {\n    return _easing.configBezier;\n  }\n}));\nObject.defineProperty(exports, \"configSpring\", ({\n  enumerable: true,\n  get: function get() {\n    return _easing.configSpring;\n  }\n}));\nexports[\"default\"] = void 0;\nvar _Animate = _interopRequireDefault(__webpack_require__(/*! ./Animate */ \"./src/Animate.js\"));\nvar _easing = __webpack_require__(/*! ./easing */ \"./src/easing.js\");\nvar _AnimateGroup = _interopRequireDefault(__webpack_require__(/*! ./AnimateGroup */ \"./src/AnimateGroup.js\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nvar _default = exports[\"default\"] = _Animate.default;\n\n//# sourceURL=webpack://react-smooth/./src/index.js?");

/***/ }),

/***/ "./src/setRafTimeout.js":
/*!******************************!*\
  !*** ./src/setRafTimeout.js ***!
  \******************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports[\"default\"] = setRafTimeout;\nfunction safeRequestAnimationFrame(callback) {\n  if (typeof requestAnimationFrame !== 'undefined') requestAnimationFrame(callback);\n}\nfunction setRafTimeout(callback) {\n  var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currTime = -1;\n  var shouldUpdate = function shouldUpdate(now) {\n    if (currTime < 0) {\n      currTime = now;\n    }\n    if (now - currTime > timeout) {\n      callback(now);\n      currTime = -1;\n    } else {\n      safeRequestAnimationFrame(shouldUpdate);\n    }\n  };\n  requestAnimationFrame(shouldUpdate);\n}\n\n//# sourceURL=webpack://react-smooth/./src/setRafTimeout.js?");

/***/ }),

/***/ "./src/util.js":
/*!*********************!*\
  !*** ./src/util.js ***!
  \*********************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.warn = exports.mapObject = exports.log = exports.identity = exports.getTransitionVal = exports.getIntersectionKeys = exports.getDashCase = exports.debugf = exports.debug = void 0;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return _typeof(key) === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (_typeof(input) !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (_typeof(res) !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/* eslint no-console: 0 */\n\nvar getIntersectionKeys = exports.getIntersectionKeys = function getIntersectionKeys(preObj, nextObj) {\n  return [Object.keys(preObj), Object.keys(nextObj)].reduce(function (a, b) {\n    return a.filter(function (c) {\n      return b.includes(c);\n    });\n  });\n};\nvar identity = exports.identity = function identity(param) {\n  return param;\n};\n\n/*\n * @description: convert camel case to dash case\n * string => string\n */\nvar getDashCase = exports.getDashCase = function getDashCase(name) {\n  return name.replace(/([A-Z])/g, function (v) {\n    return \"-\".concat(v.toLowerCase());\n  });\n};\nvar log = exports.log = function log() {\n  var _console;\n  (_console = console).log.apply(_console, arguments);\n};\n\n/*\n * @description: log the value of a varible\n * string => any => any\n */\nvar debug = exports.debug = function debug(name) {\n  return function (item) {\n    log(name, item);\n    return item;\n  };\n};\n\n/*\n * @description: log name, args, return value of a function\n * function => function\n */\nvar debugf = exports.debugf = function debugf(tag, f) {\n  return function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    var res = f.apply(void 0, args);\n    var name = tag || f.name || 'anonymous function';\n    var argNames = \"(\".concat(args.map(JSON.stringify).join(', '), \")\");\n    log(\"\".concat(name, \": \").concat(argNames, \" => \").concat(JSON.stringify(res)));\n    return res;\n  };\n};\n\n/*\n * @description: map object on every element in this object.\n * (function, object) => object\n */\nvar mapObject = exports.mapObject = function mapObject(fn, obj) {\n  return Object.keys(obj).reduce(function (res, key) {\n    return _objectSpread(_objectSpread({}, res), {}, _defineProperty({}, key, fn(key, obj[key])));\n  }, {});\n};\nvar getTransitionVal = exports.getTransitionVal = function getTransitionVal(props, duration, easing) {\n  return props.map(function (prop) {\n    return \"\".concat(getDashCase(prop), \" \").concat(duration, \"ms \").concat(easing);\n  }).join(',');\n};\nvar isDev = \"development\" !== 'production';\nvar warn = exports.warn = function warn(condition, format, a, b, c, d, e, f) {\n  if (isDev && typeof console !== 'undefined' && console.warn) {\n    if (format === undefined) {\n      console.warn('LogUtils requires an error message argument');\n    }\n    if (!condition) {\n      if (format === undefined) {\n        console.warn('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n      } else {\n        var args = [a, b, c, d, e, f];\n        var argIndex = 0;\n        console.warn(format.replace(/%s/g, function () {\n          return args[argIndex++];\n        }));\n      }\n    }\n  }\n};\n\n//# sourceURL=webpack://react-smooth/./src/util.js?");

/***/ }),

/***/ "./node_modules/object-assign/index.js":
/*!*********************************************!*\
  !*** ./node_modules/object-assign/index.js ***!
  \*********************************************/
/***/ ((module) => {

"use strict";
eval("/*\nobject-assign\n(c) Sindre Sorhus\n@license MIT\n*/\n\n\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/object-assign/index.js?");

/***/ }),

/***/ "./node_modules/prop-types/checkPropTypes.js":
/*!***************************************************!*\
  !*** ./node_modules/prop-types/checkPropTypes.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nvar printWarning = function() {};\n\nif (true) {\n  var ReactPropTypesSecret = __webpack_require__(/*! ./lib/ReactPropTypesSecret */ \"./node_modules/prop-types/lib/ReactPropTypesSecret.js\");\n  var loggedTypeFailures = {};\n  var has = __webpack_require__(/*! ./lib/has */ \"./node_modules/prop-types/lib/has.js\");\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (true) {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (true) {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/checkPropTypes.js?");

/***/ }),

/***/ "./node_modules/prop-types/factoryWithTypeCheckers.js":
/*!************************************************************!*\
  !*** ./node_modules/prop-types/factoryWithTypeCheckers.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nvar ReactIs = __webpack_require__(/*! react-is */ \"./node_modules/prop-types/node_modules/react-is/index.js\");\nvar assign = __webpack_require__(/*! object-assign */ \"./node_modules/object-assign/index.js\");\n\nvar ReactPropTypesSecret = __webpack_require__(/*! ./lib/ReactPropTypesSecret */ \"./node_modules/prop-types/lib/ReactPropTypesSecret.js\");\nvar has = __webpack_require__(/*! ./lib/has */ \"./node_modules/prop-types/lib/has.js\");\nvar checkPropTypes = __webpack_require__(/*! ./checkPropTypes */ \"./node_modules/prop-types/checkPropTypes.js\");\n\nvar printWarning = function() {};\n\nif (true) {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (true) {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if ( true && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (true) {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n       true ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/factoryWithTypeCheckers.js?");

/***/ }),

/***/ "./node_modules/prop-types/index.js":
/*!******************************************!*\
  !*** ./node_modules/prop-types/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (true) {\n  var ReactIs = __webpack_require__(/*! react-is */ \"./node_modules/prop-types/node_modules/react-is/index.js\");\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = __webpack_require__(/*! ./factoryWithTypeCheckers */ \"./node_modules/prop-types/factoryWithTypeCheckers.js\")(ReactIs.isElement, throwOnDirectAccess);\n} else {}\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/index.js?");

/***/ }),

/***/ "./node_modules/prop-types/lib/ReactPropTypesSecret.js":
/*!*************************************************************!*\
  !*** ./node_modules/prop-types/lib/ReactPropTypesSecret.js ***!
  \*************************************************************/
/***/ ((module) => {

"use strict";
eval("/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/lib/ReactPropTypesSecret.js?");

/***/ }),

/***/ "./node_modules/prop-types/lib/has.js":
/*!********************************************!*\
  !*** ./node_modules/prop-types/lib/has.js ***!
  \********************************************/
/***/ ((module) => {

eval("module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/lib/has.js?");

/***/ }),

/***/ "./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js?");

/***/ }),

/***/ "./node_modules/prop-types/node_modules/react-is/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/prop-types/node_modules/react-is/index.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-is.development.js */ \"./node_modules/prop-types/node_modules/react-is/cjs/react-is.development.js\");\n}\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/prop-types/node_modules/react-is/index.js?");

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = undefined;

/***/ }),

/***/ "react-transition-group":
/*!**********************************************************************************************************************************************************!*\
  !*** external {"root":["ReactTransitionGroup"],"commonjs2":"react-transition-group","commonjs":"react-transition-group","amd":"react-transition-group"} ***!
  \**********************************************************************************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = undefined;

/***/ }),

/***/ "./node_modules/fast-equals/dist/cjs/index.cjs":
/*!*****************************************************!*\
  !*** ./node_modules/fast-equals/dist/cjs/index.cjs ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\n\nvar getOwnPropertyNames = Object.getOwnPropertyNames, getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Combine two comparators into a single comparators.\n */\nfunction combineComparators(comparatorA, comparatorB) {\n    return function isEqual(a, b, state) {\n        return comparatorA(a, b, state) && comparatorB(a, b, state);\n    };\n}\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nfunction createIsCircular(areItemsEqual) {\n    return function isCircular(a, b, state) {\n        if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n            return areItemsEqual(a, b, state);\n        }\n        var cache = state.cache;\n        var cachedA = cache.get(a);\n        var cachedB = cache.get(b);\n        if (cachedA && cachedB) {\n            return cachedA === b && cachedB === a;\n        }\n        cache.set(a, b);\n        cache.set(b, a);\n        var result = areItemsEqual(a, b, state);\n        cache.delete(a);\n        cache.delete(b);\n        return result;\n    };\n}\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nfunction getStrictProperties(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Whether the object contains the property passed as an own property.\n */\nvar hasOwn = Object.hasOwn ||\n    (function (object, property) {\n        return hasOwnProperty.call(object, property);\n    });\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nfunction sameValueZeroEqual(a, b) {\n    return a || b ? a === b : a === b || (a !== a && b !== b);\n}\n\nvar OWNER = '_owner';\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, keys = Object.keys;\n/**\n * Whether the arrays are equal in value.\n */\nfunction areArraysEqual(a, b, state) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the dates passed are equal in value.\n */\nfunction areDatesEqual(a, b) {\n    return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n/**\n * Whether the `Map`s are equal in value.\n */\nfunction areMapsEqual(a, b, state) {\n    if (a.size !== b.size) {\n        return false;\n    }\n    var matchedIndices = {};\n    var aIterable = a.entries();\n    var index = 0;\n    var aResult;\n    var bResult;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.entries();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            var _a = aResult.value, aKey = _a[0], aValue = _a[1];\n            var _b = bResult.value, bKey = _b[0], bValue = _b[1];\n            if (!hasMatch &&\n                !matchedIndices[matchIndex] &&\n                (hasMatch =\n                    state.equals(aKey, bKey, index, matchIndex, a, b, state) &&\n                        state.equals(aValue, bValue, aKey, bKey, a, b, state))) {\n                matchedIndices[matchIndex] = true;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n        index++;\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value.\n */\nfunction areObjectsEqual(a, b, state) {\n    var properties = keys(a);\n    var index = properties.length;\n    if (keys(b).length !== index) {\n        return false;\n    }\n    var property;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        property = properties[index];\n        if (property === OWNER &&\n            (a.$$typeof || b.$$typeof) &&\n            a.$$typeof !== b.$$typeof) {\n            return false;\n        }\n        if (!hasOwn(b, property) ||\n            !state.equals(a[property], b[property], property, property, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nfunction areObjectsEqualStrict(a, b, state) {\n    var properties = getStrictProperties(a);\n    var index = properties.length;\n    if (getStrictProperties(b).length !== index) {\n        return false;\n    }\n    var property;\n    var descriptorA;\n    var descriptorB;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        property = properties[index];\n        if (property === OWNER &&\n            (a.$$typeof || b.$$typeof) &&\n            a.$$typeof !== b.$$typeof) {\n            return false;\n        }\n        if (!hasOwn(b, property)) {\n            return false;\n        }\n        if (!state.equals(a[property], b[property], property, property, a, b, state)) {\n            return false;\n        }\n        descriptorA = getOwnPropertyDescriptor(a, property);\n        descriptorB = getOwnPropertyDescriptor(b, property);\n        if ((descriptorA || descriptorB) &&\n            (!descriptorA ||\n                !descriptorB ||\n                descriptorA.configurable !== descriptorB.configurable ||\n                descriptorA.enumerable !== descriptorB.enumerable ||\n                descriptorA.writable !== descriptorB.writable)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nfunction arePrimitiveWrappersEqual(a, b) {\n    return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n/**\n * Whether the regexps passed are equal in value.\n */\nfunction areRegExpsEqual(a, b) {\n    return a.source === b.source && a.flags === b.flags;\n}\n/**\n * Whether the `Set`s are equal in value.\n */\nfunction areSetsEqual(a, b, state) {\n    if (a.size !== b.size) {\n        return false;\n    }\n    var matchedIndices = {};\n    var aIterable = a.values();\n    var aResult;\n    var bResult;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.values();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            if (!hasMatch &&\n                !matchedIndices[matchIndex] &&\n                (hasMatch = state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state))) {\n                matchedIndices[matchIndex] = true;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the TypedArray instances are equal in value.\n */\nfunction areTypedArraysEqual(a, b) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (a[index] !== b[index]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nvar ARGUMENTS_TAG = '[object Arguments]';\nvar BOOLEAN_TAG = '[object Boolean]';\nvar DATE_TAG = '[object Date]';\nvar MAP_TAG = '[object Map]';\nvar NUMBER_TAG = '[object Number]';\nvar OBJECT_TAG = '[object Object]';\nvar REG_EXP_TAG = '[object RegExp]';\nvar SET_TAG = '[object Set]';\nvar STRING_TAG = '[object String]';\nvar isArray = Array.isArray;\nvar isTypedArray = typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nvar assign = Object.assign;\nvar getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nfunction createEqualityComparator(_a) {\n    var areArraysEqual = _a.areArraysEqual, areDatesEqual = _a.areDatesEqual, areMapsEqual = _a.areMapsEqual, areObjectsEqual = _a.areObjectsEqual, arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual, areRegExpsEqual = _a.areRegExpsEqual, areSetsEqual = _a.areSetsEqual, areTypedArraysEqual = _a.areTypedArraysEqual;\n    /**\n     * compare the value of the two objects and return true if they are equivalent in values\n     */\n    return function comparator(a, b, state) {\n        // If the items are strictly equal, no need to do a value comparison.\n        if (a === b) {\n            return true;\n        }\n        // If the items are not non-nullish objects, then the only possibility\n        // of them being equal but not strictly is if they are both `NaN`. Since\n        // `NaN` is uniquely not equal to itself, we can use self-comparison of\n        // both objects, which is faster than `isNaN()`.\n        if (a == null ||\n            b == null ||\n            typeof a !== 'object' ||\n            typeof b !== 'object') {\n            return a !== a && b !== b;\n        }\n        var constructor = a.constructor;\n        // Checks are listed in order of commonality of use-case:\n        //   1. Common complex object types (plain object, array)\n        //   2. Common data values (date, regexp)\n        //   3. Less-common complex object types (map, set)\n        //   4. Less-common data values (promise, primitive wrappers)\n        // Inherently this is both subjective and assumptive, however\n        // when reviewing comparable libraries in the wild this order\n        // appears to be generally consistent.\n        // Constructors should match, otherwise there is potential for false positives\n        // between class and subclass or custom object and POJO.\n        if (constructor !== b.constructor) {\n            return false;\n        }\n        // `isPlainObject` only checks against the object's own realm. Cross-realm\n        // comparisons are rare, and will be handled in the ultimate fallback, so\n        // we can avoid capturing the string tag.\n        if (constructor === Object) {\n            return areObjectsEqual(a, b, state);\n        }\n        // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n        // the string tag or doing an `instanceof` check.\n        if (isArray(a)) {\n            return areArraysEqual(a, b, state);\n        }\n        // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n        // capturing the string tag or comparing against all possible constructors.\n        if (isTypedArray != null && isTypedArray(a)) {\n            return areTypedArraysEqual(a, b, state);\n        }\n        // Try to fast-path equality checks for other complex object types in the\n        // same realm to avoid capturing the string tag. Strict equality is used\n        // instead of `instanceof` because it is more performant for the common\n        // use-case. If someone is subclassing a native class, it will be handled\n        // with the string tag comparison.\n        if (constructor === Date) {\n            return areDatesEqual(a, b, state);\n        }\n        if (constructor === RegExp) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (constructor === Map) {\n            return areMapsEqual(a, b, state);\n        }\n        if (constructor === Set) {\n            return areSetsEqual(a, b, state);\n        }\n        // Since this is a custom object, capture the string tag to determing its type.\n        // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n        var tag = getTag(a);\n        if (tag === DATE_TAG) {\n            return areDatesEqual(a, b, state);\n        }\n        if (tag === REG_EXP_TAG) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (tag === MAP_TAG) {\n            return areMapsEqual(a, b, state);\n        }\n        if (tag === SET_TAG) {\n            return areSetsEqual(a, b, state);\n        }\n        if (tag === OBJECT_TAG) {\n            // The exception for value comparison is custom `Promise`-like class instances. These should\n            // be treated the same as standard `Promise` objects, which means strict equality, and if\n            // it reaches this point then that strict equality comparison has already failed.\n            return (typeof a.then !== 'function' &&\n                typeof b.then !== 'function' &&\n                areObjectsEqual(a, b, state));\n        }\n        // If an arguments tag, it should be treated as a standard object.\n        if (tag === ARGUMENTS_TAG) {\n            return areObjectsEqual(a, b, state);\n        }\n        // As the penultimate fallback, check if the values passed are primitive wrappers. This\n        // is very rare in modern JS, which is why it is deprioritized compared to all other object\n        // types.\n        if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n            return arePrimitiveWrappersEqual(a, b, state);\n        }\n        // If not matching any tags that require a specific type of comparison, then we hard-code false because\n        // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n        //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n        //     comparison that can be made.\n        //   - For types that can be introspected, but rarely have requirements to be compared\n        //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n        //     use-cases (may be included in a future release, if requested enough).\n        //   - For types that can be introspected but do not have an objective definition of what\n        //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n        // In all cases, these decisions should be reevaluated based on changes to the language and\n        // common development practices.\n        return false;\n    };\n}\n/**\n * Create the configuration object used for building comparators.\n */\nfunction createEqualityComparatorConfig(_a) {\n    var circular = _a.circular, createCustomConfig = _a.createCustomConfig, strict = _a.strict;\n    var config = {\n        areArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areArraysEqual,\n        areDatesEqual: areDatesEqual,\n        areMapsEqual: strict\n            ? combineComparators(areMapsEqual, areObjectsEqualStrict)\n            : areMapsEqual,\n        areObjectsEqual: strict\n            ? areObjectsEqualStrict\n            : areObjectsEqual,\n        arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n        areRegExpsEqual: areRegExpsEqual,\n        areSetsEqual: strict\n            ? combineComparators(areSetsEqual, areObjectsEqualStrict)\n            : areSetsEqual,\n        areTypedArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areTypedArraysEqual,\n    };\n    if (createCustomConfig) {\n        config = assign({}, config, createCustomConfig(config));\n    }\n    if (circular) {\n        var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n        var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n        var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n        var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n        config = assign({}, config, {\n            areArraysEqual: areArraysEqual$1,\n            areMapsEqual: areMapsEqual$1,\n            areObjectsEqual: areObjectsEqual$1,\n            areSetsEqual: areSetsEqual$1,\n        });\n    }\n    return config;\n}\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nfunction createInternalEqualityComparator(compare) {\n    return function (a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n        return compare(a, b, state);\n    };\n}\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nfunction createIsEqual(_a) {\n    var circular = _a.circular, comparator = _a.comparator, createState = _a.createState, equals = _a.equals, strict = _a.strict;\n    if (createState) {\n        return function isEqual(a, b) {\n            var _a = createState(), _b = _a.cache, cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b, meta = _a.meta;\n            return comparator(a, b, {\n                cache: cache,\n                equals: equals,\n                meta: meta,\n                strict: strict,\n            });\n        };\n    }\n    if (circular) {\n        return function isEqual(a, b) {\n            return comparator(a, b, {\n                cache: new WeakMap(),\n                equals: equals,\n                meta: undefined,\n                strict: strict,\n            });\n        };\n    }\n    var state = {\n        cache: undefined,\n        equals: equals,\n        meta: undefined,\n        strict: strict,\n    };\n    return function isEqual(a, b) {\n        return comparator(a, b, state);\n    };\n}\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nvar deepEqual = createCustomEqual();\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nvar strictDeepEqual = createCustomEqual({ strict: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nvar circularDeepEqual = createCustomEqual({ circular: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularDeepEqual = createCustomEqual({\n    circular: true,\n    strict: true,\n});\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nvar shallowEqual = createCustomEqual({\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nvar strictShallowEqual = createCustomEqual({\n    strict: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nvar circularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n    strict: true,\n});\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nfunction createCustomEqual(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.circular, circular = _a === void 0 ? false : _a, createCustomInternalComparator = options.createInternalComparator, createState = options.createState, _b = options.strict, strict = _b === void 0 ? false : _b;\n    var config = createEqualityComparatorConfig(options);\n    var comparator = createEqualityComparator(config);\n    var equals = createCustomInternalComparator\n        ? createCustomInternalComparator(comparator)\n        : createInternalEqualityComparator(comparator);\n    return createIsEqual({ circular: circular, comparator: comparator, createState: createState, equals: equals, strict: strict });\n}\n\nexports.circularDeepEqual = circularDeepEqual;\nexports.circularShallowEqual = circularShallowEqual;\nexports.createCustomEqual = createCustomEqual;\nexports.deepEqual = deepEqual;\nexports.sameValueZeroEqual = sameValueZeroEqual;\nexports.shallowEqual = shallowEqual;\nexports.strictCircularDeepEqual = strictCircularDeepEqual;\nexports.strictCircularShallowEqual = strictCircularShallowEqual;\nexports.strictDeepEqual = strictDeepEqual;\nexports.strictShallowEqual = strictShallowEqual;\n//# sourceMappingURL=index.cjs.map\n\n\n//# sourceURL=webpack://react-smooth/./node_modules/fast-equals/dist/cjs/index.cjs?");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/index.js");
/******/ 	
/******/ })()
;