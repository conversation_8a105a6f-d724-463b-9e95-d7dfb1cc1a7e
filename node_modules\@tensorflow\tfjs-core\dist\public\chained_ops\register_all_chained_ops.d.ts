/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/public/chained_ops/register_all_chained_ops" />
import './abs';
import './acos';
import './acosh';
import './add';
import './all';
import './any';
import './arg_max';
import './arg_min';
import './as_scalar';
import './as_type';
import './as1d';
import './as2d';
import './as3d';
import './as4d';
import './as5d';
import './asin';
import './asinh';
import './atan';
import './atan2';
import './atanh';
import './avg_pool';
import './batch_to_space_nd';
import './batchnorm';
import './broadcast_to';
import './cast';
import './ceil';
import './clip_by_value';
import './concat';
import './conv1d';
import './conv2d_transpose';
import './conv2d';
import './cos';
import './cosh';
import './cumprod';
import './cumsum';
import './depth_to_space';
import './depthwise_conv2d';
import './dilation2d';
import './div_no_nan';
import './div';
import './dot';
import './elu';
import './equal';
import './erf';
import './euclidean_norm';
import './exp';
import './expand_dims';
import './expm1';
import './fft';
import './flatten';
import './floor';
import './floorDiv';
import './gather';
import './greater_equal';
import './greater';
import './ifft';
import './irfft';
import './is_finite';
import './is_inf';
import './is_nan';
import './leaky_relu';
import './less_equal';
import './less';
import './local_response_normalization';
import './log_sigmoid';
import './log_softmax';
import './log_sum_exp';
import './log';
import './log1p';
import './logical_and';
import './logical_not';
import './logical_or';
import './logical_xor';
import './mat_mul';
import './max_pool';
import './max';
import './maximum';
import './mean';
import './min';
import './minimum';
import './mirror_pad';
import './mod';
import './mul';
import './neg';
import './norm';
import './not_equal';
import './one_hot';
import './ones_like';
import './pad';
import './pool';
import './pow';
import './prelu';
import './prod';
import './reciprocal';
import './relu';
import './relu6';
import './reshape_as';
import './reshape';
import './resize_bilinear';
import './resize_nearest_neighbor';
import './reverse';
import './rfft';
import './round';
import './rsqrt';
import './selu';
import './separable_conv2d';
import './sigmoid';
import './sign';
import './sin';
import './sinh';
import './slice';
import './softmax';
import './softplus';
import './space_to_batch_nd';
import './split';
import './sqrt';
import './square';
import './squared_difference';
import './squeeze';
import './stack';
import './step';
import './strided_slice';
import './sub';
import './sum';
import './tan';
import './tanh';
import './tile';
import './to_bool';
import './to_float';
import './to_int';
import './topk';
import './transpose';
import './unique';
import './unsorted_segment_sum';
import './unstack';
import './where';
import './zeros_like';
