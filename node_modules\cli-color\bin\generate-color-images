#!/usr/bin/env node

'use strict';

var uniq     = require('es5-ext/array/#/uniq')
  , deferred = require('deferred')
  , resolve  = require('path').resolve
  , gm       = require('gm')
  , colors   = require('../lib/_xterm-colors');

gm.prototype.pThumb = deferred.gate(deferred.promisify(gm.prototype.thumb), 50);

deferred.map(uniq.call(colors), function (color) {
	return gm('ROSE:').fill('#' + color).drawRectangle(0, 0, 100, 100)
		.pThumb(1, 1, resolve(__dirname, color + '.png'), 80).aside(function () {
			console.log('Done: ' + color);
		});
}).done();
