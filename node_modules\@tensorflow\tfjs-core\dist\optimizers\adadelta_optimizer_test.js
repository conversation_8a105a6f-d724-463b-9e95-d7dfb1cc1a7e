/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import * as tf from '../index';
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
describeWithFlags('AdadeltaOptimizer', ALL_ENVS, () => {
    it('basic', async () => {
        const initialTensors = tf.memory().numTensors;
        const learningRate = .1;
        const rho = .95;
        const optimizer = tf.train.adadelta(learningRate, rho);
        const x = tf.tensor1d([1, 2]).variable();
        const f = () => x.square().sum();
        let numTensors = tf.memory().numTensors;
        let cost = optimizer.minimize(f, /* returnCost */ true);
        // Cost & 2 accumulators should be the only additional arrays.
        expect(tf.memory().numTensors).toBe(numTensors + 3);
        // epsilon = 1-e8
        // newAccumulatedGrad = rho * accumulatedGrad + (1 - rho) * grad ^ 2
        // updates = -grad * sqrt(accumulatedUpdate + epsilon) /
        //     sqrt(accumulatedGrad + epsilon)
        // newAccumulatedUpdate = rho * accumulatedUpdate + (1 - rho) * updates ^ 2
        // x += learningRate * updates
        //
        // de/dx = [2, 4]
        // accumulatedGrad = [0, 0]
        // newAccumulatedGrad = [.2, .8]
        // updates = [-2, -4]
        // newAccumulatedUpdate = [.2, .8]
        // x = [0.8, 1.6]
        expectArraysClose(await x.data(), [0.8, 1.6]);
        cost.dispose();
        numTensors = tf.memory().numTensors;
        cost = optimizer.minimize(f, /* returnCost */ false);
        // de/dx = [1.6, 3.2]
        // accumulatedGrad = [.2, .8]
        // accumulatedUpdate = [.2, .8]
        // newAccumulatedGrad = [0.318, 1.272]
        // updates = [-1.6, -3.2]
        // x = [0.64, 1.28]
        expectArraysClose(await x.data(), [0.64, 1.28]);
        // There should be no new additional Tensors.
        expect(tf.memory().numTensors).toBe(numTensors);
        expect(cost).toBe(null);
        x.dispose();
        optimizer.dispose();
        // The only additional tensor remaining is the argument to variable().
        expect(tf.memory().numTensors).toBe(initialTensors + 1);
    });
    it('Save, load weights and continue training', async () => {
        const learningRate = .1;
        const rho = .95;
        const optimizer1 = tf.train.adadelta(learningRate, rho);
        const x = tf.tensor1d([1, 2]).variable();
        const f = () => x.square().sum();
        let cost = optimizer1.minimize(f, /* returnCost */ true);
        expectArraysClose(await cost.data(), 5);
        expectArraysClose(await x.data(), [0.8, 1.6]);
        const weights = await optimizer1.getWeights();
        expect(weights.length).toEqual(3);
        expect(weights[0].name).toEqual('iter');
        const optimizer2 = tf.train.adadelta(learningRate, rho);
        await optimizer2.setWeights(weights);
        cost = optimizer2.minimize(f, /* returnCost */ true);
        expectArraysClose(await cost.data(), 3.2);
        expectArraysClose(await x.data(), [0.64, 1.28]);
        expect(optimizer2.iterations).toEqual(2);
    });
    it('serialization round-trip', () => {
        const originalOpt = tf.train.adadelta(0.1, 0.2, 2e-8);
        const reserialized = tf.AdadeltaOptimizer.fromConfig(tf.AdadeltaOptimizer, originalOpt.getConfig());
        expect(reserialized.getConfig()).toEqual(originalOpt.getConfig());
    });
});
//# sourceMappingURL=data:application/json;base64,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