/**
 * @license
 * Copyright 2024 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import*as e from"@tensorflow/tfjs-core";import{env as t,util as n,clone as r,scalar as s,keep as a,tensor as o,stack as i,concat as u,unstack as p,tidy as l,reshape as c,slice as h,io as d,Tensor as m,dispose as f}from"@tensorflow/tfjs-core";function y(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(n){if("default"!==n&&!(n in e)){var r=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,r.get?r:{enumerable:!0,get:function(){return t[n]}})}}))})),e}var g,b;t().registerFlag("KEEP_INTERMEDIATE_TENSORS",(()=>!1),(e=>{e&&console.warn("Keep intermediate tensors is ON. This will print the values of all intermediate tensors during model inference. Not all models support this mode. For details, check e2e/benchmarks/ model_config.js. This significantly impacts performance.")})),function(e){e[e.DT_INVALID=0]="DT_INVALID",e[e.DT_FLOAT=1]="DT_FLOAT",e[e.DT_DOUBLE=2]="DT_DOUBLE",e[e.DT_INT32=3]="DT_INT32",e[e.DT_UINT8=4]="DT_UINT8",e[e.DT_INT16=5]="DT_INT16",e[e.DT_INT8=6]="DT_INT8",e[e.DT_STRING=7]="DT_STRING",e[e.DT_COMPLEX64=8]="DT_COMPLEX64",e[e.DT_INT64=9]="DT_INT64",e[e.DT_BOOL=10]="DT_BOOL",e[e.DT_QINT8=11]="DT_QINT8",e[e.DT_QUINT8=12]="DT_QUINT8",e[e.DT_QINT32=13]="DT_QINT32",e[e.DT_BFLOAT16=14]="DT_BFLOAT16",e[e.DT_QINT16=15]="DT_QINT16",e[e.DT_QUINT16=16]="DT_QUINT16",e[e.DT_UINT16=17]="DT_UINT16",e[e.DT_COMPLEX128=18]="DT_COMPLEX128",e[e.DT_HALF=19]="DT_HALF",e[e.DT_RESOURCE=20]="DT_RESOURCE",e[e.DT_VARIANT=21]="DT_VARIANT",e[e.DT_UINT32=22]="DT_UINT32",e[e.DT_UINT64=23]="DT_UINT64",e[e.DT_FLOAT_REF=101]="DT_FLOAT_REF",e[e.DT_DOUBLE_REF=102]="DT_DOUBLE_REF",e[e.DT_INT32_REF=103]="DT_INT32_REF",e[e.DT_UINT8_REF=104]="DT_UINT8_REF",e[e.DT_INT16_REF=105]="DT_INT16_REF",e[e.DT_INT8_REF=106]="DT_INT8_REF",e[e.DT_STRING_REF=107]="DT_STRING_REF",e[e.DT_COMPLEX64_REF=108]="DT_COMPLEX64_REF",e[e.DT_INT64_REF=109]="DT_INT64_REF",e[e.DT_BOOL_REF=110]="DT_BOOL_REF",e[e.DT_QINT8_REF=111]="DT_QINT8_REF",e[e.DT_QUINT8_REF=112]="DT_QUINT8_REF",e[e.DT_QINT32_REF=113]="DT_QINT32_REF",e[e.DT_BFLOAT16_REF=114]="DT_BFLOAT16_REF",e[e.DT_QINT16_REF=115]="DT_QINT16_REF",e[e.DT_QUINT16_REF=116]="DT_QUINT16_REF",e[e.DT_UINT16_REF=117]="DT_UINT16_REF",e[e.DT_COMPLEX128_REF=118]="DT_COMPLEX128_REF",e[e.DT_HALF_REF=119]="DT_HALF_REF",e[e.DT_RESOURCE_REF=120]="DT_RESOURCE_REF",e[e.DT_VARIANT_REF=121]="DT_VARIANT_REF",e[e.DT_UINT32_REF=122]="DT_UINT32_REF",e[e.DT_UINT64_REF=123]="DT_UINT64_REF"}(g||(g={})),function(e){var t;(t=e.CheckpointFormatVersion||(e.CheckpointFormatVersion={}))[t.LEGACY=0]="LEGACY",t[t.V1=1]="V1",t[t.V2=2]="V2"}(b||(b={}));const x={};function N(e,t){const n={tfOpName:e,category:"custom",inputs:[],attrs:[],customExecutor:t};x[e]=n}function w(e){return x[e]}function k(e){delete x[e]}function T(e,t,r,s,a){const o=t.inputParams[e];if(o&&void 0!==o.inputIndexStart){const e=o.inputIndexStart,i=0===o.inputIndexEnd?void 0:void 0===o.inputIndexEnd?e+1:o.inputIndexEnd,u=e<0?t.inputNames.length+e:e;if("tensor"===o.type)return v(t.inputNames[u],r,s,a);if("tensors"===o.type){const n=t.inputs.slice(e,i);return t.inputNames.slice(e,i).filter(((e,t)=>{var r;return"NoOp"!==(null===(r=n[t])||void 0===r?void 0:r.op)})).map((e=>v(e,r,s,a)))}const p=v(t.inputNames[u],r,s,a),l=p.dataSync();return"number"===o.type?l[0]:n.toNestedArray(p.shape,l)}const i=t.attrParams[e];return i&&i.value}function v(e,t,n,r){const[s,a]=I(e,n);if(null!=r){const e=r.getHashTableHandleByName(s);if(null!=e)return e}const o=n.currentContextIds.find((e=>!!t[E(s,e)]));return void 0!==o?t[E(s,o)][a]:void 0}function _(e,t,n){return t[E(e,n.currentContextId)]}function S(e,t){const[n,r,s]=I(e,t);return[E(n,t&&t.currentContextId),r,s]}function E(e,t){return t?`${e}-${t}`:e}function I(e,t){if(""===e)return["",0,void 0];const n=null!=t&&null!=t.parseNodeNameCache;if(n){const n=t.parseNodeNameCache.get(e);if(null!=n)return n}const r=e.split(":");let s;if(1===r.length)s=[e,0,void 0];else{const e=r[0],t=3===r.length?r[1]:void 0;s=[e,Number(r[r.length-1]),t]}return n&&t.parseNodeNameCache.set(e,s),s}function $(e,t,n){let r=T("pad",e,t,n);if("explicit"===r){r=T("explicitPaddings",e,t,n);const s=[[0,0],[0,0],[0,0],[0,0]];for(let e=0;e<4;e++)s[e][0]=r[2*e],s[e][1]=r[2*e+1];return s}return r}function A(e){return e.kept?e:r(e)}var D={__proto__:null,json:[{tfOpName:"Add",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"AddV2",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"AddN",category:"arithmetic",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}]},{tfOpName:"BiasAdd",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"Sub",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"RealDiv",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Div",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"DivNoNan",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"FloorDiv",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Mul",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Maximum",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Minimum",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Pow",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"SquaredDifference",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Mod",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"FloorMod",category:"arithmetic",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]};var O={__proto__:null,json:[{tfOpName:"Abs",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Acos",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Asin",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Atan",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Atan2",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"y",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Ceil",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ClipByValue",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"clipValueMin",type:"number"},{start:2,name:"clipValueMax",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Complex",category:"basic_math",inputs:[{start:0,name:"real",type:"tensor"},{start:1,name:"imag",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ComplexAbs",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Cos",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Cosh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Elu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Exp",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Floor",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Log",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Imag",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"Tout",name:"outputType",type:"dtype",notSupported:!0}]},{tfOpName:"Neg",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Real",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"Tout",name:"outputType",type:"dtype",notSupported:!0}]},{tfOpName:"Prelu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"alpha",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Relu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Relu6",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Selu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sigmoid",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sin",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sinh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sqrt",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Rsqrt",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Square",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Tan",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Tanh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Sign",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Round",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Expm1",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Log1p",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Reciprocal",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Softplus",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Asinh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Acosh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Atanh",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Erf",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LeakyRelu",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"alpha",name:"alpha",type:"number",defaultValue:.2},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"IsNan",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"IsFinite",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"IsInf",category:"basic_math",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]};var M={__proto__:null,json:[{tfOpName:"EmptyTensorList",category:"control",inputs:[{start:0,name:"elementShape",type:"shape"},{start:1,name:"maxNumElements",type:"number"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"LoopCond",category:"control",inputs:[{start:0,name:"pred",type:"tensor"}]},{tfOpName:"Switch",category:"control",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"pred",type:"tensor"}]},{tfOpName:"Merge",category:"control",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}]},{tfOpName:"Enter",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"frame_name",name:"frameName",type:"string"},{tfName:"is_constant",name:"isConstant",type:"bool"}]},{tfOpName:"Exit",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"NextIteration",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"TensorArrayV3",category:"control",inputs:[{start:0,name:"size",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"element_shape",name:"elementShape",type:"shape"},{tfName:"dynamic_size",name:"dynamicSize",type:"bool"},{tfName:"clear_after_read",name:"clearAfterRead",type:"bool"},{tfName:"identical_element_shapes",name:"identicalElementShapes",type:"bool"},{tfName:"tensor_array_name",name:"name",type:"string"}]},{tfOpName:"TensorArrayWriteV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"tensor",type:"tensor"},{start:3,name:"flowIn",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"TensorArrayReadV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"flowIn",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"TensorArrayGatherV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"flowIn",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"element_shape",name:"elementShape",type:"shape"}]},{tfOpName:"TensorArrayScatterV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"tensor",type:"tensor"},{start:3,name:"flowIn",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"TensorArrayConcatV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"flowIn",type:"number"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"element_shape_except0",name:"elementShapeExcept0",type:"shape",notSupported:!0}]},{tfOpName:"TensorArraySplitV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"tensor",type:"tensor"},{start:2,name:"lengths",type:"number[]"},{start:3,name:"flowIn",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"TensorArraySizeV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"},{start:1,name:"flowIn",type:"number"}]},{tfOpName:"TensorArrayCloseV3",category:"control",inputs:[{start:0,name:"tensorArrayId",type:"tensor"}]},{tfOpName:"StatelessIf",category:"control",inputs:[{start:0,name:"cond",type:"tensor"},{start:1,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"then_branch",name:"thenBranch",type:"func"},{tfName:"else_branch",name:"elseBranch",type:"func"}]},{tfOpName:"If",category:"control",inputs:[{start:0,name:"cond",type:"tensor"},{start:1,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"then_branch",name:"thenBranch",type:"func"},{tfName:"else_branch",name:"elseBranch",type:"func"}]},{tfOpName:"StatelessWhile",category:"control",inputs:[{start:0,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"cond",name:"cond",type:"func"},{tfName:"body",name:"body",type:"func"}]},{tfOpName:"While",category:"control",inputs:[{start:0,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"cond",name:"cond",type:"func"},{tfName:"body",name:"body",type:"func"}]},{tfOpName:"TensorListScatter",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListScatterV2",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"elementShape",type:"shape"},{start:3,name:"numElements",type:"number"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListGather",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"indices",type:"number[]"},{start:2,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListGetItem",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListSetItem",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"index",type:"number"},{start:2,name:"tensor",type:"tensor"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListReserve",category:"control",inputs:[{start:0,name:"elementShape",type:"shape"},{start:1,name:"numElements",type:"number"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListFromTensor",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListStack",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"},{tfName:"num_elements",name:"numElements",type:"dtype"}]},{tfOpName:"TensorListSplit",category:"control",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"elementShape",type:"shape"},{start:2,name:"lengths",type:"number[]"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListConcat",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"}],attrs:[{tfName:"element_shape",name:"elementShape",type:"shape"},{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListConcatV2",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"}],attrs:[{tfName:"element_shape",name:"elementShape",type:"shape"},{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListPopBack",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"elementShape",type:"shape"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListPushBack",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"tensor",type:"tensor"}],attrs:[{tfName:"element_dtype",name:"elementDType",type:"dtype"}]},{tfOpName:"TensorListLength",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"}]},{tfOpName:"TensorListResize",category:"control",inputs:[{start:0,name:"tensorListId",type:"tensor"},{start:1,name:"size",type:"number"}]}]};var C={__proto__:null,json:[{tfOpName:"AvgPool",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MaxPool",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[],notSupported:!0},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MaxPoolWithArgmax",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"include_batch_in_index",name:"includeBatchInIndex",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"AvgPool3D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MaxPool3D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"ksize",name:"kernelSize",type:"number[]"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Conv1D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"stride",name:"stride",type:"number"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NWC"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"dilation",name:"dilation",type:"number",defaultValue:1}]},{tfOpName:"Conv2D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"useCudnnOnGpu",name:"useCudnnOnGpu",type:"bool"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"_FusedConv2D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"},{start:2,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"num_args",name:"numArgs",type:"number"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"use_cudnn_on_gpu",name:"useCudnnOnGpu",type:"bool",defaultValue:!0},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"dilations",name:"dilations",type:"number[]",defaultValue:[1,1,1,1]},{tfName:"fused_ops",name:"fusedOps",type:"string[]",defaultValue:[]},{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:1e-4},{tfName:"leakyrelu_alpha",name:"leakyreluAlpha",type:"number",defaultValue:.2}]},{tfOpName:"Conv2DBackpropInput",category:"convolution",inputs:[{start:2,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"},{start:0,name:"outputShape",type:"number[]"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]",notSupported:!0}]},{tfOpName:"DepthwiseConv2d",category:"convolution",inputs:[{start:0,name:"input",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"DepthwiseConv2dNative",category:"convolution",inputs:[{start:0,name:"input",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"FusedDepthwiseConv2dNative",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"},{start:2,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"num_args",name:"numArgs",type:"number"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"dilations",name:"dilations",type:"number[]",defaultValue:[1,1,1,1]},{tfName:"fused_ops",name:"fusedOps",type:"string[]",defaultValue:[]},{tfName:"explicit_paddings",name:"explicitPaddings",type:"number[]",defaultValue:[]}]},{tfOpName:"Conv3D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"padding",name:"pad",type:"string"},{tfName:"data_format",name:"dataFormat",type:"string",defaultValue:"NHWC"},{tfName:"dilations",name:"dilations",type:"number[]"}]},{tfOpName:"Dilation2D",category:"convolution",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"filter",type:"tensor"}],attrs:[{tfName:"strides",name:"strides",type:"number[]"},{tfName:"rates",name:"dilations",type:"number[]"},{tfName:"padding",name:"pad",type:"string"}]}]};var F={__proto__:null,json:[{tfOpName:"Fill",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"},{start:1,name:"value",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"LinSpace",category:"creation",inputs:[{start:0,name:"start",type:"number"},{start:1,name:"stop",type:"number"},{start:2,name:"num",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"OneHot",category:"creation",inputs:[{start:0,name:"indices",type:"tensor"},{start:1,name:"depth",type:"number"},{start:2,name:"onValue",type:"number",defaultValue:1},{start:3,name:"offValue",type:"number",defaultValue:0}],attrs:[{tfName:"axis",name:"axis",type:"number",notSupported:!0},{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"Ones",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"OnesLike",category:"creation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"dtype",name:"dtype",type:"dtype"}]},{tfOpName:"RandomStandardNormal",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"seed",name:"seed",type:"number",defaultValue:0},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0},{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"T",name:"T",type:"number",notSupported:!0}]},{tfOpName:"RandomUniform",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"minval",name:"minval",type:"number",defaultValue:0},{tfName:"maxval",name:"maxval",type:"number",defaultValue:1},{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"seed",name:"seed",type:"number",defaultValue:0},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0},{tfName:"T",name:"T",type:"number",notSupported:!0}]},{tfOpName:"RandomUniformInt",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"minval",name:"minval",type:"number"},{tfName:"maxval",name:"maxval",type:"number"},{tfName:"seed",name:"seed",type:"number",defaultValue:0},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0}]},{tfOpName:"Range",category:"creation",inputs:[{start:0,name:"start",type:"number"},{start:1,name:"stop",type:"number"},{start:2,name:"step",type:"number",defaultValue:0}],attrs:[{tfName:"Tidx",name:"dtype",type:"dtype"}]},{tfOpName:"TruncatedNormal",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"means",name:"mean",type:"number",defaultValue:0},{tfName:"stddev",name:"stdDev",type:"number",defaultValue:1},{tfName:"seed",name:"seed",type:"number"},{tfName:"seed2",name:"seed2",type:"number",defaultValue:0,notSupported:!0},{tfName:"dtype",name:"dtype",type:"dtype"},{tfName:"T",name:"T",type:"number",notSupported:!0}]},{tfOpName:"Zeros",category:"creation",inputs:[{start:0,name:"shape",type:"number[]"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"ZerosLike",category:"creation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"Multinomial",category:"creation",inputs:[{start:0,name:"logits",type:"tensor"},{start:1,name:"numSamples",type:"number"}],attrs:[{tfName:"seed",name:"seed",type:"number"},{tfName:"seed2",name:"seed2",type:"number"},{tfName:"T",name:"dtype",type:"dtype"},{tfName:"output_dtype",name:"output_dtype",type:"dtype"}]}]};var R={__proto__:null,json:[{tfOpName:"NonMaxSuppressionV2",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"}]},{tfOpName:"NonMaxSuppressionV3",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"},{start:4,name:"scoreThreshold",type:"number"}]},{tfOpName:"NonMaxSuppressionV4",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"},{start:4,name:"scoreThreshold",type:"number"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0},{tfName:"T_threshold",name:"threshold",type:"dtype",notSupported:!0},{tfName:"pad_to_max_output_size",name:"padToMaxOutputSize",type:"bool"}]},{tfOpName:"NonMaxSuppressionV5",category:"dynamic",inputs:[{start:0,name:"boxes",type:"tensor"},{start:1,name:"scores",type:"tensor"},{start:2,name:"maxOutputSize",type:"number"},{start:3,name:"iouThreshold",type:"number"},{start:4,name:"scoreThreshold",type:"number"},{start:5,name:"softNmsSigma",type:"number"}]},{tfOpName:"Where",category:"dynamic",inputs:[{start:0,name:"condition",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ListDiff",category:"dynamic",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"y",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]}]};var z={__proto__:null,json:[{tfOpName:"LowerBound",category:"evaluation",inputs:[{start:0,name:"sortedSequence",type:"tensor"},{start:1,name:"values",type:"tensor"}]},{tfOpName:"TopKV2",category:"evaluation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"k",type:"number"}],attrs:[{tfName:"sorted",name:"sorted",type:"bool"}]},{tfOpName:"UpperBound",category:"evaluation",inputs:[{start:0,name:"sortedSequence",type:"tensor"},{start:1,name:"values",type:"tensor"}]},{tfOpName:"Unique",category:"evaluation",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"UniqueV2",category:"evaluation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]}]};var L={__proto__:null,json:[{tfOpName:"PlaceholderWithDefault",category:"graph",inputs:[{start:0,name:"default",type:"tensor"}],attrs:[{tfName:"shape",name:"shape",type:"shape"},{tfName:"dtype",name:"dtype",type:"dtype"}]},{tfOpName:"Placeholder",category:"graph",attrs:[{tfName:"shape",name:"shape",type:"shape"},{tfName:"dtype",name:"dtype",type:"dtype"}]},{tfOpName:"Const",category:"graph"},{tfOpName:"Identity",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"IdentityN",category:"graph",inputs:[{start:0,end:0,name:"x",type:"tensors"}]},{tfOpName:"Snapshot",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"Rank",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"Size",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"Shape",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"ShapeN",category:"graph",inputs:[{start:0,end:0,name:"x",type:"tensors"}]},{tfOpName:"Print",category:"graph",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"data",type:"tensors"}],attrs:[{tfName:"message",name:"message",type:"string"},{tfName:"first_n",name:"firstN",type:"number",notSupported:!0},{tfName:"summarize",name:"summarize",type:"number",defaultValue:3}]},{tfOpName:"NoOp",category:"graph",inputs:[]},{tfOpName:"StopGradient",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"FakeQuantWithMinMaxVars",category:"graph",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"min",name:"min",type:"number"},{tfName:"max",name:"max",type:"number"}]}]};var V={__proto__:null,json:[{tfOpName:"HashTable",category:"hash_table",inputs:[],attrs:[{tfName:"shared_name",name:"sharedName",type:"string"},{tfName:"use_node_name_sharing",name:"useNodeNameSharing",type:"bool"},{tfName:"key_dtype",name:"keyDType",type:"dtype"},{tfName:"value_dtype",name:"valueDType",type:"dtype"}]},{tfOpName:"HashTableV2",category:"hash_table",inputs:[],attrs:[{tfName:"shared_name",name:"sharedName",type:"string"},{tfName:"use_node_name_sharing",name:"useNodeNameSharing",type:"bool"},{tfName:"key_dtype",name:"keyDType",type:"dtype"},{tfName:"value_dtype",name:"valueDType",type:"dtype"}]},{tfOpName:"LookupTableImport",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"values",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableImportV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"values",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableFind",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableFindV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"Tin",name:"tIn",type:"dtype",notSupported:!0},{tfName:"Tout",name:"tOut",type:"dtype",notSupported:!0}]},{tfOpName:"LookupTableSize",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"}]},{tfOpName:"LookupTableSizeV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"}]},{tfOpName:"InitializeTable",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"values",type:"tensor"}]},{tfOpName:"InitializeTableV2",category:"hash_table",inputs:[{start:0,name:"tableHandle",type:"tensor"},{start:1,name:"keys",type:"tensor"},{start:2,name:"values",type:"tensor"}]}]};var B={__proto__:null,json:[{tfOpName:"ResizeBilinear",category:"image",inputs:[{start:0,name:"images",type:"tensor"},{start:1,name:"size",type:"number[]"}],attrs:[{tfName:"align_corners",name:"alignCorners",type:"bool"},{tfName:"half_pixel_centers",name:"halfPixelCenters",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"ResizeNearestNeighbor",category:"image",inputs:[{start:0,name:"images",type:"tensor"},{start:1,name:"size",type:"number[]"}],attrs:[{tfName:"align_corners",name:"alignCorners",type:"bool"},{tfName:"half_pixel_centers",name:"halfPixelCenters",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"CropAndResize",category:"image",inputs:[{start:0,name:"image",type:"tensor"},{start:1,name:"boxes",type:"tensor"},{start:2,name:"boxInd",type:"tensor"},{start:3,name:"cropSize",type:"number[]"}],attrs:[{tfName:"method",name:"method",type:"string"},{tfName:"extrapolation_value",name:"extrapolationValue",type:"number"}]},{tfOpName:"ImageProjectiveTransformV3",category:"image",inputs:[{start:0,name:"images",type:"tensor"},{start:1,name:"transforms",type:"tensor"},{start:2,name:"outputShape",type:"number[]"},{start:3,name:"fillValue",type:"number"}],attrs:[{tfName:"interpolation",name:"interpolation",type:"string"},{tfName:"fill_mode",name:"fillMode",type:"string"}]}]};var P={__proto__:null,json:[{tfOpName:"Equal",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"NotEqual",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Greater",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"GreaterEqual",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Less",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LessEqual",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LogicalAnd",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LogicalNot",category:"logical",inputs:[{start:0,name:"a",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"LogicalOr",category:"logical",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Select",category:"logical",inputs:[{start:0,name:"condition",type:"tensor"},{start:1,name:"a",type:"tensor"},{start:2,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"SelectV2",category:"logical",inputs:[{start:0,name:"condition",type:"tensor"},{start:1,name:"a",type:"tensor"},{start:2,name:"b",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"BitwiseAnd",category:"logical",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"y",type:"tensor"}]}]};var K={__proto__:null,json:[{tfOpName:"_FusedMatMul",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"},{start:2,end:0,name:"args",type:"tensors"}],attrs:[{tfName:"num_args",name:"numArgs",type:"number"},{tfName:"fused_ops",name:"fusedOps",type:"string[]",defaultValue:[]},{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:1e-4},{tfName:"transpose_a",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"transpose_b",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"leakyrelu_alpha",name:"leakyreluAlpha",type:"number",defaultValue:.2},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"MatMul",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"transpose_a",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"transpose_b",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"BatchMatMul",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"adj_x",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"adj_y",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"BatchMatMulV2",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"b",type:"tensor"}],attrs:[{tfName:"adj_x",name:"transposeA",type:"bool",defaultValue:!1},{tfName:"adj_y",name:"transposeB",type:"bool",defaultValue:!1},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Transpose",category:"matrices",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"perm",type:"number[]"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Einsum",category:"matrices",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}],attrs:[{tfName:"equation",name:"equation",type:"string"},{tfName:"N",name:"n",type:"number",defaultValue:2},{tfName:"T",name:"dtype",type:"dtype"}]},{tfOpName:"MatrixBandPart",category:"matrices",inputs:[{start:0,name:"a",type:"tensor"},{start:1,name:"numLower",type:"tensor"},{start:1,name:"numUpper",type:"tensor"}]}]};var q={__proto__:null,json:[{tfOpName:"EuclideanNorm",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool",defaultValue:!1}]},{tfOpName:"FusedBatchNorm",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"scale",type:"tensor"},{start:2,name:"offset",type:"tensor"},{start:3,name:"mean",type:"tensor"},{start:4,name:"variance",type:"tensor"}],attrs:[{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:.001},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"FusedBatchNormV2",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"scale",type:"tensor"},{start:2,name:"offset",type:"tensor"},{start:3,name:"mean",type:"tensor"},{start:4,name:"variance",type:"tensor"}],attrs:[{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:.001},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"FusedBatchNormV3",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"scale",type:"tensor"},{start:2,name:"offset",type:"tensor"},{start:3,name:"mean",type:"tensor"},{start:4,name:"variance",type:"tensor"}],attrs:[{tfName:"epsilon",name:"epsilon",type:"number",defaultValue:.001},{tfName:"data_format",name:"dataFormat",type:"string",notSupported:!0}]},{tfOpName:"LRN",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"depth_radius",name:"radius",type:"number",defaultValue:5},{tfName:"bias",name:"bias",type:"number",defaultValue:1},{tfName:"alpha",name:"alpha",type:"number",defaultValue:1},{tfName:"beta",name:"beta",type:"number",defaultValue:.5}]},{tfOpName:"Softmax",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"LogSoftmax",category:"normalization",inputs:[{start:0,name:"x",type:"tensor"}]}]};var U={__proto__:null,json:[{tfOpName:"Bincount",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"size",type:"number"},{start:2,name:"weights",type:"tensor"}]},{tfOpName:"DenseBincount",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"size",type:"number"},{start:2,name:"weights",type:"tensor"}],attrs:[{tfName:"binary_output",name:"binaryOutput",type:"bool"}]},{tfOpName:"Max",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Mean",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Min",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Sum",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"All",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"Any",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"}]},{tfOpName:"ArgMax",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]},{tfOpName:"ArgMin",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]},{tfOpName:"Prod",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}],attrs:[{tfName:"keep_dims",name:"keepDims",type:"bool"},{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"Cumprod",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}],attrs:[{tfName:"exclusive",name:"exclusive",type:"bool"},{tfName:"reverse",name:"reverse",type:"bool"}]},{tfOpName:"Cumsum",category:"reduction",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}],attrs:[{tfName:"exclusive",name:"exclusive",type:"bool"},{tfName:"reverse",name:"reverse",type:"bool"}]}]};var W={__proto__:null,json:[{tfOpName:"ConcatV2",category:"slice_join",inputs:[{start:0,end:-1,name:"tensors",type:"tensors"},{start:-1,name:"axis",type:"number"}],attrs:[{tfName:"N",name:"n",type:"number",defaultValue:2}]},{tfOpName:"Concat",category:"slice_join",inputs:[{start:1,end:0,name:"tensors",type:"tensors"},{start:0,name:"axis",type:"number"}],attrs:[{tfName:"N",name:"n",type:"number",defaultValue:2}]},{tfOpName:"GatherV2",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"axis",type:"number",defaultValue:0}],attrs:[{tfName:"batch_dims",name:"batchDims",type:"number",defaultValue:0}]},{tfOpName:"Gather",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"indices",type:"tensor"}],attrs:[{tfName:"validate_indices",name:"validateIndices",type:"bool",notSupported:!0}]},{tfOpName:"Reverse",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"dims",type:"bool[]"}]},{tfOpName:"ReverseV2",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number[]"}]},{tfOpName:"Slice",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"begin",type:"number[]"},{start:2,name:"size",type:"number[]"}]},{tfOpName:"StridedSlice",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"begin",type:"number[]"},{start:2,name:"end",type:"number[]"},{start:3,name:"strides",type:"number[]"}],attrs:[{tfName:"begin_mask",name:"beginMask",type:"number",defaultValue:0},{tfName:"end_mask",name:"endMask",type:"number",defaultValue:0},{tfName:"new_axis_mask",name:"newAxisMask",type:"number",defaultValue:0},{tfName:"ellipsis_mask",name:"ellipsisMask",type:"number",defaultValue:0},{tfName:"shrink_axis_mask",name:"shrinkAxisMask",type:"number",defaultValue:0}]},{tfOpName:"Pack",category:"slice_join",inputs:[{start:0,end:0,name:"tensors",type:"tensors"}],attrs:[{tfName:"axis",name:"axis",type:"number",defaultValue:0}]},{tfOpName:"Unpack",category:"slice_join",inputs:[{start:0,name:"tensor",type:"tensor"}],attrs:[{tfName:"axis",name:"axis",type:"number",defaultValue:0},{tfName:"num",name:"num",type:"number",defaultValue:0,notSupported:!0}]},{tfOpName:"Tile",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"reps",type:"number[]"}]},{tfOpName:"Split",category:"slice_join",inputs:[{start:0,name:"axis",type:"number",defaultValue:0},{start:1,name:"x",type:"tensor"}],attrs:[{tfName:"num_split",name:"numOrSizeSplits",type:"number",defaultValue:1}]},{tfOpName:"SplitV",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"numOrSizeSplits",type:"number[]"},{start:2,name:"axis",type:"number",defaultValue:0}]},{tfOpName:"ScatterNd",category:"slice_join",inputs:[{start:0,name:"indices",type:"tensor"},{start:1,name:"values",type:"tensor"},{start:2,name:"shape",type:"number[]"}]},{tfOpName:"GatherNd",category:"slice_join",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"indices",type:"tensor"}]},{tfOpName:"SparseToDense",category:"slice_join",inputs:[{start:0,name:"sparseIndices",type:"tensor"},{start:1,name:"outputShape",type:"number[]"},{start:2,name:"sparseValues",type:"tensor"},{start:3,name:"defaultValue",type:"tensor"}],attrs:[{tfName:"validate_indices",name:"validateIndices",type:"bool",defaultValue:!1,notSupported:!0}]},{tfOpName:"TensorScatterUpdate",category:"slice_join",inputs:[{start:0,name:"tensor",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"values",type:"tensor"}]}]};var j={__proto__:null,json:[{tfOpName:"SparseFillEmptyRows",category:"sparse",inputs:[{start:0,name:"indices",type:"tensor"},{start:1,name:"values",type:"tensor"},{start:2,name:"denseShape",type:"tensor"},{start:3,name:"defaultValue",type:"tensor"}]},{tfOpName:"SparseReshape",category:"sparse",inputs:[{start:0,name:"inputIndices",type:"tensor"},{start:1,name:"inputShape",type:"tensor"},{start:2,name:"newShape",type:"tensor"}],attrs:[{tfName:"T",name:"dtype",type:"dtype",notSupported:!0}]},{tfOpName:"SparseSegmentMean",category:"sparse",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"segmentIds",type:"tensor"}]},{tfOpName:"SparseSegmentSum",category:"sparse",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"indices",type:"tensor"},{start:2,name:"segmentIds",type:"tensor"}]}]};var G={__proto__:null,json:[{tfOpName:"FFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"IFFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"}]},{tfOpName:"RFFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"fft_length",type:"number",notSupported:!0}]},{tfOpName:"IRFFT",category:"spectral",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"fft_length",type:"number",notSupported:!0}]}]};var H={__proto__:null,json:[{tfOpName:"StaticRegexReplace",category:"string",inputs:[{start:0,name:"input",type:"tensor"}],attrs:[{tfName:"pattern",name:"pattern",type:"string"},{tfName:"rewrite",name:"rewrite",type:"string"},{tfName:"replace_global",name:"replaceGlobal",type:"bool"}]},{tfOpName:"StringNGrams",category:"string",inputs:[{start:0,name:"data",type:"tensor"},{start:1,name:"dataSplits",type:"tensor"}],attrs:[{tfName:"separator",name:"separator",type:"string"},{tfName:"ngram_widths",name:"nGramWidths",type:"number[]"},{tfName:"left_pad",name:"leftPad",type:"string"},{tfName:"right_pad",name:"rightPad",type:"string"},{tfName:"pad_width",name:"padWidth",type:"number"},{tfName:"preserve_short_sequences",name:"preserveShortSequences",type:"bool"}],outputs:["ngrams","ngrams_splits"]},{tfOpName:"StringSplit",category:"string",inputs:[{start:0,name:"input",type:"tensor"},{start:1,name:"delimiter",type:"tensor"}],attrs:[{tfName:"skip_empty",name:"skipEmpty",type:"bool"}],outputs:["indices","values","shape"]},{tfOpName:"StringToHashBucketFast",category:"string",inputs:[{start:0,name:"input",type:"tensor"}],attrs:[{tfName:"num_buckets",name:"numBuckets",type:"number"}]}]};var Z={__proto__:null,json:[{tfOpName:"Cast",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"SrcT",name:"sdtype",type:"dtype",notSupported:!0},{tfName:"DstT",name:"dtype",type:"dtype"}]},{tfOpName:"ExpandDims",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"axis",type:"number"}]},{tfOpName:"MirrorPad",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"padding",type:"number[]"}],attrs:[{tfName:"mode",name:"mode",type:"string"}]},{tfOpName:"Pad",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"padding",type:"number[]"}],attrs:[{tfName:"constant_value",name:"constantValue",type:"number",defaultValue:0}]},{tfOpName:"PadV2",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"padding",type:"number[]"},{start:2,name:"constantValue",type:"number",defaultValue:0}]},{tfOpName:"Reshape",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"shape",type:"number[]"}]},{tfOpName:"EnsureShape",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"shape",type:"number[]"}]},{tfOpName:"Squeeze",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"axis",tfDeprecatedName:"squeeze_dims",name:"axis",type:"number[]"}]},{tfOpName:"SpaceToBatchND",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"blockShape",type:"number[]"},{start:2,name:"paddings",type:"number[]"}]},{tfOpName:"BatchToSpaceND",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"blockShape",type:"number[]"},{start:2,name:"crops",type:"number[]"}]},{tfOpName:"DepthToSpace",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"}],attrs:[{tfName:"block_size",name:"blockSize",type:"number"},{tfName:"data_format",name:"dataFormat",type:"string"}]},{tfOpName:"BroadcastTo",category:"transformation",inputs:[{start:0,name:"x",type:"tensor"},{start:1,name:"shape",type:"number[]"}],attrs:[]},{tfOpName:"BroadcastArgs",category:"transformation",inputs:[{start:0,name:"s0",type:"tensor"},{start:1,name:"s1",type:"tensor"}],attrs:[]}]};class Q{static get Instance(){return this._instance||(this._instance=new this)}constructor(){const e=[].concat(...[D,O,M,C,F,R,z,L,V,B,P,K,q,U,W,j,G,H,Z].map((e=>e.json)));this.opMappers=e.reduce(((e,t)=>(e[t.tfOpName]=t,e)),{})}transformGraph(e,t={}){const n=e.node,r=[],s=[],a=[],o=n.reduce(((e,t)=>(e[t.name]=this.mapNode(t),t.op.startsWith("Placeholder")?r.push(e[t.name]):"Const"===t.op?s.push(e[t.name]):null!=t.input&&0!==t.input.length||a.push(e[t.name]),e)),{});let i=[];const u=[];let p={},l={};null!=t&&(p=this.mapSignatureEntries(t.inputs),l=this.mapSignatureEntries(t.outputs));const c=Object.keys(o);c.forEach((e=>{const t=o[e];t.inputNames.forEach(((e,n)=>{const[r,,s]=S(e),a=o[r];if(null!=a.outputs){const e=a.outputs.indexOf(s);if(-1!==e){const s=`${r}:${e}`;t.inputNames[n]=s}}t.inputs.push(a),a.children.push(t)}))})),0===Object.keys(l).length?c.forEach((e=>{const t=o[e];0===t.children.length&&u.push(t)})):Object.keys(l).forEach((e=>{const[t]=S(e),n=o[t];null!=n&&(n.signatureKey=l[e],u.push(n))})),Object.keys(p).length>0?Object.keys(p).forEach((e=>{const[t]=S(e),n=o[t];n&&(n.signatureKey=p[e],i.push(n))})):i=r;let h={};null!=e.library&&null!=e.library.function&&(h=e.library.function.reduce(((e,t)=>(e[t.signature.name]=this.mapFunction(t),e)),{}));const d={nodes:o,inputs:i,outputs:u,weights:s,placeholders:r,signature:t,functions:h};return a.length>0&&(d.initNodes=a),d}mapSignatureEntries(e){return Object.keys(e||{}).reduce(((t,n)=>(t[e[n].name]=n,t)),{})}mapNode(e){const t=w(e.op)||this.opMappers[e.op]||{};null==e.attr&&(e.attr={});const n={name:e.name,op:e.op,category:t.category,inputNames:(e.input||[]).map((e=>e.startsWith("^")?e.slice(1):e)),inputs:[],children:[],inputParams:{},attrParams:{},rawAttrs:e.attr,outputs:t.outputs};return null!=t.inputs&&(n.inputParams=t.inputs.reduce(((e,t)=>(e[t.name]={type:t.type,inputIndexStart:t.start,inputIndexEnd:t.end},e)),{})),null!=t.attrs&&(n.attrParams=t.attrs.reduce(((t,n)=>{const r=n.type;let s;switch(n.type){case"string":s=Y(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=Y(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"string[]":s=ue(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=ue(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"number":s=ee(e.attr,n.tfName,n.defaultValue||0),void 0===s&&n.tfDeprecatedName&&(s=ee(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"number[]":s=ie(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=ie(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"bool":s=J(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=J(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"bool[]":s=le(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=le(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"shape":s=oe(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=oe(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"shape[]":s=pe(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=pe(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"dtype":s=re(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=re(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"dtype[]":s=se(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=se(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"func":s=ne(e.attr,n.tfName,n.defaultValue),void 0===s&&n.tfDeprecatedName&&(s=ne(e.attr,n.tfDeprecatedName,n.defaultValue));break;case"tensor":case"tensors":break;default:throw new Error(`Unsupported param type: ${n.type} for op: ${e.op}`)}return t[n.name]={value:s,type:r},t}),{})),n}mapFunction(e){const t=e.nodeDef,n=[];let r={};null!=t&&(r=t.reduce(((e,t)=>(e[t.name]=this.mapNode(t),"Const"===t.op&&n.push(e[t.name]),e)),{}));const s=[],a=[];e.signature.inputArg.forEach((e=>{const[t]=S(e.name),n={name:t,op:"Placeholder",inputs:[],inputNames:[],category:"graph",inputParams:{},attrParams:{dtype:{value:te(e.type),type:"dtype"}},children:[]};n.signatureKey=e.name,s.push(n),r[t]=n}));Object.keys(r).forEach((e=>{const t=r[e];t.inputNames.forEach(((e,n)=>{const[s,,a]=S(e),o=r[s];if(null!=o.outputs){const e=o.outputs.indexOf(a);if(-1!==e){const r=`${s}:${e}`;t.inputNames[n]=r}}t.inputs.push(o),o.children.push(t)}))}));const o=e.ret;e.signature.outputArg.forEach((e=>{const[t,n]=S(o[e.name]),s=r[t];null!=s&&(s.defaultOutput=n,a.push(s))}));const i=this.mapArgsToSignature(e);return{nodes:r,inputs:s,outputs:a,weights:n,placeholders:[],signature:i}}mapArgsToSignature(e){return{methodName:e.signature.name,inputs:e.signature.inputArg.reduce(((e,t)=>(e[t.name]=this.mapArgToTensorInfo(t),e)),{}),outputs:e.signature.outputArg.reduce(((t,n)=>(t[n.name]=this.mapArgToTensorInfo(n,e.ret),t)),{})}}mapArgToTensorInfo(e,t){let n=e.name;return null!=t&&(n=t[n]),{name:n,dtype:e.type}}}function X(e,n){const r=Array.isArray(e)?String.fromCharCode.apply(null,e):function(e){const n=t().global;if("undefined"!=typeof n.atob)return n.atob(e);if("undefined"!=typeof Buffer)return new Buffer(e,"base64").toString();throw new Error("Unable to decode base64 in this environment. Missing built-in atob() or Buffer()")}(e);return n?r:r.toLowerCase()}function Y(e,t,n,r=!1){const s=e[t];return null!=s?X(s.s,r):n}function J(e,t,n){const r=e[t];return r?r.b:n}function ee(e,t,n){const r=e[t]||{},s=null!=r.i?r.i:null!=r.f?r.f:n;return"number"==typeof s?s:parseInt(s,10)}function te(e){switch("string"==typeof e&&(e=g[e]),e){case g.DT_FLOAT:case g.DT_HALF:return"float32";case g.DT_INT32:case g.DT_INT64:case g.DT_INT8:case g.DT_UINT8:return"int32";case g.DT_BOOL:return"bool";case g.DT_DOUBLE:return"float32";case g.DT_STRING:return"string";case g.DT_COMPLEX64:case g.DT_COMPLEX128:return"complex64";default:return null}}function ne(e,t,n){const r=e[t];return r&&r.func?r.func.name:n}function re(e,t,n){const r=e[t];return r&&r.type?te(r.type):n}function se(e,t,n){const r=e[t];return r&&r.list&&r.list.type?r.list.type.map((e=>te(e))):n}function ae(e){if(!e.unknownRank)return null!=e.dim?e.dim.map((e=>"number"==typeof e.size?e.size:parseInt(e.size,10))):[]}function oe(e,t,n){const r=e[t];return r&&r.shape?ae(r.shape):n}function ie(e,t,n){const r=e[t];return r?((r.list.f&&r.list.f.length?r.list.f:r.list.i)||[]).map((e=>"number"==typeof e?e:parseInt(e,10))):n}function ue(e,t,n,r=!1){const s=e[t];return s&&s.list&&s.list.s?s.list.s.map((e=>X(e,r))):n}function pe(e,t,n){const r=e[t];return r&&r.list&&r.list.shape?r.list.shape.map((e=>ae(e))):n}function le(e,t,n){const r=e[t];return r&&r.list&&r.list.b?r.list.b:n}class ce{constructor(e,t,n){this.node=e,this.tensorMap=t,this.context=n,this.inputs=[],this.attrs={},this.inputs=e.inputNames.map((e=>this.getInput(e))),null!=e.rawAttrs&&(this.attrs=Object.keys(e.rawAttrs).reduce(((e,t)=>(e[t]=this.getAttr(t),e)),{}))}getInput(e){return v(e,this.tensorMap,this.context)}getAttr(e,t){const n=this.node.rawAttrs[e];if(null!=n.tensor)return v(e,this.tensorMap,this.context);if(null!=n.i||null!=n.f)return ee(this.node.rawAttrs,e,t);if(null!=n.s)return Y(this.node.rawAttrs,e,t);if(null!=n.b)return J(this.node.rawAttrs,e,t);if(null!=n.shape)return oe(this.node.rawAttrs,e,t);if(null!=n.type)return re(this.node.rawAttrs,e,t);if(null!=n.list){if(null!=n.list.i||null!=n.list.f)return ie(this.node.rawAttrs,e,t);if(null!=n.list.s)return ue(this.node.rawAttrs,e,t);if(null!=n.list.shape)return pe(this.node.rawAttrs,e,t);if(null!=n.list.b)return le(this.node.rawAttrs,e,t);if(null!=n.list.type)return se(this.node.rawAttrs,e,t)}return t}}function he(e){throw new Error(`'${e}' not yet implemented or not found in the registry. This kernel may not be supported by the tfjs backend you have chosen`)}function de(e,t){if(!e)throw new Error("string"==typeof t?t:t())}function me(e,t,n=""){de(ge(e,t),(()=>n+` Shapes ${e} and ${t} must match`))}function fe(e){de(null!=e,(()=>"The input to the tensor constructor must be a non-null value."))}function ye(e){if(0===e.length)return 1;let t=e[0];for(let n=1;n<e.length;n++)t*=e[n];return t}function ge(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function be(e){return e%1==0}function xe(e,t){return t<=e.length?e:e+" ".repeat(t-e.length)}function Ne(e,t){const n=t.length;return de((e=null==e?t.map(((e,t)=>t)):[].concat(e)).every((e=>e>=-n&&e<n)),(()=>`All values in axis param must be in range [-${n}, ${n}) but got axis ${e}`)),de(e.every((e=>be(e))),(()=>`All values in axis param must be integers but got axis ${e}`)),e.map((e=>e<0?n+e:e))}function we(e,t){let n=null;if(null==e||"float32"===e)n=new Float32Array(t);else if("int32"===e)n=new Int32Array(t);else if("bool"===e)n=new Uint8Array(t);else{if("string"!==e)throw new Error(`Unknown data type ${e}`);n=new Array(t)}return n}function ke(e){if("float32"===e||"int32"===e)return 4;if("complex64"===e)return 8;if("bool"===e)return 1;throw new Error(`Unknown dtype ${e}`)}function Te(e){return"string"==typeof e||e instanceof String}function ve(e){return Array.isArray(e)?ve(e[0]):e instanceof Float32Array?"float32":e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray?"int32":"number"==typeof e?"float32":Te(e)?"string":function(e){return"boolean"==typeof e}(e)?"bool":"float32"}function _e(e){return!!(e&&e.constructor&&e.call&&e.apply)}function Se(e){const t=e.length;if(t<2)return[];const n=new Array(t-1);n[t-2]=e[t-1];for(let r=t-3;r>=0;--r)n[r]=n[r+1]*e[r+1];return n}function Ee(e,t,n,r=!1){const s=new Array;if(1===t.length){const a=t[0]*(r?2:1);for(let t=0;t<a;t++)s[t]=n[e+t]}else{const a=t[0],o=t.slice(1),i=o.reduce(((e,t)=>e*t))*(r?2:1);for(let t=0;t<a;t++)s[t]=Ee(e+t*i,o,n,r)}return s}function Ie(e,t,n=!1){if(0===e.length)return t[0];const r=e.reduce(((e,t)=>e*t))*(n?2:1);if(0===r)return[];if(r!==t.length)throw new Error(`[${e}] does not match the input size ${t.length}${n?" for a complex tensor":""}.`);return Ee(0,e,t,n)}function $e(e,t){const n=Ae(e,t);for(let e=0;e<n.length;e++)n[e]=1;return n}function Ae(e,t){if(null==t||"float32"===t||"complex64"===t)return new Float32Array(e);if("int32"===t)return new Int32Array(e);if("bool"===t)return new Uint8Array(e);throw new Error(`Unknown data type ${t}`)}function De(e){e.forEach((t=>{de(Number.isInteger(t)&&t>=0,(()=>`Tensor must have a shape comprised of positive integers but got shape [${e}].`))}))}function Oe(e){return e&&e.then&&"function"==typeof e.then}class Me{constructor(e){this.global=e,this.flags={},this.flagRegistry={},this.urlFlags={},this.getQueryParams=Ce,this.populateURLFlags()}setPlatform(e,t){null!=this.platform&&(Fe().getBool("IS_TEST")||Fe().getBool("PROD")||console.warn(`Platform ${this.platformName} has already been set. Overwriting the platform with ${e}.`)),this.platformName=e,this.platform=t}registerFlag(e,t,n){if(this.flagRegistry[e]={evaluationFn:t,setHook:n},null!=this.urlFlags[e]){const t=this.urlFlags[e];Fe().getBool("IS_TEST")||Fe().getBool("PROD")||console.warn(`Setting feature override from URL ${e}: ${t}.`),this.set(e,t)}}async getAsync(e){return e in this.flags||(this.flags[e]=await this.evaluateFlag(e)),this.flags[e]}get(e){if(e in this.flags)return this.flags[e];const t=this.evaluateFlag(e);if(Oe(t))throw new Error(`Flag ${e} cannot be synchronously evaluated. Please use getAsync() instead.`);return this.flags[e]=t,this.flags[e]}getNumber(e){return this.get(e)}getBool(e){return this.get(e)}getString(e){return this.get(e)}getFlags(){return this.flags}get features(){return this.flags}set(e,t){if(null==this.flagRegistry[e])throw new Error(`Cannot set flag ${e} as it has not been registered.`);this.flags[e]=t,null!=this.flagRegistry[e].setHook&&this.flagRegistry[e].setHook(t)}evaluateFlag(e){if(null==this.flagRegistry[e])throw new Error(`Cannot evaluate flag '${e}': no evaluation function found.`);return this.flagRegistry[e].evaluationFn()}setFlags(e){this.flags=Object.assign({},e)}reset(){this.flags={},this.urlFlags={},this.populateURLFlags()}populateURLFlags(){if("undefined"==typeof this.global||"undefined"==typeof this.global.location||"undefined"==typeof this.global.location.search)return;const e=this.getQueryParams(this.global.location.search);if("tfjsflags"in e){e.tfjsflags.split(",").forEach((e=>{const[t,n]=e.split(":");this.urlFlags[t]=function(e,t){const n=t.toLowerCase();return"true"===n||"false"===n?"true"===n:""+ +n===n?+n:t}(0,n)}))}}}function Ce(e){const t={};return e.replace(/[?&]([^=?&]+)(?:=([^&]*))?/g,((e,...n)=>(function(e,t,n){e[decodeURIComponent(t)]=decodeURIComponent(n||"")}(t,n[0],n[1]),n.join("=")))),t}function Fe(){return ze}let Re,ze=null;function Le(){if(null==Re){let e;if("undefined"!=typeof window)e=window;else if("undefined"!=typeof global)e=global;else if("undefined"!=typeof process)e=process;else{if("undefined"==typeof self)throw new Error("Could not find a global object");e=self}Re=e}return Re}function Ve(e,t){const n=function(){const e=Le();return null==e._tfGlobals&&(e._tfGlobals=new Map),e._tfGlobals}();if(n.has(e))return n.get(e);{const r=t();return n.set(e,r),n.get(e)}}function Be(...e){Fe().getBool("IS_TEST")||Fe().getBool("PROD")||console.warn(...e)}const Pe=Ve("kernelRegistry",(()=>new Map)),Ke=Ve("gradRegistry",(()=>new Map));function qe(e,t){const n=function(e,t){return`${t}_${e}`}(e,t);return Pe.get(n)}function Ue(e){return Ke.get(e)}function We(e){const t=Pe.entries(),n=[];for(;;){const{done:r,value:s}=t.next();if(r)break;const[a,o]=s,[i]=a.split("_");i===e&&n.push(o)}return n}var je="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Ge(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function He(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var n=function e(){if(this instanceof e){var n=[null];n.push.apply(n,arguments);var r=Function.bind.apply(t,n);return new r}return t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(n,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),n}var Ze=Xe,Qe=null;try{Qe=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(e){}function Xe(e,t,n){this.low=0|e,this.high=0|t,this.unsigned=!!n}function Ye(e){return!0===(e&&e.__isLong__)}Xe.prototype.__isLong__,Object.defineProperty(Xe.prototype,"__isLong__",{value:!0}),Xe.isLong=Ye;var Je={},et={};function tt(e,t){var n,r,s;return t?(s=0<=(e>>>=0)&&e<256)&&(r=et[e])?r:(n=rt(e,(0|e)<0?-1:0,!0),s&&(et[e]=n),n):(s=-128<=(e|=0)&&e<128)&&(r=Je[e])?r:(n=rt(e,e<0?-1:0,!1),s&&(Je[e]=n),n)}function nt(e,t){if(isNaN(e))return t?ht:ct;if(t){if(e<0)return ht;if(e>=ut)return gt}else{if(e<=-pt)return bt;if(e+1>=pt)return yt}return e<0?nt(-e,t).neg():rt(e%it|0,e/it|0,t)}function rt(e,t,n){return new Xe(e,t,n)}Xe.fromInt=tt,Xe.fromNumber=nt,Xe.fromBits=rt;var st=Math.pow;function at(e,t,n){if(0===e.length)throw Error("empty string");if("NaN"===e||"Infinity"===e||"+Infinity"===e||"-Infinity"===e)return ct;if("number"==typeof t?(n=t,t=!1):t=!!t,(n=n||10)<2||36<n)throw RangeError("radix");var r;if((r=e.indexOf("-"))>0)throw Error("interior hyphen");if(0===r)return at(e.substring(1),t,n).neg();for(var s=nt(st(n,8)),a=ct,o=0;o<e.length;o+=8){var i=Math.min(8,e.length-o),u=parseInt(e.substring(o,o+i),n);if(i<8){var p=nt(st(n,i));a=a.mul(p).add(nt(u))}else a=(a=a.mul(s)).add(nt(u))}return a.unsigned=t,a}function ot(e,t){return"number"==typeof e?nt(e,t):"string"==typeof e?at(e,t):rt(e.low,e.high,"boolean"==typeof t?t:e.unsigned)}Xe.fromString=at,Xe.fromValue=ot;var it=4294967296,ut=it*it,pt=ut/2,lt=tt(1<<24),ct=tt(0);Xe.ZERO=ct;var ht=tt(0,!0);Xe.UZERO=ht;var dt=tt(1);Xe.ONE=dt;var mt=tt(1,!0);Xe.UONE=mt;var ft=tt(-1);Xe.NEG_ONE=ft;var yt=rt(-1,2147483647,!1);Xe.MAX_VALUE=yt;var gt=rt(-1,-1,!0);Xe.MAX_UNSIGNED_VALUE=gt;var bt=rt(0,-2147483648,!1);Xe.MIN_VALUE=bt;var xt=Xe.prototype;xt.toInt=function(){return this.unsigned?this.low>>>0:this.low},xt.toNumber=function(){return this.unsigned?(this.high>>>0)*it+(this.low>>>0):this.high*it+(this.low>>>0)},xt.toString=function(e){if((e=e||10)<2||36<e)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(this.eq(bt)){var t=nt(e),n=this.div(t),r=n.mul(t).sub(this);return n.toString(e)+r.toInt().toString(e)}return"-"+this.neg().toString(e)}for(var s=nt(st(e,6),this.unsigned),a=this,o="";;){var i=a.div(s),u=(a.sub(i.mul(s)).toInt()>>>0).toString(e);if((a=i).isZero())return u+o;for(;u.length<6;)u="0"+u;o=""+u+o}},xt.getHighBits=function(){return this.high},xt.getHighBitsUnsigned=function(){return this.high>>>0},xt.getLowBits=function(){return this.low},xt.getLowBitsUnsigned=function(){return this.low>>>0},xt.getNumBitsAbs=function(){if(this.isNegative())return this.eq(bt)?64:this.neg().getNumBitsAbs();for(var e=0!=this.high?this.high:this.low,t=31;t>0&&0==(e&1<<t);t--);return 0!=this.high?t+33:t+1},xt.isZero=function(){return 0===this.high&&0===this.low},xt.eqz=xt.isZero,xt.isNegative=function(){return!this.unsigned&&this.high<0},xt.isPositive=function(){return this.unsigned||this.high>=0},xt.isOdd=function(){return 1==(1&this.low)},xt.isEven=function(){return 0==(1&this.low)},xt.equals=function(e){return Ye(e)||(e=ot(e)),(this.unsigned===e.unsigned||this.high>>>31!=1||e.high>>>31!=1)&&(this.high===e.high&&this.low===e.low)},xt.eq=xt.equals,xt.notEquals=function(e){return!this.eq(e)},xt.neq=xt.notEquals,xt.ne=xt.notEquals,xt.lessThan=function(e){return this.comp(e)<0},xt.lt=xt.lessThan,xt.lessThanOrEqual=function(e){return this.comp(e)<=0},xt.lte=xt.lessThanOrEqual,xt.le=xt.lessThanOrEqual,xt.greaterThan=function(e){return this.comp(e)>0},xt.gt=xt.greaterThan,xt.greaterThanOrEqual=function(e){return this.comp(e)>=0},xt.gte=xt.greaterThanOrEqual,xt.ge=xt.greaterThanOrEqual,xt.compare=function(e){if(Ye(e)||(e=ot(e)),this.eq(e))return 0;var t=this.isNegative(),n=e.isNegative();return t&&!n?-1:!t&&n?1:this.unsigned?e.high>>>0>this.high>>>0||e.high===this.high&&e.low>>>0>this.low>>>0?-1:1:this.sub(e).isNegative()?-1:1},xt.comp=xt.compare,xt.negate=function(){return!this.unsigned&&this.eq(bt)?bt:this.not().add(dt)},xt.neg=xt.negate,xt.add=function(e){Ye(e)||(e=ot(e));var t=this.high>>>16,n=65535&this.high,r=this.low>>>16,s=65535&this.low,a=e.high>>>16,o=65535&e.high,i=e.low>>>16,u=0,p=0,l=0,c=0;return l+=(c+=s+(65535&e.low))>>>16,p+=(l+=r+i)>>>16,u+=(p+=n+o)>>>16,u+=t+a,rt((l&=65535)<<16|(c&=65535),(u&=65535)<<16|(p&=65535),this.unsigned)},xt.subtract=function(e){return Ye(e)||(e=ot(e)),this.add(e.neg())},xt.sub=xt.subtract,xt.multiply=function(e){if(this.isZero())return ct;if(Ye(e)||(e=ot(e)),Qe)return rt(Qe.mul(this.low,this.high,e.low,e.high),Qe.get_high(),this.unsigned);if(e.isZero())return ct;if(this.eq(bt))return e.isOdd()?bt:ct;if(e.eq(bt))return this.isOdd()?bt:ct;if(this.isNegative())return e.isNegative()?this.neg().mul(e.neg()):this.neg().mul(e).neg();if(e.isNegative())return this.mul(e.neg()).neg();if(this.lt(lt)&&e.lt(lt))return nt(this.toNumber()*e.toNumber(),this.unsigned);var t=this.high>>>16,n=65535&this.high,r=this.low>>>16,s=65535&this.low,a=e.high>>>16,o=65535&e.high,i=e.low>>>16,u=65535&e.low,p=0,l=0,c=0,h=0;return c+=(h+=s*u)>>>16,l+=(c+=r*u)>>>16,c&=65535,l+=(c+=s*i)>>>16,p+=(l+=n*u)>>>16,l&=65535,p+=(l+=r*i)>>>16,l&=65535,p+=(l+=s*o)>>>16,p+=t*u+n*i+r*o+s*a,rt((c&=65535)<<16|(h&=65535),(p&=65535)<<16|(l&=65535),this.unsigned)},xt.mul=xt.multiply,xt.divide=function(e){if(Ye(e)||(e=ot(e)),e.isZero())throw Error("division by zero");var t,n,r;if(Qe)return this.unsigned||-2147483648!==this.high||-1!==e.low||-1!==e.high?rt((this.unsigned?Qe.div_u:Qe.div_s)(this.low,this.high,e.low,e.high),Qe.get_high(),this.unsigned):this;if(this.isZero())return this.unsigned?ht:ct;if(this.unsigned){if(e.unsigned||(e=e.toUnsigned()),e.gt(this))return ht;if(e.gt(this.shru(1)))return mt;r=ht}else{if(this.eq(bt))return e.eq(dt)||e.eq(ft)?bt:e.eq(bt)?dt:(t=this.shr(1).div(e).shl(1)).eq(ct)?e.isNegative()?dt:ft:(n=this.sub(e.mul(t)),r=t.add(n.div(e)));if(e.eq(bt))return this.unsigned?ht:ct;if(this.isNegative())return e.isNegative()?this.neg().div(e.neg()):this.neg().div(e).neg();if(e.isNegative())return this.div(e.neg()).neg();r=ct}for(n=this;n.gte(e);){t=Math.max(1,Math.floor(n.toNumber()/e.toNumber()));for(var s=Math.ceil(Math.log(t)/Math.LN2),a=s<=48?1:st(2,s-48),o=nt(t),i=o.mul(e);i.isNegative()||i.gt(n);)i=(o=nt(t-=a,this.unsigned)).mul(e);o.isZero()&&(o=dt),r=r.add(o),n=n.sub(i)}return r},xt.div=xt.divide,xt.modulo=function(e){return Ye(e)||(e=ot(e)),Qe?rt((this.unsigned?Qe.rem_u:Qe.rem_s)(this.low,this.high,e.low,e.high),Qe.get_high(),this.unsigned):this.sub(this.div(e).mul(e))},xt.mod=xt.modulo,xt.rem=xt.modulo,xt.not=function(){return rt(~this.low,~this.high,this.unsigned)},xt.and=function(e){return Ye(e)||(e=ot(e)),rt(this.low&e.low,this.high&e.high,this.unsigned)},xt.or=function(e){return Ye(e)||(e=ot(e)),rt(this.low|e.low,this.high|e.high,this.unsigned)},xt.xor=function(e){return Ye(e)||(e=ot(e)),rt(this.low^e.low,this.high^e.high,this.unsigned)},xt.shiftLeft=function(e){return Ye(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?rt(this.low<<e,this.high<<e|this.low>>>32-e,this.unsigned):rt(0,this.low<<e-32,this.unsigned)},xt.shl=xt.shiftLeft,xt.shiftRight=function(e){return Ye(e)&&(e=e.toInt()),0==(e&=63)?this:e<32?rt(this.low>>>e|this.high<<32-e,this.high>>e,this.unsigned):rt(this.high>>e-32,this.high>=0?0:-1,this.unsigned)},xt.shr=xt.shiftRight,xt.shiftRightUnsigned=function(e){if(Ye(e)&&(e=e.toInt()),0===(e&=63))return this;var t=this.high;return e<32?rt(this.low>>>e|t<<32-e,t>>>e,this.unsigned):rt(32===e?t:t>>>e-32,0,this.unsigned)},xt.shru=xt.shiftRightUnsigned,xt.shr_u=xt.shiftRightUnsigned,xt.toSigned=function(){return this.unsigned?rt(this.low,this.high,!1):this},xt.toUnsigned=function(){return this.unsigned?this:rt(this.low,this.high,!0)},xt.toBytes=function(e){return e?this.toBytesLE():this.toBytesBE()},xt.toBytesLE=function(){var e=this.high,t=this.low;return[255&t,t>>>8&255,t>>>16&255,t>>>24,255&e,e>>>8&255,e>>>16&255,e>>>24]},xt.toBytesBE=function(){var e=this.high,t=this.low;return[e>>>24,e>>>16&255,e>>>8&255,255&e,t>>>24,t>>>16&255,t>>>8&255,255&t]},Xe.fromBytes=function(e,t,n){return n?Xe.fromBytesLE(e,t):Xe.fromBytesBE(e,t)},Xe.fromBytesLE=function(e,t){return new Xe(e[0]|e[1]<<8|e[2]<<16|e[3]<<24,e[4]|e[5]<<8|e[6]<<16|e[7]<<24,t)},Xe.fromBytesBE=function(e,t){return new Xe(e[4]<<24|e[5]<<16|e[6]<<8|e[7],e[0]<<24|e[1]<<16|e[2]<<8|e[3],t)};var Nt=Ge(Ze);const wt=Nt||y({__proto__:null,default:Nt},[Ze]);function kt(e){return wt.fromString(e,!0,16)}function Tt(e,t){if("string"===t)throw new Error("Cannot convert a string[] to a TypedArray");if(Array.isArray(e)&&(e=Et(e)),Fe().getBool("DEBUG")&&function(e,t){for(let n=0;n<e.length;n++){const r=e[n];if(isNaN(r)||!isFinite(r))throw Error(`A tensor of type ${t} being uploaded contains ${r}.`)}}(e,t),function(e,t){return e instanceof Float32Array&&"float32"===t||e instanceof Int32Array&&"int32"===t||e instanceof Uint8Array&&"bool"===t}(e,t))return e;if(null==t||"float32"===t||"complex64"===t)return new Float32Array(e);if("int32"===t)return new Int32Array(e);if("bool"===t){const t=new Uint8Array(e.length);for(let n=0;n<t.length;++n)0!==Math.round(e[n])&&(t[n]=1);return t}throw new Error(`Unknown data type ${t}`)}function vt(){return Fe().platform.now()}function _t(e,t="utf-8"){return t=t||"utf-8",Fe().platform.decode(e,t)}function St(e){return null!=Fe().platform.isTypedArray?Fe().platform.isTypedArray(e):function(e){return e instanceof Float32Array||e instanceof Int32Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray}(e)}function Et(e,t=[],n=!1){if(null==t&&(t=[]),"boolean"==typeof e||"number"==typeof e||"string"==typeof e||Oe(e)||null==e||St(e)&&n)t.push(e);else if(Array.isArray(e)||St(e))for(let r=0;r<e.length;++r)Et(e[r],t,n);else{let r=-1;for(const t of Object.keys(e))/^([1-9]+[0-9]*|0)$/.test(t)&&(r=Math.max(r,Number(t)));for(let s=0;s<=r;s++)Et(e[s],t,n)}return t}kt("c3a5c85c97cb3127"),kt("b492b66fbe98f273"),kt("9ae16a3b2f90404f");class It{constructor(e,t){this.backendTimer=e,this.logger=t,null==t&&(this.logger=new At)}profileKernel(e,t,n){let r;const s=()=>{r=n()};let a;const o=vt();if(this.backendTimer.timerAvailable())a=this.backendTimer.time(s);else{s();for(const e of r)e.dataSync();a=Promise.resolve({kernelMs:vt()-o})}if(Fe().getBool("CHECK_COMPUTATION_FOR_ERRORS"))for(let t=0;t<r.length;t++){const n=r[t];n.data().then((t=>{$t(t,n.dtype,e)}))}return{kernelName:e,outputs:r,inputs:t,timeMs:a.then((e=>e.kernelMs)),extraInfo:a.then((e=>null!=e.getExtraProfileInfo?e.getExtraProfileInfo():""))}}logKernelProfile(e){const{kernelName:t,outputs:n,timeMs:r,inputs:s,extraInfo:a}=e;n.forEach((e=>{Promise.all([e.data(),r,a]).then((n=>{this.logger.logKernelProfile(t,e,n[0],n[1],s,n[2])}))}))}}function $t(e,t,n){if("float32"!==t)return!1;for(let t=0;t<e.length;t++){const r=e[t];if(isNaN(r)||!isFinite(r))return console.warn(`Found ${r} in the result of '${n}'`),!0}return!1}class At{logKernelProfile(e,t,n,r,s,a){const o="number"==typeof r?xe(`${r}ms`,9):r.error,i=xe(e,25),u=t.rank,p=t.size,l=xe(t.shape.toString(),14);let c="";for(const e in s){const n=s[e];if(null!=n){const r=n.shape||t.shape,s=r.length;c+=`${e}: ${s}D ${s>0?r:""} `}}console.log(`%c${i}\t%c${o}\t%c${u}D ${l}\t%c${p}\t%c${c}\t%c${a}`,"font-weight:bold","color:red","color:blue","color: orange","color: green","color: steelblue")}}function Dt(e,t,n,r){const s=Se(t),a=function(e,t,n,r){const s=ye(t),a=r[r.length-1],o=new Array(a).fill(0),i=t.length,u="complex64"===n?Ft(e):e;if(i>1)for(let e=0;e<s/a;e++){const t=e*a;for(let e=0;e<a;e++)o[e]=Math.max(o[e],Ot(u[t+e],0,n).length)}return o}(e,t,n,s),o=t.length,i=Ct(e,t,n,s,a),u=["Tensor"];return r&&(u.push(`  dtype: ${n}`),u.push(`  rank: ${o}`),u.push(`  shape: [${t}]`),u.push("  values:")),u.push(i.map((e=>"    "+e)).join("\n")),u.join("\n")}function Ot(e,t,n){let r;return r=Array.isArray(e)?`${parseFloat(e[0].toFixed(7))} + ${parseFloat(e[1].toFixed(7))}j`:Te(e)?`'${e}'`:"bool"===n?Mt(e):parseFloat(e.toFixed(7)).toString(),xe(r,t)}function Mt(e){return 0===e?"false":"true"}function Ct(e,t,n,r,s,a=!0){const o="complex64"===n?2:1,i=t[0],u=t.length;if(0===u){if("complex64"===n){return[Ot(Ft(e)[0],0,n)]}return"bool"===n?[Mt(e[0])]:[e[0].toString()]}if(1===u){if(i>20){const t=3*o;let r=Array.from(e.slice(0,t)),a=Array.from(e.slice((i-3)*o,i*o));return"complex64"===n&&(r=Ft(r),a=Ft(a)),["["+r.map(((e,t)=>Ot(e,s[t],n))).join(", ")+", ..., "+a.map(((e,t)=>Ot(e,s[i-3+t],n))).join(", ")+"]"]}return["["+("complex64"===n?Ft(e):Array.from(e)).map(((e,t)=>Ot(e,s[t],n))).join(", ")+"]"]}const p=t.slice(1),l=r.slice(1),c=r[0]*o,h=[];if(i>20){for(let t=0;t<3;t++){const r=t*c,a=r+c;h.push(...Ct(e.slice(r,a),p,n,l,s,!1))}h.push("...");for(let t=i-3;t<i;t++){const r=t*c,a=r+c;h.push(...Ct(e.slice(r,a),p,n,l,s,t===i-1))}}else for(let t=0;t<i;t++){const r=t*c,a=r+c;h.push(...Ct(e.slice(r,a),p,n,l,s,t===i-1))}const d=2===u?",":"";h[0]="["+(i>0?h[0]+d:"");for(let e=1;e<h.length-1;e++)h[e]=" "+h[e]+d;let m=",\n";for(let e=2;e<u;e++)m+="\n";return h[h.length-1]=" "+h[h.length-1]+"]"+(a?"":m),h}function Ft(e){const t=[];for(let n=0;n<e.length;n+=2)t.push([e[n],e[n+1]]);return t}class Rt{constructor(e,t,n){if(this.dtype=t,this.shape=e.slice(),this.size=ye(e),null!=n){const e=n.length;de(e===this.size,(()=>`Length of values '${e}' does not match the size inferred by the shape '${this.size}'.`))}if("complex64"===t)throw new Error("complex64 dtype TensorBuffers are not supported. Please create a TensorBuffer for the real and imaginary parts separately and call tf.complex(real, imag).");this.values=n||we(t,this.size),this.strides=Se(e)}set(e,...t){0===t.length&&(t=[0]),de(t.length===this.rank,(()=>`The number of provided coordinates (${t.length}) must match the rank (${this.rank})`));const n=this.locToIndex(t);this.values[n]=e}get(...e){0===e.length&&(e=[0]);let t=0;for(const n of e){if(n<0||n>=this.shape[t]){const t=`Requested out of range element at ${e}.   Buffer shape=${this.shape}`;throw new Error(t)}t++}let n=e[e.length-1];for(let t=0;t<e.length-1;++t)n+=this.strides[t]*e[t];return this.values[n]}locToIndex(e){if(0===this.rank)return 0;if(1===this.rank)return e[0];let t=e[e.length-1];for(let n=0;n<e.length-1;++n)t+=this.strides[n]*e[n];return t}indexToLoc(e){if(0===this.rank)return[];if(1===this.rank)return[e];const t=new Array(this.shape.length);for(let n=0;n<t.length-1;++n)t[n]=Math.floor(e/this.strides[n]),e-=t[n]*this.strides[n];return t[t.length-1]=e,t}get rank(){return this.shape.length}toTensor(){return zt().makeTensor(this.values,this.shape,this.dtype)}}let zt=null;class Lt{constructor(e,t,n,r){this.kept=!1,this.isDisposedInternal=!1,this.shape=e.slice(),this.dtype=t||"float32",this.size=ye(e),this.strides=Se(e),this.dataId=n,this.id=r,this.rankType=this.rank<5?this.rank.toString():"higher"}get rank(){return this.shape.length}async buffer(){const e=await this.data();return null.buffer(this.shape,this.dtype,e)}bufferSync(){return null.buffer(this.shape,this.dtype,this.dataSync())}async array(){const e=await this.data();return Ie(this.shape,e,"complex64"===this.dtype)}arraySync(){return Ie(this.shape,this.dataSync(),"complex64"===this.dtype)}async data(){this.throwIfDisposed();const e=zt().read(this.dataId);if("string"===this.dtype){const t=await e;try{return t.map((e=>_t(e)))}catch(e){throw new Error("Failed to decode the string bytes into utf-8. To get the original bytes, call tensor.bytes().")}}return e}dataToGPU(e){return this.throwIfDisposed(),zt().readToGPU(this.dataId,e)}dataSync(){this.throwIfDisposed();const e=zt().readSync(this.dataId);if("string"===this.dtype)try{return e.map((e=>_t(e)))}catch(e){throw new Error("Failed to decode the string bytes into utf-8. To get the original bytes, call tensor.bytes().")}return e}async bytes(){this.throwIfDisposed();const e=await zt().read(this.dataId);return"string"===this.dtype?e:new Uint8Array(e.buffer)}dispose(){this.isDisposed||(this.kerasMask&&this.kerasMask.dispose(),zt().disposeTensor(this),this.isDisposedInternal=!0)}get isDisposed(){return this.isDisposedInternal}throwIfDisposed(){if(this.isDisposed)throw new Error("Tensor is disposed.")}print(e=!1){return null.print(this,e)}clone(){return this.throwIfDisposed(),null.clone(this)}toString(e=!1){return Dt(this.dataSync(),this.shape,this.dtype,e)}cast(e){return this.throwIfDisposed(),null.cast(this,e)}variable(e=!0,t,n){return this.throwIfDisposed(),zt().makeVariable(this,e,t,n)}}function Vt(){return Ve("Tensor",(()=>Lt))}Object.defineProperty(Lt,Symbol.hasInstance,{value:e=>!!e&&null!=e.data&&null!=e.dataSync&&null!=e.throwIfDisposed}),Vt();class Bt extends Lt{constructor(e,t,n,r){super(e.shape,e.dtype,e.dataId,r),this.trainable=t,this.name=n}assign(e){if(e.dtype!==this.dtype)throw new Error(`dtype of the new value (${e.dtype}) and previous value (${this.dtype}) must match`);if(!ge(e.shape,this.shape))throw new Error(`shape of the new value (${e.shape}) and previous value (${this.shape}) must match`);zt().disposeTensor(this),this.dataId=e.dataId,zt().incRef(this,null)}dispose(){zt().disposeVariable(this),this.isDisposedInternal=!0}}var Pt,Kt,qt,Ut,Wt;Object.defineProperty(Bt,Symbol.hasInstance,{value:e=>e instanceof Lt&&null!=e.assign&&e.assign instanceof Function}),function(e){e.R0="R0",e.R1="R1",e.R2="R2",e.R3="R3",e.R4="R4",e.R5="R5",e.R6="R6"}(Pt||(Pt={})),function(e){e.float32="float32",e.int32="int32",e.bool="int32",e.complex64="complex64"}(Kt||(Kt={})),function(e){e.float32="float32",e.int32="int32",e.bool="bool",e.complex64="complex64"}(qt||(qt={})),function(e){e.float32="float32",e.int32="float32",e.bool="float32",e.complex64="complex64"}(Ut||(Ut={})),function(e){e.float32="complex64",e.int32="complex64",e.bool="complex64",e.complex64="complex64"}(Wt||(Wt={}));const jt={float32:Ut,int32:Kt,bool:qt,complex64:Wt};function Gt(e){return null!=e&&"object"==typeof e&&"texture"in e&&e.texture instanceof WebGLTexture}function Ht(e){return"undefined"!=typeof GPUBuffer&&null!=e&&"object"==typeof e&&"buffer"in e&&e.buffer instanceof GPUBuffer}function Zt(e,t){if(e.dtype===t.dtype)return[e,t];const n=function(e,t){if("string"===e||"string"===t){if("string"===e&&"string"===t)return"string";throw new Error(`Can not upcast ${e} with ${t}`)}return jt[e][t]}(e.dtype,t.dtype);return[e.cast(n),t.cast(n)]}function Qt(e){const t=[];return Xt(e,t,new Set),t}function Xt(e,t,n){if(null==e)return;if(e instanceof Lt)return void t.push(e);if(r=e,!Array.isArray(r)&&"object"!=typeof r)return;var r;const s=e;for(const e in s){const r=s[e];n.has(r)||(n.add(r),Xt(r,t,n))}}function Yt(e){return null!=e.kernelName}class Jt{constructor(){this.registeredVariables={},this.nextTapeNodeId=0,this.numBytes=0,this.numTensors=0,this.numStringTensors=0,this.numDataBuffers=0,this.gradientDepth=0,this.kernelDepth=0,this.scopeStack=[],this.numDataMovesStack=[],this.nextScopeId=0,this.tensorInfo=new WeakMap,this.profiling=!1,this.activeProfile={newBytes:0,newTensors:0,peakBytes:0,kernels:[],result:null,get kernelNames(){return Array.from(new Set(this.kernels.map((e=>e.name))))}}}dispose(){for(const e in this.registeredVariables)this.registeredVariables[e].dispose()}}class en{constructor(e){this.ENV=e,this.registry={},this.registryFactory={},this.pendingBackendInitId=0,this.state=new Jt}async ready(){if(null!=this.pendingBackendInit)return this.pendingBackendInit.then((()=>{}));if(null!=this.backendInstance)return;const e=this.getSortedBackends();for(let t=0;t<e.length;t++){const n=e[t];if(await this.initializeBackend(n).success)return void await this.setBackend(n)}throw new Error("Could not initialize any backends, all backend initializations failed.")}get backend(){if(null!=this.pendingBackendInit)throw new Error(`Backend '${this.backendName}' has not yet been initialized. Make sure to await tf.ready() or await tf.setBackend() before calling other methods`);if(null==this.backendInstance){const{name:e,asyncInit:t}=this.initializeBackendsAndReturnBest();if(t)throw new Error(`The highest priority backend '${e}' has not yet been initialized. Make sure to await tf.ready() or await tf.setBackend() before calling other methods`);this.setBackend(e)}return this.backendInstance}backendNames(){return Object.keys(this.registryFactory)}findBackend(e){if(!(e in this.registry)){if(!(e in this.registryFactory))return null;{const{asyncInit:t}=this.initializeBackend(e);if(t)return null}}return this.registry[e]}findBackendFactory(e){return e in this.registryFactory?this.registryFactory[e].factory:null}registerBackend(e,t,n=1){return e in this.registryFactory?(Be(`${e} backend was already registered. Reusing existing backend factory.`),!1):(this.registryFactory[e]={factory:t,priority:n},!0)}async setBackend(e){if(null==this.registryFactory[e])throw new Error(`Backend name '${e}' not found in registry`);if(this.backendName=e,null==this.registry[e]){this.backendInstance=null;const{success:t,asyncInit:n}=this.initializeBackend(e);if(!(n?await t:t))return!1}return this.backendInstance=this.registry[e],this.setupRegisteredKernels(),this.profiler=new It(this.backendInstance),!0}setupRegisteredKernels(){We(this.backendName).forEach((e=>{null!=e.setupFunc&&e.setupFunc(this.backendInstance)}))}disposeRegisteredKernels(e){We(e).forEach((t=>{null!=t.disposeFunc&&t.disposeFunc(this.registry[e])}))}initializeBackend(e){const t=this.registryFactory[e];if(null==t)throw new Error(`Cannot initialize backend ${e}, no registration found.`);try{const n=t.factory();if(!n||n instanceof class{refCount(e){return he("refCount")}incRef(e){return he("incRef")}timerAvailable(){return!0}time(e){return he("time")}read(e){return he("read")}readSync(e){return he("readSync")}readToGPU(e,t){return he("readToGPU")}numDataIds(){return he("numDataIds")}disposeData(e,t){return he("disposeData")}write(e,t,n){return he("write")}move(e,t,n,r,s){return he("move")}createTensorFromGPUData(e,t,n){return he("createTensorFromGPUData")}memory(){return he("memory")}floatPrecision(){return he("floatPrecision")}epsilon(){return 32===this.floatPrecision()?1e-7:1e-4}dispose(){return he("dispose")}}||"function"!=typeof n.then)return this.registry[e]=n,{success:!0,asyncInit:!1};{const t=++this.pendingBackendInitId,r=n.then((n=>!(t<this.pendingBackendInitId)&&(this.registry[e]=n,this.pendingBackendInit=null,!0))).catch((n=>(t<this.pendingBackendInitId||(this.pendingBackendInit=null,Be(`Initialization of backend ${e} failed`),Be(n.stack||n.message)),!1)));return this.pendingBackendInit=r,{success:r,asyncInit:!0}}}catch(t){return Be(`Initialization of backend ${e} failed`),Be(t.stack||t.message),{success:!1,asyncInit:!1}}}removeBackend(e){if(!(e in this.registryFactory))throw new Error(`${e} backend not found in registry`);this.backendName===e&&null!=this.pendingBackendInit&&this.pendingBackendInitId++,e in this.registry&&(this.disposeRegisteredKernels(e),this.registry[e].dispose(),delete this.registry[e]),delete this.registryFactory[e],this.backendName===e&&(this.pendingBackendInit=null,this.backendName=null,this.backendInstance=null)}getSortedBackends(){if(0===Object.keys(this.registryFactory).length)throw new Error("No backend found in registry.");return Object.keys(this.registryFactory).sort(((e,t)=>this.registryFactory[t].priority-this.registryFactory[e].priority))}initializeBackendsAndReturnBest(){const e=this.getSortedBackends();for(let t=0;t<e.length;t++){const n=e[t],{success:r,asyncInit:s}=this.initializeBackend(n);if(s||r)return{name:n,asyncInit:s}}throw new Error("Could not initialize any backends, all backend initializations failed.")}moveData(e,t){const n=this.state.tensorInfo.get(t),r=n.backend,s=this.readSync(t),a=r.refCount(t);r.disposeData(t,!0),n.backend=e,e.move(t,s,n.shape,n.dtype,a),this.shouldCheckForMemLeaks()&&this.state.numDataMovesStack[this.state.numDataMovesStack.length-1]++}tidy(e,t){let n,r=null;if(null==t){if("function"!=typeof e)throw new Error("Please provide a function to tidy()");t=e}else{if("string"!=typeof e&&!(e instanceof String))throw new Error("When calling with two arguments, the first argument to tidy() must be a string");if("function"!=typeof t)throw new Error("When calling with two arguments, the 2nd argument to tidy() must be a function");r=e}return this.scopedRun((()=>this.startScope(r)),(()=>this.endScope(n)),(()=>(n=t(),n instanceof Promise&&console.error("Cannot return a Promise inside of tidy."),n)))}scopedRun(e,t,n){e();try{const e=n();return t(),e}catch(e){throw t(),e}}nextTensorId(){return en.nextTensorId++}nextVariableId(){return en.nextVariableId++}clone(e){const t=tn.runKernel("Identity",{x:e}),n={x:e};return this.addTapeNode(this.state.activeScope.name,n,[t],(e=>({x:()=>{const t={x:e},n={dtype:"float32"};return tn.runKernel("Cast",t,n)}})),[],{}),t}runKernel(e,t,n){null==this.backendName&&this.backend;if(!(null!=qe(e,this.backendName)))throw new Error(`Kernel '${e}' not registered for backend '${this.backendName}'`);return this.runKernelFunc({kernelName:e,inputs:t,attrs:n})}shouldCheckForMemLeaks(){return this.ENV.getBool("IS_TEST")}checkKernelForMemLeak(e,t,n){const r=this.backend.numDataIds();let s=0;n.forEach((e=>{s+="complex64"===e.dtype?3:1}));const a=this.state.numDataMovesStack[this.state.numDataMovesStack.length-1],o=r-t-s-a;if(o>0)throw new Error(`Backend '${this.backendName}' has an internal memory leak (${o} data ids) after running '${e}'`)}runKernelFunc(e){let t,n=[];const r=this.isTapeOn(),s=this.state.numBytes,a=this.state.numTensors;let o,i;this.shouldCheckForMemLeaks()&&this.state.numDataMovesStack.push(0),null==this.backendName&&this.backend;const u=Yt(e)?e.kernelName:null!=this.state.activeScope?this.state.activeScope.name:"";if(Yt(e)){const{kernelName:t,inputs:s,attrs:a}=e;null==this.backendName&&this.backend;const u=qe(t,this.backendName);de(null!=u,(()=>`Cannot find registered kernel '${t}' for backend '${this.backendName}'`)),o=()=>{const e=this.backend.numDataIds();i=u.kernelFunc({inputs:s,attrs:a,backend:this.backend});const o=Array.isArray(i)?i:[i];this.shouldCheckForMemLeaks()&&this.checkKernelForMemLeak(t,e,o);const p=o.map((e=>null!=e.rank?e:this.makeTensorFromTensorInfo(e)));if(r){const e=this.getTensorsForGradient(t,s,p);n=this.saveTensorsForBackwardMode(e)}return p}}else{const{forwardFunc:t}=e,s=e=>{r&&(n=e.map((e=>this.keep(this.clone(e)))))};o=()=>{const e=this.backend.numDataIds();i=this.tidy((()=>t(this.backend,s)));const n=Array.isArray(i)?i:[i];return this.shouldCheckForMemLeaks()&&this.checkKernelForMemLeak(u,e,n),n}}const{inputs:p,attrs:l}=e,c=Yt(e)?null:e.backwardsFunc;let h;return this.scopedRun((()=>this.state.kernelDepth++),(()=>this.state.kernelDepth--),(()=>{this.ENV.getBool("DEBUG")||this.state.profiling?(h=this.profiler.profileKernel(u,p,(()=>o())),this.ENV.getBool("DEBUG")&&this.profiler.logKernelProfile(h),t=h.outputs):t=o()})),r&&this.addTapeNode(u,p,t,c,n,l),this.state.profiling&&this.state.activeProfile.kernels.push({name:u,bytesAdded:this.state.numBytes-s,totalBytesSnapshot:this.state.numBytes,tensorsAdded:this.state.numTensors-a,totalTensorsSnapshot:this.state.numTensors,inputShapes:Object.keys(p).map((e=>null!=p[e]?p[e].shape:null)),outputShapes:t.map((e=>e.shape)),kernelTimeMs:h.timeMs,extraInfo:h.extraInfo}),Array.isArray(i)?t:t[0]}saveTensorsForBackwardMode(e){const t=e.map((e=>this.keep(this.clone(e))));return t}getTensorsForGradient(e,t,n){const r=Ue(e);if(null!=r){const e=r.inputsToSave||[],s=r.outputsToSave||[];let a;r.saveAllInputs?(de(Array.isArray(t),(()=>"saveAllInputs is true, expected inputs to be an array.")),a=Object.keys(t).map((e=>t[e]))):a=e.map((e=>t[e]));const o=n.filter(((e,t)=>s[t]));return a.concat(o)}return[]}makeTensor(e,t,n,r){if(null==e)throw new Error("Values passed to engine.makeTensor() are null");n=n||"float32",r=r||this.backend;let s=e;"string"===n&&Te(e[0])&&(s=e.map((e=>function(e,t="utf-8"){return t=t||"utf-8",Fe().platform.encode(e,t)}(e))));const a=r.write(s,t,n),o=new Lt(t,n,a,this.nextTensorId());if(this.trackTensor(o,r),"string"===n){const e=this.state.tensorInfo.get(a),t=function(e){if(null==e)return 0;let t=0;return e.forEach((e=>t+=e.length)),t}(s);this.state.numBytes+=t-e.bytes,e.bytes=t}return o}makeTensorFromDataId(e,t,n,r){const s={dataId:e,shape:t,dtype:n=n||"float32"};return this.makeTensorFromTensorInfo(s,r)}makeTensorFromTensorInfo(e,t){const{dataId:n,shape:r,dtype:s}=e,a=new Lt(r,s,n,this.nextTensorId());return this.trackTensor(a,t),a}makeVariable(e,t=!0,n,r){n=n||this.nextVariableId().toString(),null!=r&&r!==e.dtype&&(e=e.cast(r));const s=new Bt(e,t,n,this.nextTensorId());if(null!=this.state.registeredVariables[s.name])throw new Error(`Variable with name ${s.name} was already registered`);return this.state.registeredVariables[s.name]=s,this.incRef(s,this.backend),s}trackTensor(e,t){this.state.numTensors++,"string"===e.dtype&&this.state.numStringTensors++;let n=0;"complex64"!==e.dtype&&"string"!==e.dtype&&(n=e.size*ke(e.dtype)),this.state.numBytes+=n,this.state.tensorInfo.has(e.dataId)||(this.state.numDataBuffers++,this.state.tensorInfo.set(e.dataId,{backend:t||this.backend,dtype:e.dtype,shape:e.shape,bytes:n})),e instanceof Bt||this.track(e)}incRef(e,t){this.trackTensor(e,t),this.backend.incRef(e.dataId)}removeDataId(e,t){this.state.tensorInfo.has(e)&&this.state.tensorInfo.get(e).backend===t&&(this.state.tensorInfo.delete(e),this.state.numDataBuffers--)}disposeTensor(e){if(!this.state.tensorInfo.has(e.dataId))return;const t=this.state.tensorInfo.get(e.dataId);if(this.state.numTensors--,"string"===e.dtype&&(this.state.numStringTensors--,this.state.numBytes-=t.bytes),"complex64"!==e.dtype&&"string"!==e.dtype){const t=e.size*ke(e.dtype);this.state.numBytes-=t}t.backend.disposeData(e.dataId)&&this.removeDataId(e.dataId,t.backend)}disposeVariables(){for(const e in this.state.registeredVariables){const t=this.state.registeredVariables[e];this.disposeVariable(t)}}disposeVariable(e){this.disposeTensor(e),null!=this.state.registeredVariables[e.name]&&delete this.state.registeredVariables[e.name]}memory(){const e=this.backend.memory();return e.numTensors=this.state.numTensors,e.numDataBuffers=this.state.numDataBuffers,e.numBytes=this.state.numBytes,this.state.numStringTensors>0&&(e.unreliable=!0,null==e.reasons&&(e.reasons=[]),e.reasons.push("Memory usage by string tensors is approximate (2 bytes per character)")),e}async profile(e){this.state.profiling=!0;const t=this.state.numBytes,n=this.state.numTensors;this.state.activeProfile.kernels=[],this.state.activeProfile.result=await e(),this.state.profiling=!1,this.state.activeProfile.peakBytes=Math.max(...this.state.activeProfile.kernels.map((e=>e.totalBytesSnapshot))),this.state.activeProfile.newBytes=this.state.numBytes-t,this.state.activeProfile.newTensors=this.state.numTensors-n;for(const e of this.state.activeProfile.kernels)e.kernelTimeMs=await e.kernelTimeMs,e.extraInfo=await e.extraInfo;return this.state.activeProfile}isTapeOn(){return this.state.gradientDepth>0&&0===this.state.kernelDepth}addTapeNode(e,t,n,r,s,a){const o={id:this.state.nextTapeNodeId++,kernelName:e,inputs:t,outputs:n,saved:s},i=Ue(e);null!=i&&(r=i.gradFunc),null!=r&&(o.gradient=e=>(e=e.map(((e,t)=>{if(null==e){const e=n[t],r=Ae(e.size,e.dtype);return this.makeTensor(r,e.shape,e.dtype)}return e})),r(e.length>1?e:e[0],s,a))),this.state.activeTape.push(o)}keep(e){return e.kept=!0,e}startTape(){0===this.state.gradientDepth&&(this.state.activeTape=[]),this.state.gradientDepth++}endTape(){this.state.gradientDepth--}startScope(e){const t={track:[],name:"unnamed scope",id:this.state.nextScopeId++};e&&(t.name=e),this.state.scopeStack.push(t),this.state.activeScope=t}endScope(e){const t=Qt(e),n=new Set(t.map((e=>e.id)));for(let e=0;e<this.state.activeScope.track.length;e++){const t=this.state.activeScope.track[e];t.kept||n.has(t.id)||t.dispose()}const r=this.state.scopeStack.pop();this.state.activeScope=0===this.state.scopeStack.length?null:this.state.scopeStack[this.state.scopeStack.length-1],t.forEach((e=>{e.kept||e.scopeId!==r.id||this.track(e)}))}gradients(e,t,n,r=!1){if(de(t.length>0,(()=>"gradients() received an empty list of xs.")),null!=n&&"float32"!==n.dtype)throw new Error(`dy must have 'float32' dtype, but has '${n.dtype}'`);const s=this.scopedRun((()=>this.startTape()),(()=>this.endTape()),(()=>this.tidy("forward",e)));de(s instanceof Lt,(()=>"The result y returned by f() must be a tensor."));const a=function(e,t,n){const r={},s={};for(let e=0;e<t.length;e++)r[t[e].id]=!0;for(let n=0;n<e.length;n++){const a=e[n],o=a.inputs;for(const e in o){const n=o[e];let i=!1;for(let e=0;e<t.length;e++)if(r[n.id]){a.outputs.forEach((e=>r[e.id]=!0)),i=!0,s[a.id]=!0;break}if(i)break}}const a={};a[n.id]=!0;const o={};for(let t=e.length-1;t>=0;t--){const n=e[t],r=n.inputs;for(let e=0;e<n.outputs.length;e++)if(a[n.outputs[e].id]){for(const e in r)a[r[e].id]=!0,o[n.id]=!0;break}}const i=[];for(let t=0;t<e.length;t++){const n=e[t];if(s[n.id]&&o[n.id]){const e={};for(const t in n.inputs){const s=n.inputs[t];r[s.id]&&(e[t]=s)}const t=Object.assign({},n);t.inputs=e,t.outputs=n.outputs,i.push(t)}}return i}(this.state.activeTape,t,s);if(!r&&0===a.length&&t.length>0)throw new Error("Cannot compute gradient of y=f(x) with respect to x. Make sure that the f you passed encloses all operations that lead from x to y.");return this.tidy("backward",(()=>{const e={};e[s.id]=null==n?function(e){const t=$e(ye(e),"float32");return tn.makeTensor(t,e,"float32")}(s.shape):n,function(e,t,n,r){for(let s=t.length-1;s>=0;s--){const a=t[s],o=[];if(a.outputs.forEach((t=>{const n=e[t.id];null!=n?o.push(n):o.push(null)})),null==a.gradient)throw new Error(`Cannot compute gradient: gradient function not found for ${a.kernelName}.`);const i=a.gradient(o);for(const t in a.inputs){if(!(t in i))throw new Error(`Cannot backprop through input ${t}. Available gradients found: ${Object.keys(i)}.`);const s=n((()=>i[t]()));if("float32"!==s.dtype)throw new Error(`Error in gradient for op ${a.kernelName}. The gradient of input ${t} must have 'float32' dtype, but has '${s.dtype}'`);const o=a.inputs[t];if(!ge(s.shape,o.shape))throw new Error(`Error in gradient for op ${a.kernelName}. The gradient of input '${t}' has shape '${s.shape}', which does not match the shape of the input '${o.shape}'`);if(null==e[o.id])e[o.id]=s;else{const t=e[o.id];e[o.id]=r(t,s),t.dispose()}}}}(e,a,(e=>this.tidy(e)),nn);const r=t.map((t=>e[t.id]));return 0===this.state.gradientDepth&&(this.state.activeTape.forEach((e=>{for(const t of e.saved)t.dispose()})),this.state.activeTape=null),{value:s,grads:r}}))}customGrad(e){return de(_e(e),(()=>"The f passed in customGrad(f) must be a function.")),(...t)=>{let n;de(t.every((e=>e instanceof Lt)),(()=>"The args passed in customGrad(f)(x1, x2,...) must all be tensors"));const r={};t.forEach(((e,t)=>{r[t]=e}));return this.runKernelFunc({forwardFunc:(r,s)=>(n=e(...t,s),de(n.value instanceof Lt,(()=>"The function f passed in customGrad(f) must return an object where `obj.value` is a tensor")),de(_e(n.gradFunc),(()=>"The function f passed in customGrad(f) must return an object where `obj.gradFunc` is a function.")),n.value),backwardsFunc:(e,r)=>{const s=n.gradFunc(e,r),a=Array.isArray(s)?s:[s];de(a.length===t.length,(()=>"The function f passed in customGrad(f) must return an object where `obj.gradFunc` is a function that returns the same number of tensors as inputs passed to f(...).")),de(a.every((e=>e instanceof Lt)),(()=>"The function f passed in customGrad(f) must return an object where `obj.gradFunc` is a function that returns a list of only tensors."));const o={};return a.forEach(((e,t)=>{o[t]=()=>e})),o},inputs:r})}}readSync(e){return this.state.tensorInfo.get(e).backend.readSync(e)}read(e){return this.state.tensorInfo.get(e).backend.read(e)}readToGPU(e,t){return this.state.tensorInfo.get(e).backend.readToGPU(e,t)}async time(e){const t=vt(),n=await this.backend.time(e);return n.wallMs=vt()-t,n}track(e){return null!=this.state.activeScope&&(e.scopeId=this.state.activeScope.id,this.state.activeScope.track.push(e)),e}get registeredVariables(){return this.state.registeredVariables}reset(){this.pendingBackendInitId++,this.state.dispose(),this.ENV.reset(),this.state=new Jt;for(const e in this.registry)this.disposeRegisteredKernels(e),this.registry[e].dispose(),delete this.registry[e];this.backendName=null,this.backendInstance=null,this.pendingBackendInit=null}}en.nextTensorId=0,en.nextVariableId=0;const tn=function(){const e=Le();if(null==e._tfengine){const t=new Me(e);e._tfengine=new en(t)}var t;return t=e._tfengine.ENV,ze=t,zt=()=>e._tfengine,e._tfengine}();function nn(e,t){const n={a:e,b:t};return tn.runKernel("Add",n)}function rn(e,t){let n=e;if(St(e))return"string"===t?[]:[e.length];if(Gt(e)){const t=e.channels||"RGBA";return[e.height,e.width*t.length]}if(Ht(e))return[e.buffer.size/(null==t?4:ke(t))];if(!Array.isArray(e))return[];const r=[];for(;Array.isArray(n)||St(n)&&"string"!==t;)r.push(n.length),n=n[0];return Array.isArray(e)&&Fe().getBool("TENSORLIKE_CHECK_SHAPE_CONSISTENCY")&&sn(e,r,[]),r}function sn(e,t,n){if(n=n||[],!Array.isArray(e)&&!St(e))return void de(0===t.length,(()=>`Element arr[${n.join("][")}] is a primitive, but should be an array/TypedArray of ${t[0]} elements`));de(t.length>0,(()=>`Element arr[${n.join("][")}] should be a primitive, but is an array of ${e.length} elements`)),de(e.length===t[0],(()=>`Element arr[${n.join("][")}] should have ${t[0]} elements, but has ${e.length} elements`));const r=t.slice(1);for(let t=0;t<e.length;++t)sn(e[t],r,n.concat(t))}function an(e,t,n,r){if("string_or_numeric"!==e){if(null==e)throw new Error("Expected dtype cannot be null.");if("numeric"!==e&&e!==t||"numeric"===e&&"string"===t)throw new Error(`Argument '${n}' passed to '${r}' must be ${e} tensor, but got ${t} tensor`)}}function on(e,t,n,r="numeric"){if(e instanceof Vt())return an(r,e.dtype,t,n),e;let s=ve(e);if("string"!==s&&["bool","int32","float32"].indexOf(r)>=0&&(s=r),an(r,s,t,n),null==e||!St(e)&&!Array.isArray(e)&&"number"!=typeof e&&"boolean"!=typeof e&&"string"!=typeof e){const r=null==e?"null":e.constructor.name;throw new Error(`Argument '${t}' passed to '${n}' must be a Tensor or TensorLike, but got '${r}'`)}const a=rn(e,s);St(e)||Array.isArray(e)||(e=[e]);const o="string"!==s?Tt(e,s):Et(e,[],!0);return tn.makeTensor(o,a,s)}function un(e,t,n,r="numeric"){if(!Array.isArray(e))throw new Error(`Argument ${t} passed to ${n} must be a \`Tensor[]\` or \`TensorLike[]\``);return e.map(((e,s)=>on(e,`${t}[${s}]`,n,r)))}function pn(e){const t=Object.keys(e);if(1!==t.length)throw new Error(`Please provide an object with a single key (operation name) mapping to a function. Got an object with ${t.length} keys.`);let n=t[0];const r=e[n];n.endsWith("_")&&(n=n.substring(0,n.length-1)),n+="__op";const s=(...e)=>{tn.startScope(n);try{const t=r(...e);return Oe(t)&&console.error("Cannot return a Promise inside of tidy."),tn.endScope(t),t}catch(e){throw tn.endScope(null),e}};return Object.defineProperty(s,"name",{value:n,configurable:!0}),s}const ln=pn({abs_:function(e){const t=on(e,"x","abs");if("complex64"===t.dtype){const e={x:t};return tn.runKernel("ComplexAbs",e)}{const e={x:t};return tn.runKernel("Abs",e)}}});const cn=pn({acos_:function(e){const t={x:on(e,"x","acos")};return tn.runKernel("Acos",t)}});const hn=pn({acosh_:function(e){const t={x:on(e,"x","acosh")};return tn.runKernel("Acosh",t)}});const dn=pn({add_:function(e,t){let n=on(e,"a","add"),r=on(t,"b","add");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("Add",s)}});const mn=pn({addN_:function(e){de(Array.isArray(e),(()=>"The argument passed to tf.addN() must be a list of tensors")),de(e.length>=1,(()=>`Must pass at least one tensor to tf.addN(), but got ${e.length}`));const t=e.map(((e,t)=>on(e,`tensors${t}`,"addN"))),n=t[0];t.forEach((e=>{if(e.dtype!==n.dtype)throw new Error("All tensors passed to tf.addN() must have the same dtype")})),t.forEach((e=>{if(!ge(e.shape,n.shape))throw new Error("All tensors passed to tf.addN() must have the same shape")}));const r=t;return tn.runKernel("AddN",r)}});const fn=pn({all_:function(e,t=null,n=!1){const r={x:on(e,"x","all","bool")},s={axis:t,keepDims:n};return tn.runKernel("All",r,s)}});const yn=pn({any_:function(e,t=null,n=!1){const r={x:on(e,"x","any","bool")},s={axis:t,keepDims:n};return tn.runKernel("Any",r,s)}});const gn=pn({argMax_:function(e,t=0){const n={x:on(e,"x","argMax")},r={axis:t};return tn.runKernel("ArgMax",n,r)}});const bn=pn({argMin_:function(e,t=0){const n={x:on(e,"x","argMin")},r={axis:t};return tn.runKernel("ArgMin",n,r)}});const xn=pn({asin_:function(e){const t={x:on(e,"x","asin")};return tn.runKernel("Asin",t)}});const Nn=pn({asinh_:function(e){const t={x:on(e,"x","asinh")};return tn.runKernel("Asinh",t)}});const wn=pn({atan_:function(e){const t={x:on(e,"x","atan")};return tn.runKernel("Atan",t)}});const kn=pn({atan2_:function(e,t){let n=on(e,"a","atan2"),r=on(t,"b","atan2");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("Atan2",s)}});const Tn=pn({atanh_:function(e){const t={x:on(e,"x","atanh")};return tn.runKernel("Atanh",t)}});const vn=pn({cast_:function(e,t){const n=on(e,"x","cast");if(!function(e){return"bool"===e||"complex64"===e||"float32"===e||"int32"===e||"string"===e}(t))throw new Error(`Failed to cast to unknown dtype ${t}`);if("string"===t&&"string"!==n.dtype||"string"!==t&&"string"===n.dtype)throw new Error("Only strings can be casted to strings");const r={x:n},s={dtype:t};return tn.runKernel("Cast",r,s)}});function _n(e,t,n,r,s,a,o=!1,i="channelsLast"){let[u,p,l,c]=[-1,-1,-1,-1];if("channelsLast"===i)[u,p,l,c]=e;else{if("channelsFirst"!==i)throw new Error(`Unknown dataFormat ${i}`);[u,c,p,l]=e}const[h,d,,m]=t,[f,y]=Sn(n),[g,b]=Sn(r),x=En(h,g),N=En(d,b),{padInfo:w,outHeight:k,outWidth:T}=function(e,t,n,r,s,a,o,i,u){let p,l,c;if("number"==typeof e){p={top:e,bottom:e,left:e,right:e,type:0===e?"VALID":"NUMBER"};const s=function(e,t,n,r,s){null==r&&(r=function(e,t,n,r=1){const s=En(t,r);return Math.floor((e[0]*(n-1)-n+s)/2)}(e,t,n));const a=e[0],o=e[1],i=In((a-t+2*r)/n+1,s),u=In((o-t+2*r)/n+1,s);return[i,u]}([t,n],a,r,e,i);l=s[0],c=s[1]}else if("same"===e){l=Math.ceil(t/r),c=Math.ceil(n/s);const e=Math.max(0,(l-1)*r+a-t),i=Math.max(0,(c-1)*s+o-n),u=Math.floor(e/2),h=e-u,d=Math.floor(i/2);p={top:u,bottom:h,left:d,right:i-d,type:"SAME"}}else if("valid"===e)p={top:0,bottom:0,left:0,right:0,type:"VALID"},l=Math.ceil((t-a+1)/r),c=Math.ceil((n-o+1)/s);else{if("object"!=typeof e)throw Error(`Unknown padding parameter: ${e}`);{const h="channelsLast"===u?e[1][0]:e[2][0],d="channelsLast"===u?e[1][1]:e[2][1],m="channelsLast"===u?e[2][0]:e[3][0],f="channelsLast"===u?e[2][1]:e[3][1];p={top:h,bottom:d,left:m,right:f,type:0===h&&0===d&&0===m&&0===f?"VALID":"EXPLICIT"},l=In((t-a+h+d)/r+1,i),c=In((n-o+m+f)/s+1,i)}}return{padInfo:p,outHeight:l,outWidth:c}}(s,p,l,f,y,x,N,a,i),v=o?m*c:m;let _;return"channelsFirst"===i?_=[u,v,k,T]:"channelsLast"===i&&(_=[u,k,T,v]),{batchSize:u,dataFormat:i,inHeight:p,inWidth:l,inChannels:c,outHeight:k,outWidth:T,outChannels:v,padInfo:w,strideHeight:f,strideWidth:y,filterHeight:h,filterWidth:d,effectiveFilterHeight:x,effectiveFilterWidth:N,dilationHeight:g,dilationWidth:b,inShape:e,outShape:_,filterShape:t}}function Sn(e){return"number"==typeof e?[e,e,e]:2===e.length?[e[0],e[1],1]:e}function En(e,t){return t<=1?e:e+(e-1)*(t-1)}function In(e,t){if(!t)return Math.trunc(e);switch(t){case"round":return Math.round(e);case"ceil":return Math.ceil(e);case"floor":return Math.floor(e);default:throw new Error(`Unknown roundingMode ${t}`)}}function $n(e){const[t,n,r]=Sn(e);return 1===t&&1===n&&1===r}function An(e,t){return $n(e)||$n(t)}function Dn(e){return Sn(e).every((e=>e>0))}function On(e,t,n){if(null!=n){if("string"==typeof t)throw Error(`Error in ${e}: pad must be an integer when using dimRoundingMode ${n} but got pad ${t}.`);if("number"==typeof t)de(be(t),(()=>`Error in ${e}: pad must be an integer when using dimRoundingMode ${n} but got pad ${t}.`));else{if("object"!=typeof t)throw Error(`Error in ${e}: Unknown padding parameter: ${t}`);t.forEach((t=>{t.forEach((t=>{de(be(t),(()=>`Error in ${e}: pad must be an integer when using dimRoundingMode ${n} but got pad ${t}.`))}))}))}}}const Mn=pn({reshape_:function(e,t){const n={x:on(e,"x","reshape","string_or_numeric")},r={shape:t};return tn.runKernel("Reshape",n,r)}});const Cn=pn({avgPool_:function(e,t,n,r,s){const a=on(e,"x","avgPool","float32");de(An(n,1),(()=>`Error in avgPool: Either strides or dilations must be 1. Got strides ${n} and dilations '1'`));let o=a,i=!1;3===a.rank&&(i=!0,o=Mn(a,[1,a.shape[0],a.shape[1],a.shape[2]])),de(4===o.rank,(()=>`Error in avgPool: x must be rank 4 but got rank ${o.rank}.`)),On("avgPool",r,s);const u={x:o},p={filterSize:t,strides:n,pad:r,dimRoundingMode:s};let l=tn.runKernel("AvgPool",u,p);return l=vn(l,a.dtype),i?Mn(l,[l.shape[1],l.shape[2],l.shape[3]]):l}});const Fn=pn({avgPool3d_:function(e,t,n,r,s,a="NDHWC"){const o=on(e,"x","avgPool3d","float32");let i=o,u=!1;4===o.rank&&(u=!0,i=Mn(o,[1,o.shape[0],o.shape[1],o.shape[2],o.shape[3]])),de(5===i.rank,(()=>`Error in avgPool3d: x must be rank 5 but got rank ${i.rank}.`)),de("NDHWC"===a,(()=>`Error in avgPool3d: Only NDHWC is currently supported, but got dataFormat of ${a}`)),de("number"==typeof n&&n>0||Array.isArray(n)&&n[0]>0&&n[1]>0&&n[2]>0,(()=>`Error in avgPool3d: Stride must be > 0, but got '${n}'`)),On("avgPool3d",r,s);const p={x:i},l={filterSize:t,strides:n,pad:r,dimRoundingMode:s,dataFormat:a};let c=tn.runKernel("AvgPool3D",p,l);return c=vn(c,i.dtype),u?Mn(c,[c.shape[1],c.shape[2],c.shape[3],c.shape[4]]):c}});const Rn=pn({clone_:function(e){const t={x:on(e,"x","clone","string_or_numeric")};return tn.runKernel("Identity",t)}});const zn=pn({concat_:function(e,t=0){de(e.length>=1,(()=>"Pass at least one tensor to concat"));const n=un(e,"tensors","concat","string_or_numeric");if("complex64"===n[0].dtype&&n.forEach((e=>{if("complex64"!==e.dtype)throw new Error(`Cannot concatenate complex64 tensors with a tensor\n          with dtype ${e.dtype}. `)})),1===n.length)return Rn(n[0]);const r=n,s={axis:t};return tn.runKernel("Concat",r,s)}});const Ln=pn({matMul_:function(e,t,n=!1,r=!1){let s=on(e,"a","matMul"),a=on(t,"b","matMul");[s,a]=Zt(s,a);const o={a:s,b:a},i={transposeA:n,transposeB:r};return tn.runKernel("BatchMatMul",o,i)}});const Vn=pn({mul_:function(e,t){let n=on(e,"a","mul"),r=on(t,"b","mul");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("Multiply",s)}});const Bn=pn({sigmoid_:function(e){const t={x:on(e,"x","sigmoid","float32")};return tn.runKernel("Sigmoid",t)}});const Pn=pn({slice_:function(e,t,n){const r=on(e,"x","slice","string_or_numeric");if(0===r.rank)throw new Error("Slicing scalar is not possible");const s={x:r},a={begin:t,size:n};return tn.runKernel("Slice",s,a)}});const Kn=pn({tanh_:function(e){const t={x:on(e,"x","tanh","float32")};return tn.runKernel("Tanh",t)}});const qn=pn({basicLSTMCell_:function(e,t,n,r,s,a){const o=on(e,"forgetBias","basicLSTMCell"),i=on(t,"lstmKernel","basicLSTMCell"),u=on(n,"lstmBias","basicLSTMCell"),p=on(r,"data","basicLSTMCell"),l=on(s,"c","basicLSTMCell"),c=on(a,"h","basicLSTMCell"),h=zn([p,c],1),d=Ln(h,i),m=dn(d,u),f=m.shape[0],y=m.shape[1]/4,g=[f,y],b=Pn(m,[0,0],g),x=Pn(m,[0,y],g),N=Pn(m,[0,2*y],g),w=Pn(m,[0,3*y],g),k=dn(Vn(Bn(b),Kn(x)),Vn(l,Bn(dn(o,N))));return[k,Vn(Kn(k),Bn(w))]}});const Un=pn({batchToSpaceND_:function(e,t,n){const r=on(e,"x","batchToSpaceND"),s=t.reduce(((e,t)=>e*t));de(r.rank>=1+t.length,(()=>`input rank is ${r.rank} but should be > than blockShape.length ${t.length}`)),de(n.length===t.length,(()=>`crops.length is ${n.length} but should be equal to blockShape.length  ${t.length}`)),de(r.shape[0]%s==0,(()=>`input tensor batch is ${r.shape[0]} but is not divisible by the product of the elements of blockShape ${t.join(" * ")} === ${s}`));const a={x:r},o={blockShape:t,crops:n};return tn.runKernel("BatchToSpaceND",a,o)}});const Wn=pn({batchNorm_:function(e,t,n,r,s,a){null==a&&(a=.001);const o=on(e,"x","batchNorm"),i=on(t,"mean","batchNorm"),u=on(n,"variance","batchNorm");let p,l;null!=s&&(p=on(s,"scale","batchNorm")),null!=r&&(l=on(r,"offset","batchNorm")),de(i.rank===u.rank,(()=>"Batch normalization gradient requires mean and variance to have equal ranks.")),de(null==l||i.rank===l.rank,(()=>"Batch normalization gradient requires mean and offset to have equal ranks.")),de(null==p||i.rank===p.rank,(()=>"Batch normalization gradient requires mean and scale to have equal ranks."));const c=function(e){let t;return t=0===e.rank||1===e.rank?Mn(e,[1,1,1,e.size]):2===e.rank?Mn(e,[1,1,e.shape[0],e.shape[1]]):3===e.rank?Mn(e,[1,e.shape[0],e.shape[1],e.shape[2]]):e,t}(o),h={x:c,scale:p,offset:l,mean:i,variance:u},d={varianceEpsilon:a},m=tn.runKernel("FusedBatchNorm",h,d);return Mn(m,o.shape)}});const jn=pn({batchNorm2d_:function(e,t,n,r,s,a){const o=on(e,"x","batchNorm"),i=on(t,"mean","batchNorm"),u=on(n,"variance","batchNorm");let p,l;return null!=s&&(p=on(s,"scale","batchNorm")),null!=r&&(l=on(r,"offset","batchNorm")),de(2===o.rank,(()=>`Error in batchNorm2D: x must be rank 2 but got rank ${o.rank}.`)),de(2===i.rank||1===i.rank,(()=>`Error in batchNorm2D: mean must be rank 2 or rank 1 but got rank ${i.rank}.`)),de(2===u.rank||1===u.rank,(()=>`Error in batchNorm2D: variance must be rank 2 or rank 1 but got rank ${u.rank}.`)),null!=p&&de(2===p.rank||1===p.rank,(()=>`Error in batchNorm2D: scale must be rank 2 or rank 1 but got rank ${p.rank}.`)),null!=l&&de(2===l.rank||1===l.rank,(()=>`Error in batchNorm2D: offset must be rank 2 or rank 1 but got rank ${l.rank}.`)),Wn(o,i,u,l,p,a)}});const Gn=pn({batchNorm3d_:function(e,t,n,r,s,a){const o=on(e,"x","batchNorm"),i=on(t,"mean","batchNorm"),u=on(n,"variance","batchNorm");let p,l;return null!=s&&(p=on(s,"scale","batchNorm")),null!=r&&(l=on(r,"offset","batchNorm")),de(3===o.rank,(()=>`Error in batchNorm3D: x must be rank 3 but got rank ${o.rank}.`)),de(3===i.rank||1===i.rank,(()=>`Error in batchNorm3D: mean must be rank 3 or rank 1 but got rank ${i.rank}.`)),de(3===u.rank||1===u.rank,(()=>`Error in batchNorm3D: variance must be rank 3 or rank 1 but got rank ${u.rank}.`)),null!=p&&de(3===p.rank||1===p.rank,(()=>`Error in batchNorm3D: scale must be rank 3 or rank 1 but got rank ${p.rank}.`)),null!=l&&de(3===l.rank||1===l.rank,(()=>`Error in batchNorm3D: offset must be rank 3 or rank 1 but got rank ${l.rank}.`)),Wn(o,i,u,l,p,a)}});const Hn=pn({batchNorm4d_:function(e,t,n,r,s,a){const o=on(e,"x","batchNorm"),i=on(t,"mean","batchNorm"),u=on(n,"variance","batchNorm");let p,l;return null!=s&&(p=on(s,"scale","batchNorm")),null!=r&&(l=on(r,"offset","batchNorm")),de(4===o.rank,(()=>`Error in batchNorm4D: x must be rank 4 but got rank ${o.rank}.`)),de(4===i.rank||1===i.rank,(()=>`Error in batchNorm4D: mean must be rank 4 or rank 1 but got rank ${i.rank}.`)),de(4===u.rank||1===u.rank,(()=>`Error in batchNorm4D: variance must be rank 4 or rank 1 but got rank ${u.rank}.`)),null!=p&&de(4===p.rank||1===p.rank,(()=>`Error in batchNorm4D: scale must be rank 4 or rank 1 but got rank ${p.rank}.`)),null!=l&&de(4===l.rank||1===l.rank,(()=>`Error in batchNorm4D: offset must be rank 4 or rank 1 but got rank ${l.rank}.`)),Wn(o,i,u,l,p,a)}});const Zn=pn({bincount_:function(e,t,n){const r=on(e,"x","bincount"),s=on(t,"weights","bincount");de("int32"===r.dtype,(()=>`Error in bincount: input dtype must be int32, but got ${r.dtype}`)),de(n>=0,(()=>`size must be non-negative, but got ${n}.`)),de(s.size===r.size||0===s.size,(()=>`Error in bincount: weights must have the same size as input or0-length, but got input shape: ${r.shape}, weights shape: ${s.shape}.`));const a={x:r,weights:s},o={size:n};return tn.runKernel("Bincount",a,o)}});const Qn=pn({bitwiseAnd_:function(e,t){const n=on(e,"x","bitwiseAnd"),r=on(t,"y","bitwiseAnd");if(!ge(n.shape,r.shape))throw new Error(`BitwiseAnd: Tensors must have the same shape. x: ${n.shape}, y: ${r.shape}`);if("int32"!==n.dtype||"int32"!==r.dtype)throw new Error(`BitwiseAnd: Only supports 'int32' values in tensor, found type of x: ${n.dtype} and type of y: ${r.dtype}`);const s={a:n,b:r};return tn.runKernel("BitwiseAnd",s)}});const Xn=pn({broadcastArgs_:function(e,t){const n=on(e,"s0","broadcastArgs","int32"),r=on(t,"s1","broadcastArgs","int32");if(1!==n.rank)throw new Error(`broadcastArgs(): first input must be a vector (rank=1). Has rank ${n.rank}`);if(1!==r.rank)throw new Error(`broadcastArgs(): second input must be a vector (rank=1). Has rank ${r.rank}`);const s={s0:n,s1:r};return tn.runKernel("BroadcastArgs",s)}});const Yn=pn({broadcastTo_:function(e,t){let n=on(e,"broadcastTo","x");const r=n.shape;if(De(t),t.length<n.rank)throw new Error(`broadcastTo(): shape.length=${t.length} < input.rank=${n.rank}.`);if(t.length>n.rank){const e=n.shape.slice();for(;e.length<t.length;)e.unshift(1);n=Mn(n,e)}const s=n.shape,a=Array.from(t);for(let e=t.length-1;e>=0;e--)if(s[e]===t[e])a[e]=1;else if(1!==n.shape[e])throw new Error(`broadcastTo(): [${r}] cannot be broadcast to [${t}].`);if(0===a.map(((e,t)=>e>1?t:-1)).filter((e=>e>=0)).length)return Rn(n);const o={x:n},i={reps:a};return tn.runKernel("Tile",o,i)}});function Jn(e,t="float32",n){return t=t||"float32",De(e),new Rt(e,t,n)}const er=pn({ceil_:function(e){const t={x:on(e,"x","ceil","float32")};return tn.runKernel("Ceil",t)}});function tr(e,t,n){De(e);const r={shape:e,value:t,dtype:n=n||ve(t)};return tn.runKernel("Fill",{},r)}const nr=pn({clipByValue_:function(e,t,n){const r=on(e,"x","clipByValue");if(de(t<=n,(()=>`Error in clip: min (${t}) must be less than or equal to max (${n}).`)),t===n)return tr(r.shape,t,r.dtype);const s={x:r},a={clipValueMin:t,clipValueMax:n};return tn.runKernel("ClipByValue",s,a)}});const rr=pn({complex_:function(e,t){const n=on(e,"real","complex"),r=on(t,"imag","complex");me(n.shape,r.shape,`real and imag shapes, ${n.shape} and ${r.shape}, must match in call to tf.complex().`);const s={real:n,imag:r};return tn.runKernel("Complex",s)}});const sr=pn({concat1d_:function(e){return zn(e,0)}});const ar=pn({concat2d_:function(e,t){return zn(e,t)}});const or=pn({concat3d_:function(e,t){return zn(e,t)}});const ir=pn({concat4d_:function(e,t){return zn(e,t)}});const ur=pn({conv2d_:function(e,t,n,r,s="NHWC",a=[1,1],o){const i=on(e,"x","conv2d","float32"),u=on(t,"filter","conv2d","float32");let p=i,l=!1;3===i.rank&&(l=!0,p=Mn(i,[1,i.shape[0],i.shape[1],i.shape[2]])),de(4===p.rank,(()=>`Error in conv2d: input must be rank 4, but got rank ${p.rank}.`)),de(4===u.rank,(()=>`Error in conv2d: filter must be rank 4, but got rank ${u.rank}.`)),On("conv2d",r,o);const c="NHWC"===s?p.shape[3]:p.shape[1];de(c===u.shape[2],(()=>`Error in conv2d: depth of input (${c}) must match input depth for filter ${u.shape[2]}.`)),de(An(n,a),(()=>`Error in conv2D: Either strides or dilations must be 1. Got strides ${n} and dilations '${a}'`)),de(Dn(a),(()=>"Error in conv2D: Dilated rates should be larger than 0.")),de(Dn(n),(()=>"Error in conv2D: Strides should be larger than 0."));const h={x:p,filter:u},d={strides:n,pad:r,dataFormat:s,dilations:a,dimRoundingMode:o},m=tn.runKernel("Conv2D",h,d);return l?Mn(m,[m.shape[1],m.shape[2],m.shape[3]]):m}});const pr=pn({conv1d_:function(e,t,n,r,s="NWC",a=1,o){const i=on(e,"x","conv1d"),u=on(t,"filter","conv1d");let p=i,l=!1;2===i.rank&&(l=!0,p=Mn(i,[1,i.shape[0],i.shape[1]])),de(3===p.rank,(()=>`Error in conv1d: input must be rank 3, but got rank ${p.rank}.`)),de(3===u.rank,(()=>`Error in conv1d: filter must be rank 3, but got rank ${u.rank}.`)),On("conv1d",r,o),de(p.shape[2]===u.shape[1],(()=>`Error in conv1d: depth of input (${p.shape[2]}) must match input depth for filter ${u.shape[1]}.`)),de(An(n,a),(()=>`Error in conv1D: Either stride or dilation must be 1. Got stride ${n} and dilation '${a}'`)),de(Dn(a),(()=>"Error in conv1D: Dilated rates should be larger than 0.")),de(Dn(n),(()=>"Error in conv1D: Stride should be larger than 0.")),de("NWC"===s,(()=>`Error in conv1d: got dataFormat of ${s} but only NWC is currently supported.`));const c=Mn(u,[1,u.shape[0],u.shape[1],u.shape[2]]),h=Mn(p,[p.shape[0],1,p.shape[1],p.shape[2]]),d=ur(h,c,[1,n],r,"NHWC",[1,a],o);return Mn(d,l?[d.shape[2],d.shape[3]]:[d.shape[0],d.shape[2],d.shape[3]])}});const lr=pn({conv2DBackpropInput_:function(e,t,n,r,s,a="NHWC",o){de(e.length===t.rank,(()=>`Length of inShape (${e.length}) and rank of dy (${t.rank}) must match`));let i=e,u=t,p=!1;3===t.rank&&(p=!0,u=Mn(t,[1,t.shape[0],t.shape[1],t.shape[2]]),i=[1,e[0],e[1],e[2]]),de(4===i.length,(()=>`Error in conv2dDerInput: inShape must be length 4, but got length ${i.length}.`)),de(4===u.rank,(()=>`Error in conv2dDerInput: dy must be rank 4, but got rank ${u.rank}`)),de(4===n.rank,(()=>`Error in conv2dDerInput: filter must be rank 4, but got rank ${n.rank}`));const l="NHWC"===a?i[3]:i[1],c="NHWC"===a?u.shape[3]:u.shape[1];de(l===n.shape[2],(()=>`Error in conv2dDerInput: depth of input (${l}) must match input depth for filter ${n.shape[2]}.`)),de(c===n.shape[3],(()=>`Error in conv2dDerInput: depth of output (${c}) must match output depth for filter ${n.shape[3]}.`)),On("conv2dDerInput",s,o);const h={dy:u,filter:n},d={strides:r,pad:s,dataFormat:a,dimRoundingMode:o,inputShape:i},m=tn.runKernel("Conv2DBackpropInput",h,d);return p?Mn(m,[m.shape[1],m.shape[2],m.shape[3]]):m}});const cr=pn({conv2dTranspose_:function(e,t,n,r,s,a){const o=on(e,"x","conv2dTranspose"),i=on(t,"filter","conv2dTranspose");return lr(n,o,i,r,s,"NHWC",a)}});const hr=pn({conv3d_:function(e,t,n,r,s="NDHWC",a=[1,1,1]){const o=on(e,"x","conv3d"),i=on(t,"filter","conv3d");let u=o,p=!1;4===o.rank&&(p=!0,u=Mn(o,[1,o.shape[0],o.shape[1],o.shape[2],o.shape[3]])),de(5===u.rank,(()=>`Error in conv3d: input must be rank 5, but got rank ${u.rank}.`)),de(5===i.rank,(()=>`Error in conv3d: filter must be rank 5, but got rank ${i.rank}.`)),de(u.shape[4]===i.shape[3],(()=>`Error in conv3d: depth of input (${u.shape[4]}) must match input depth for filter ${i.shape[3]}.`)),de(An(n,a),(()=>`Error in conv3D: Either strides or dilations must be 1. Got strides ${n} and dilations '${a}'`)),de("NDHWC"===s,(()=>`Error in conv3d: got dataFormat of ${s} but only NDHWC is currently supported.`)),de(Dn(a),(()=>"Error in conv3D: Dilated rates should be larger than 0.")),de(Dn(n),(()=>"Error in conv3D: Strides should be larger than 0."));const l={x:u,filter:i},c={strides:n,pad:r,dataFormat:s,dilations:a},h=tn.runKernel("Conv3D",l,c);return p?Mn(h,[h.shape[1],h.shape[2],h.shape[3],h.shape[4]]):h}});const dr=pn({conv3DBackpropInput_:function(e,t,n,r,s){de(e.length===t.rank,(()=>`Length of inShape (${e.length}) and rank of dy (${t.rank}) must match`));let a=e,o=t,i=!1;4===t.rank&&(i=!0,o=Mn(t,[1,t.shape[0],t.shape[1],t.shape[2],t.shape[3]]),a=[1,e[0],e[1],e[2],e[3]]);const u=a[4],p=o.shape[4];de(5===a.length,(()=>`Error in conv3dDerInput: inShape must be length 5, but got length ${a.length}.`)),de(5===o.rank,(()=>`Error in conv3dDerInput: dy must be rank 5, but got rank ${o.rank}`)),de(5===n.rank,(()=>`Error in conv3dDerInput: filter must be rank 5, but got rank ${n.rank}`)),de(u===n.shape[3],(()=>`Error in conv3dDerInput: depth of input (${u}) must match input depth for filter ${n.shape[3]}.`)),de(p===n.shape[4],(()=>`Error in conv3dDerInput: depth of output (${p}) must match output depth for filter ${n.shape[4]}.`));const l={dy:o,filter:n},c={pad:s,strides:r,inputShape:a},h=tn.runKernel("Conv3DBackpropInputV2",l,c);return i?Mn(h,[h.shape[1],h.shape[2],h.shape[3],h.shape[4]]):h}});const mr=pn({conv3dTranspose_:function(e,t,n,r,s){const a=on(e,"x","conv3dTranspose"),o=on(t,"filter","conv3dTranspose");return dr(n,a,o,r,s)}});const fr=pn({cos_:function(e){const t={x:on(e,"x","cos","float32")};return tn.runKernel("Cos",t)}});const yr=pn({cosh_:function(e){const t={x:on(e,"x","cosh","float32")};return tn.runKernel("Cosh",t)}});const gr=pn({cumprod_:function(e,t=0,n=!1,r=!1){const s={x:on(e,"x","cumprod")},a={axis:t,exclusive:n,reverse:r};return tn.runKernel("Cumprod",s,a)}});const br=pn({cumsum_:function(e,t=0,n=!1,r=!1){const s={x:on(e,"x","cumsum")},a={axis:t,exclusive:n,reverse:r};return tn.runKernel("Cumsum",s,a)}});const xr=pn({denseBincount_:function(e,t,n,r=!1){const s=on(e,"x","denseBincount"),a=on(t,"weights","denseBincount");de("int32"===s.dtype,(()=>`Error in denseBincount: input dtype must be int32, but got ${s.dtype}`)),de(s.rank<=2,(()=>`Error in denseBincount: input must be at most rank 2, but got rank ${s.rank}.`)),de(n>=0,(()=>`size must be non-negative, but got ${n}.`)),de(a.size===s.size||0===a.size,(()=>`Error in denseBincount: weights must have the same shape as x or 0-length, but got x shape: ${s.shape}, weights shape: ${a.shape}.`));const o={x:s,weights:a},i={size:n,binaryOutput:r};return tn.runKernel("DenseBincount",o,i)}});const Nr=pn({depthToSpace_:function(e,t,n="NHWC"){const r=on(e,"x","depthToSpace","float32"),s="NHWC"===n?r.shape[1]:r.shape[2],a="NHWC"===n?r.shape[2]:r.shape[3],o="NHWC"===n?r.shape[3]:r.shape[1];de(t>1,(()=>`blockSize should be > 1 for depthToSpace, but was: ${t}`)),de(s*t>=0,(()=>`Negative dimension size caused by overflow when multiplying\n    ${s} and ${t}  for depthToSpace with input shape\n    ${r.shape}`)),de(a*t>=0,(()=>`Negative dimension size caused by overflow when multiplying\n    ${a} and ${t} for depthToSpace with input shape\n        ${r.shape}`)),de(o%(t*t)==0,(()=>`Dimension size must be evenly divisible by ${t*t} but is ${o} for depthToSpace with input shape ${r.shape}`));const i={x:r},u={blockSize:t,dataFormat:n};return tn.runKernel("DepthToSpace",i,u)}});const wr=pn({depthwiseConv2d_:function(e,t,n,r,s="NHWC",a=[1,1],o){const i=on(e,"x","depthwiseConv2d","float32"),u=on(t,"filter","depthwiseConv2d","float32");let p=i,l=!1;3===i.rank&&(l=!0,p=Mn(i,[1,i.shape[0],i.shape[1],i.shape[2]])),de(4===p.rank,(()=>`Error in depthwiseConv2d: input must be rank 4, but got rank ${p.rank}.`)),de(4===u.rank,(()=>`Error in depthwiseConv2d: filter must be rank 4, but got rank ${u.rank}.`));const c="NHWC"===s?p.shape[3]:p.shape[1];de(c===u.shape[2],(()=>`Error in depthwiseConv2d: number of input channels (${c}) must match the inChannels dimension in filter ${u.shape[2]}.`)),On("depthwiseConv2d",r,o);const h={x:p,filter:u},d={strides:n,pad:r,dataFormat:s,dilations:a,dimRoundingMode:o},m=tn.runKernel("DepthwiseConv2dNative",h,d);return l?Mn(m,[m.shape[1],m.shape[2],m.shape[3]]):m}});const kr=pn({diag_:function(e){const t={x:on(e,"x","diag")};return tn.runKernel("Diag",t)}});const Tr=pn({dilation2d_:function(e,t,n,r,s=[1,1],a="NHWC"){const o=on(e,"x","dilation2d"),i=on(t,"filter","dilation2d");de(3===o.rank||4===o.rank,(()=>`Error in dilation2d: input must be rank 3 or 4, but got rank ${o.rank}.`)),de(3===i.rank,(()=>`Error in dilation2d: filter must be rank 3, but got rank ${i.rank}.`)),de("NHWC"===a,(()=>`Error in dilation2d: Only NHWC is currently supported, but got dataFormat of ${a}`));let u=o,p=!1;3===o.rank&&(u=Mn(o,[1,o.shape[0],o.shape[1],o.shape[2]]),p=!0),de(u.shape[3]===i.shape[2],(()=>`Error in dilation2d:  input and filter must have the same depth: ${u.shape[3]} vs ${i.shape[2]}`));const l={x:u,filter:i},c={strides:n,pad:r,dilations:s},h=tn.runKernel("Dilation2D",l,c);return p?Mn(h,[h.shape[1],h.shape[2],h.shape[3]]):h}});const vr=pn({floorDiv_:function(e,t){let n=on(e,"a","floorDiv"),r=on(t,"b","floorDiv");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("FloorDiv",s)}});const _r=pn({div_:function(e,t){let n=on(e,"a","div"),r=on(t,"b","div");if([n,r]=Zt(n,r),"int32"===n.dtype&&"int32"===r.dtype)return vr(n,r);const s={a:n,b:r};return tn.runKernel("RealDiv",s,{})}});function Sr(e,t){const n=Math.max(e.length,t.length),r=new Array(n);for(let s=0;s<n;s++){let a=e[e.length-s-1];null==a&&(a=1);let o=t[t.length-s-1];if(null==o&&(o=1),1===a)r[n-s-1]=o;else if(1===o)r[n-s-1]=a;else{if(a!==o){throw Error(`Operands could not be broadcast together with shapes ${e} and ${t}.`)}r[n-s-1]=a}}return r}const Er=pn({equal_:function(e,t){let n=on(e,"a","equal","string_or_numeric"),r=on(t,"b","equal","string_or_numeric");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("Equal",s)}});const Ir=pn({where_:function(e,t,n){const r=on(t,"a","where"),s=on(n,"b","where"),a=on(e,"condition","where","bool"),o=Sr(Sr(a.shape,r.shape),s.shape),i={condition:Yn(a,o),t:Yn(r,o),e:Yn(s,o)};return tn.runKernel("Select",i)}});const $r=pn({zerosLike_:function(e){const t={x:on(e,"x","zerosLike")};return tn.runKernel("ZerosLike",t)}});const Ar=pn({divNoNan_:function(e,t){let n=on(e,"a","div"),r=on(t,"b","div");[n,r]=Zt(n,r);const s=_r(n,r),a=$r(s),o=Er(r,a);return Ir(o,a,s)}});const Dr=pn({dot_:function(e,t){const n=on(e,"t1","dot"),r=on(t,"t2","dot");de(!(1!==n.rank&&2!==n.rank||1!==r.rank&&2!==r.rank),(()=>`Error in dot: inputs must all be rank 1 or 2, but got ranks ${n.rank} and ${r.rank}.`));const s=1===n.rank?n.size:n.shape[1],a=1===r.rank?r.size:r.shape[0];if(de(s===a,(()=>`Error in dot: inner dimensions of inputs must match, but got ${s} and ${a}.`)),1===n.rank&&1===r.rank){const e=Mn(n,[1,-1]),t=Mn(r,[-1,1]),s=Ln(e,t);return Mn(s,[])}if(1===n.rank&&2===r.rank){const e=Mn(n,[1,-1]),t=Mn(r,[r.shape[0],r.shape[1]]),s=Ln(e,t);return Mn(s,[s.size])}if(2===n.rank&&1===r.rank){const e=Mn(r,[-1,1]),t=Ln(n,e);return Mn(t,[t.size])}{const e=Mn(r,[r.shape[0],r.shape[1]]);return Ln(n,e)}}});const Or=pn({einsum_:function(e,...t){const n=t.map(((e,t)=>on(e,`tensors${t}`,"einsum"))),r={equation:e};return tn.runKernel("Einsum",n,r)}});const Mr=pn({elu_:function(e){const t={x:on(e,"x","elu","float32")};return tn.runKernel("Elu",t)}});const Cr=pn({ensureShape_:function(e,t){const n=on(e,"x","ensureShape","string_or_numeric");if(!function(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(null!==e[n]&&null!==t[n]&&e[n]!==t[n])return!1;return!0}(n.shape,t))throw new Error(`EnsureShape: Shape of tensor ${n.shape} is not compatible with expected shape ${t}`);return e}});const Fr=pn({erf_:function(e){let t=on(e,"x","erf");de("int32"===t.dtype||"float32"===t.dtype,(()=>"Input dtype must be `int32` or `float32`.")),"int32"===t.dtype&&(t=vn(t,"float32"));const n={x:t};return tn.runKernel("Erf",n)}});function Rr(e,t){return function(e,t,n){const r=e.length+t.length,s=[];let a=0,o=0;for(let i=0;i<r;i++)-1===n.indexOf(i)?s.push(e[a++]):s.push(t[o++]);return s}(e,t.map((e=>1)),t)}const zr=pn({max_:function(e,t=null,n=!1){const r={x:on(e,"x","max")},s={reductionIndices:t,keepDims:n};return tn.runKernel("Max",r,s)}});const Lr=pn({min_:function(e,t=null,n=!1){const r={x:on(e,"x","min")},s={axis:t,keepDims:n};return tn.runKernel("Min",r,s)}});const Vr=pn({pow_:function(e,t){let n=on(e,"base","pow"),r=on(t,"exp","pow");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("Pow",s)}});function Br(e,t,n,r){if(null==r)r=ve(e);else if("complex64"===r)throw new Error("Cannot construct a complex64 tensor directly. Please use tf.complex(real, imag).");if(Ht(e)||Gt(e)){if("float32"!==r&&"int32"!==r)throw new Error(`Creating tensor from GPU data only supports 'float32'|'int32' dtype, while the dtype is ${r}.`);return tn.backend.createTensorFromGPUData(e,t||n,r)}if(!St(e)&&!Array.isArray(e)&&"number"!=typeof e&&"boolean"!=typeof e&&"string"!=typeof e)throw new Error("values passed to tensor(values) must be a number/boolean/string or an array of numbers/booleans/strings, or a TypedArray");if(null!=t){De(t);const e=ye(t),r=ye(n);de(e===r,(()=>`Based on the provided shape, [${t}], the tensor should have ${e} values but has ${r}`));for(let e=0;e<n.length;++e){const r=n[e],s=e!==n.length-1||r!==ye(t.slice(e));de(n[e]===t[e]||!s,(()=>`Error creating a new Tensor. Inferred shape (${n}) does not match the provided shape (${t}). `))}}return St(e)||Array.isArray(e)||(e=[e]),t=t||n,e="string"!==r?Tt(e,r):Et(e,[],!0),tn.makeTensor(e,t,r)}function Pr(e,t){if((St(e)&&"string"!==t||Array.isArray(e))&&"complex64"!==t)throw new Error("Error creating a new Scalar: value must be a primitive (number|boolean|string)");if("string"===t&&St(e)&&!(e instanceof Uint8Array))throw new Error("When making a scalar from encoded string, the value must be `Uint8Array`.");return Br(e,[],[],t)}const Kr=pn({sqrt_:function(e){const t={x:on(e,"x","sqrt","float32")};return tn.runKernel("Sqrt",t)}});const qr=pn({square_:function(e){const t=on(e,"x","square");return tn.runKernel("Square",{x:t},{})}});const Ur=pn({sum_:function(e,t=null,n=!1){let r=on(e,"x","sum");"bool"===r.dtype&&(r=vn(r,"int32"));const s={x:r},a={axis:t,keepDims:n};return tn.runKernel("Sum",s,a)}});function Wr(e,t,n=null){if(0===e.rank)return ln(e);if(1!==e.rank&&null===n)return Wr(Mn(e,[-1]),t,n);if(1===e.rank||"number"==typeof n||Array.isArray(n)&&1===n.length){if(1===t)return Ur(ln(e),n);if(t===1/0)return zr(ln(e),n);if(t===-1/0)return Lr(ln(e),n);if("euclidean"===t||2===t)return Kr(Ur(Vr(ln(e),Pr(2,"int32")),n));throw new Error(`Error in norm: invalid ord value: ${t}`)}if(Array.isArray(n)&&2===n.length){if(1===t)return zr(Ur(ln(e),n[0]),n[1]-1);if(t===1/0)return zr(Ur(ln(e),n[1]),n[0]);if(t===-1/0)return Lr(Ur(ln(e),n[1]),n[0]);if("fro"===t||"euclidean"===t)return Kr(Ur(qr(e),n));throw new Error(`Error in norm: invalid ord value: ${t}`)}throw new Error(`Error in norm: invalid axis: ${n}`)}const jr=pn({norm_:function(e,t="euclidean",n=null,r=!1){const s=Wr(e=on(e,"x","norm"),t,n);let a=s.shape;if(r){const t=Ne(n,e.shape);a=Rr(s.shape,t)}return Mn(s,a)}});const Gr=pn({euclideanNorm_:function(e,t=null,n=!1){return jr(e,"euclidean",t,n)}});const Hr=pn({exp_:function(e){const t={x:on(e,"x","exp")};return tn.runKernel("Exp",t)}});const Zr=pn({expandDims_:function(e,t=0){const n=on(e,"x","expandDims","string_or_numeric");de(t<=n.rank,(()=>"Axis must be <= rank of the tensor"));const r={input:n},s={dim:t};return tn.runKernel("ExpandDims",r,s)}});const Qr=pn({expm1_:function(e){const t={x:on(e,"x","expm1")};return tn.runKernel("Expm1",t)}});const Xr=pn({tile_:function(e,t){const n=on(e,"x","tile","string_or_numeric");de(n.rank===t.length,(()=>`Error in transpose: rank of input ${n.rank} must match length of reps ${t}.`));const r={x:n},s={reps:t};return tn.runKernel("Tile",r,s)}});const Yr=pn({eye_:function(e,t,n,r="float32"){null==t&&(t=e);const s=Jn([e,t],r),a=e<=t?e:t;for(let e=0;e<a;++e)s.set(1,e,e);const o=Mn(s.toTensor(),[e,t]);if(null==n)return o;if(1===n.length)return Xr(Zr(o,0),[n[0],1,1]);if(2===n.length)return Xr(Zr(Zr(o,0),0),[n[0],n[1],1,1]);if(3===n.length)return Xr(Zr(Zr(Zr(o,0),0),0),[n[0],n[1],n[2],1,1]);throw new Error(`eye() currently supports only 1D and 2D batchShapes, but received ${n.length}D.`)}});const Jr=pn({floor_:function(e){const t={x:on(e,"x","floor","float32")};return tn.runKernel("Floor",t)}});const es=pn({gather_:function(e,t,n=0,r=0){const s={x:on(e,"x","gather"),indices:on(t,"indices","gather","int32")},a={axis:n,batchDims:r};return tn.runKernel("GatherV2",s,a)}});const ts=pn({greater_:function(e,t){let n=on(e,"a","greater","string_or_numeric"),r=on(t,"b","greater","string_or_numeric");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("Greater",s)}});const ns=pn({greaterEqual_:function(e,t){let n=on(e,"a","greaterEqual","string_or_numeric"),r=on(t,"b","greaterEqual","string_or_numeric");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("GreaterEqual",s)}});const rs=pn({imag_:function(e){const t={input:on(e,"input","imag")};return tn.runKernel("Imag",t)}});const ss=pn({isFinite_:function(e){const t={x:on(e,"x","isFinite")};return tn.runKernel("IsFinite",t)}});const as=pn({isInf_:function(e){const t={x:on(e,"x","isInf")};return tn.runKernel("IsInf",t)}});const os=pn({isNaN_:function(e){const t={x:on(e,"x","isNaN")};return tn.runKernel("IsNan",t)}});const is=pn({leakyRelu_:function(e,t=.2){const n={x:on(e,"x","leakyRelu")},r={alpha:t};return tn.runKernel("LeakyRelu",n,r)}});const us=pn({less_:function(e,t){let n=on(e,"a","less","string_or_numeric"),r=on(t,"b","less","string_or_numeric");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("Less",s)}});const ps=pn({lessEqual_:function(e,t){let n=on(e,"a","lessEqual","string_or_numeric"),r=on(t,"b","lessEqual","string_or_numeric");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("LessEqual",s)}});const ls=pn({localResponseNormalization_:function(e,t=5,n=1,r=1,s=.5){const a=on(e,"x","localResponseNormalization");de(4===a.rank||3===a.rank,(()=>`Error in localResponseNormalization: x must be rank 3 or 4 but got\n               rank ${a.rank}.`)),de(be(t),(()=>`Error in localResponseNormalization: depthRadius must be an integer but got depthRadius ${t}.`));let o=a,i=!1;3===a.rank&&(i=!0,o=Mn(a,[1,a.shape[0],a.shape[1],a.shape[2]]));const u={x:o},p={depthRadius:t,bias:n,alpha:r,beta:s},l=tn.runKernel("LRN",u,p);return i?Mn(l,[l.shape[1],l.shape[2],l.shape[3]]):l}});const cs=pn({log_:function(e){const t={x:on(e,"x","log","float32")};return tn.runKernel("Log",t)}});const hs=pn({log1p_:function(e){const t={x:on(e,"x","log1p")};return tn.runKernel("Log1p",t)}});function ds(e){return tn.customGrad(e)}const ms=pn({neg_:function(e){const t={x:on(e,"x","neg")};return tn.runKernel("Neg",t)}});const fs=pn({softplus_:function(e){const t={x:on(e,"x","softplus")};return tn.runKernel("Softplus",t)}});const ys=pn({logSigmoid_:function(e){const t=on(e,"x","logSigmoid"),n=ds((e=>({value:ms(fs(ms(e))),gradFunc:t=>Vn(t,Bn(ms(e)))})));return n(t)}});const gs=pn({sub_:function(e,t){let n=on(e,"a","sub"),r=on(t,"b","sub");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("Sub",s)}});const bs=pn({logSoftmax_:function(e,t=-1){const n=on(e,"logits","logSoftmax");if(-1===t&&(t=n.rank-1),t!==n.rank-1)throw Error(`Log Softmax along a non-last dimension is not yet supported. Logits was rank ${n.rank} and axis was ${t}`);const r=ds(((e,n)=>{const r=zr(e,t,!0),s=gs(e,r),a=gs(vn(s,"float32"),cs(Ur(Hr(s),t,!0)));n([a]);return{value:a,gradFunc:(e,n)=>{const[r]=n,s=Hr(r);return gs(e,Vn(Ur(e,t,!0),s))}}}));return r(n)}});const xs=pn({logSumExp_:function(e,t=null,n=!1){const r=on(e,"x","logSumExp"),s=Ne(t,r.shape),a=zr(r,s,!0),o=gs(r,a),i=Hr(o),u=Ur(i,s),p=cs(u),l=dn(Mn(a,p.shape),p);if(n){const e=Rr(l.shape,s);return Mn(l,e)}return l}});const Ns=pn({logicalAnd_:function(e,t){const n=on(e,"a","logicalAnd","bool"),r=on(t,"b","logicalAnd","bool");Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("LogicalAnd",s)}});const ws=pn({logicalNot_:function(e){const t={x:on(e,"x","logicalNot","bool")};return tn.runKernel("LogicalNot",t)}});const ks=pn({logicalOr_:function(e,t){const n=on(e,"a","logicalOr","bool"),r=on(t,"b","logicalOr","bool");Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("LogicalOr",s)}});const Ts=pn({logicalXor_:function(e,t){const n=on(e,"a","logicalXor","bool"),r=on(t,"b","logicalXor","bool");return Sr(n.shape,r.shape),Ns(ks(e,t),ws(Ns(e,t)))}});const vs=pn({searchSorted_:function(e,t,n="left"){const r=on(e,"sortedSequence","searchSorted"),s=on(t,"values","searchSorted"),a=r.shape[r.shape.length-1],o=s.shape[s.shape.length-1],i=Mn(r,[-1,a]),u=Mn(s,[-1,o]);if(i.rank<2)throw new Error("Sorted input argument must be at least 2-dimensional");if(i.shape[0]!==u.shape[0])throw new Error("Leading dimension of 'sortedSequence' and 'values' must match.");if(ye(u.shape)>=2147483648)throw new Error("values tensor size must less than 2147483648");if(i.shape[1]>=2147483648)throw new Error(`trailing dim_size must less than 2147483648 for int32 output type, was ${i.shape[1]}`);const p={sortedSequence:i,values:u},l={side:n};return tn.runKernel("SearchSorted",p,l)}});const _s=pn({maxPool_:function(e,t,n,r,s){const a=on(e,"x","maxPool");let o=a,i=!1;3===a.rank&&(i=!0,o=Mn(a,[1,a.shape[0],a.shape[1],a.shape[2]])),de(4===o.rank,(()=>`Error in maxPool: input must be rank 4 but got rank ${o.rank}.`)),de(An(n,1),(()=>`Error in maxPool: Either strides or dilations must be 1. Got strides ${n} and dilations '1'`)),On("maxPool",r,s);const u={x:o},p={filterSize:t,strides:n,pad:r,dimRoundingMode:s},l=tn.runKernel("MaxPool",u,p);return i?Mn(l,[l.shape[1],l.shape[2],l.shape[3]]):l}});const Ss=pn({maxPool3d_:function(e,t=[1,1,1],n,r,s,a="NDHWC"){const o=on(e,"x","maxPool3d");let i=o,u=!1;4===o.rank&&(u=!0,i=Mn(o,[1,o.shape[0],o.shape[1],o.shape[2],o.shape[3]])),de(5===i.rank,(()=>`Error in maxPool3d: x must be rank 5 but got rank ${i.rank}.`)),de("NDHWC"===a,(()=>`Error in maxPool3d: Only NDHWC is currently supported, but got dataFormat of ${a}`)),On("maxPool3d",r,s);const p={x:i},l={filterSize:t,strides:n,pad:r,dimRoundingMode:s,dataFormat:a},c=tn.runKernel("MaxPool3D",p,l);return u?Mn(c,[c.shape[1],c.shape[2],c.shape[3],c.shape[4]]):c}});const Es=pn({maxPoolWithArgmax_:function(e,t,n,r,s=!1){const a={x:on(e,"x","maxPoolWithArgmax")},o={filterSize:t,strides:n,pad:r,includeBatchInIndex:s},i=tn.runKernel("MaxPoolWithArgmax",a,o);return{result:i[0],indexes:i[1]}}});const Is=pn({maximum_:function(e,t){let n=on(e,"a","maximum"),r=on(t,"b","maximum");[n,r]=Zt(n,r),"bool"===n.dtype&&(n=vn(n,"int32"),r=vn(r,"int32")),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("Maximum",s)}});const $s=pn({mean_:function(e,t=null,n=!1){const r={x:on(e,"x","mean")},s={axis:t,keepDims:n};return tn.runKernel("Mean",r,s)}});function As(e,t="float32"){if(De(e),"complex64"===t){const t=As(e,"float32"),n=As(e,"float32");return rr(t,n)}const n=Ae(ye(e),t);return tn.makeTensor(n,e,t)}function Ds(e,t="float32"){if(De(e),"complex64"===t){const t=Ds(e,"float32"),n=As(e,"float32");return rr(t,n)}const n=$e(ye(e),t);return tn.makeTensor(n,e,t)}const Os=pn({minimum_:function(e,t){let n=on(e,"a","minimum"),r=on(t,"b","minimum");[n,r]=Zt(n,r),"bool"===n.dtype&&(n=vn(n,"int32"),r=vn(r,"int32")),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("Minimum",s)}});const Ms=pn({mirrorPad_:function(e,t,n){de("reflect"===n||"symmetric"===n,(()=>`Invalid mode. Mode must be either reflect or symmetric. Got ${n}.`));const r=on(e,"x","mirrorPad");if(0===r.rank)throw new Error("mirrorPad(scalar) is not defined. Pass non-scalar to mirrorPad");de(t.length===r.rank,(()=>`Padding doesn't match input. Must be ${r.rank}. Got ${t.length}.`));const s="reflect"===n?1:0;for(let e=0;e<r.rank;e++)de(2===t[e].length,(()=>"Invalid number of paddings. Must be length of 2 each.")),de(t[e][0]>=0&&t[e][0]<=r.shape[e]-s&&t[e][1]>=0&&t[e][1]<=r.shape[e]-s,(()=>`Padding in dimension ${e} cannot be greater than or equal to ${r.shape[e]-s} or less than 0 for input of shape ${r.shape}`));const a={paddings:t,mode:n},o={x:r};return tn.runKernel("MirrorPad",o,a)}});const Cs=pn({mod_:function(e,t){let n=on(e,"a","mod"),r=on(t,"b","mod");[n,r]=Zt(n,r);const s={a:n,b:r};return tn.runKernel("Mod",s)}});const Fs=pn({moments_:function(e,t=null,n=!1){const r=Ne(t,(e=on(e,"x","moments")).shape),s=$s(e,r,n);let a=s.shape;n||(a=Rr(s.shape,r));const o=qr(gs(vn(e,"float32"),Mn(s,a)));return{mean:s,variance:$s(o,r,n)}}});const Rs=pn({multiRNNCell_:function(e,t,n,r){const s=on(t,"data","multiRNNCell"),a=un(n,"c","multiRNNCell"),o=un(r,"h","multiRNNCell");let i=s;const u=[];for(let t=0;t<e.length;t++){const n=e[t](i,a[t],o[t]);u.push(n[0]),u.push(n[1]),i=n[1]}const p=[],l=[];for(let e=0;e<u.length;e+=2)p.push(u[e]),l.push(u[e+1]);return[p,l]}});const zs=pn({multinomial_:function(e,t,n,r=!1){const s=on(e,"logits","multinomial"),a=s.size,o=s.rank;if(a<2)throw new Error(`Error in multinomial: you need at least 2 outcomes, but got ${a}.`);if(o>2)throw new Error(`Rank of probabilities must be 1 or 2, but is ${o}`);n=n||Math.random();const i={logits:1===o?Mn(s,[1,-1]):s},u={numSamples:t,seed:n,normalized:r},p=tn.runKernel("Multinomial",i,u);return 1===o?Mn(p,[p.size]):p}});const Ls=pn({notEqual_:function(e,t){let n=on(e,"a","notEqual","string_or_numeric"),r=on(t,"b","notEqual","string_or_numeric");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("NotEqual",s)}});const Vs=pn({oneHot_:function(e,t,n=1,r=0,s="int32"){if(t<2)throw new Error(`Error in oneHot: depth must be >=2, but it is ${t}`);const a={indices:on(e,"indices","oneHot","int32")},o={dtype:s,depth:t,onValue:n,offValue:r};return tn.runKernel("OneHot",a,o)}});const Bs=pn({onesLike_:function(e){const t={x:on(e,"x","onesLike")};return tn.runKernel("OnesLike",t)}});const Ps=pn({outerProduct_:function(e,t){const n=on(e,"v1","outerProduct"),r=on(t,"v2","outerProduct");de(1===n.rank&&1===r.rank,(()=>`Error in outerProduct: inputs must be rank 1, but got ranks ${n.rank} and ${r.rank}.`));const s=Mn(n,[-1,1]),a=Mn(r,[1,-1]);return Ln(s,a)}});const Ks=pn({pad_:function(e,t,n=0){const r=on(e,"x","pad");if(0===r.rank)throw new Error("pad(scalar) is not defined. Pass non-scalar to pad");const s={paddings:t,constantValue:n},a={x:r};return tn.runKernel("PadV2",a,s)}});const qs=pn({pad1d_:function(e,t,n=0){return de(2===t.length,(()=>"Invalid number of paddings. Must be length of 2.")),Ks(e,[t],n)}});const Us=pn({pad2d_:function(e,t,n=0){return de(2===t.length&&2===t[0].length&&2===t[1].length,(()=>"Invalid number of paddings. Must be length of 2 each.")),Ks(e,t,n)}});const Ws=pn({pad3d_:function(e,t,n=0){return de(3===t.length&&2===t[0].length&&2===t[1].length&&2===t[2].length,(()=>"Invalid number of paddings. Must be length of 2 each.")),Ks(e,t,n)}});const js=pn({pad4d_:function(e,t,n=0){return de(4===t.length&&2===t[0].length&&2===t[1].length&&2===t[2].length&&2===t[3].length,(()=>"Invalid number of paddings. Must be length of 2 each.")),Ks(e,t,n)}});const Gs=pn({spaceToBatchND_:function(e,t,n){const r=on(e,"x","spaceToBatchND");de(r.rank>=1+t.length,(()=>`input rank ${r.rank} should be > than [blockShape] ${t.length}`)),de(n.length===t.length,(()=>`paddings.shape[0] ${n.length} must be equal to [blockShape] ${t.length}`)),de(r.shape.reduce(((e,r,s)=>s>0&&s<=t.length?e&&(r+n[s-1][0]+n[s-1][1])%t[s-1]==0:e),!0),(()=>`input spatial dimensions ${r.shape.slice(1)} with paddings ${n.toString()} must be divisible by blockShapes ${t.toString()}`));const s={x:r},a={blockShape:t,paddings:n};return tn.runKernel("SpaceToBatchND",s,a)}});const Hs=pn({pool_:function(e,t,n,r,s,a,o){null==s&&(s=[1,1]),null==a&&(a=1),0===r&&(r="valid");const i=on(e,"x","maxPool");let u=i,p=!1;3===i.rank&&(p=!0,u=Mn(i,[1,i.shape[0],i.shape[1],i.shape[2]])),de(An(a,s),(()=>`Error in pool: Either strides or dilations must be 1. Got strides ${a} and dilations '${s}'`));const l=function(e,t,n,r,s,a,o="channelsLast"){const[i,u]=Sn(t);let p;if("channelsLast"===o)p=[i,u,e[3],e[3]];else{if("channelsFirst"!==o)throw new Error(`Unknown dataFormat ${o}`);p=[i,u,e[1],e[1]]}return _n(e,p,n,r,s,a,!1,o)}(u.shape,t,a,s,r),c=[l.dilationHeight,l.dilationWidth];let h;h="same"===r?function(e,t){const n=e.map(((e,n)=>e+(e-1)*(t[n]-1))).map((e=>e-1)),r=n.map((e=>Math.floor(e/2))),s=n.map(((e,t)=>e-r[t]));return n.map(((e,t)=>[r[t],s[t]]))}([l.filterHeight,l.filterWidth],c):[[0,0],[0,0]];const d=1===c[0]&&1===c[1],[m,f]=function(e,t,n){const r=n.map((e=>e[0])),s=n.map((e=>e[1])),a=e.concat(r,s),o=t.map(((e,t)=>(e-a[t]%e)%e)),i=s.map(((e,t)=>e+o[t])),u=t.map(((e,t)=>[r[t],i[t]])),p=t.map(((e,t)=>[0,o[t]]));return[u,p]}([l.inHeight,l.inWidth],c,h),y=d?r:"valid",g=d?u:Gs(u,c,m),b=("avg"===n?()=>Cn(g,t,a,y,o):()=>_s(g,t,a,y,o))(),x=d?b:Un(b,c,f);return p?Mn(x,[x.shape[1],x.shape[2],x.shape[3]]):x}});const Zs=pn({prelu_:function(e,t){const n={x:on(e,"x","prelu"),alpha:on(t,"alpha","prelu")};return tn.runKernel("Prelu",n)}});const Qs=pn({prod_:function(e,t=null,n=!1){let r=on(e,"x","prod");"bool"===r.dtype&&(r=vn(r,"int32"));const s={x:r},a={axis:t,keepDims:n};return tn.runKernel("Prod",s,a)}});const Xs=pn({raggedGather_:function(e,t,n,r){const s={paramsNestedSplits:e.map(((e,t)=>on(e,`tensors${t}`,"raggedGather","int32"))),paramsDenseValues:on(t,"paramsDenseValues","raggedGather"),indices:on(n,"indices","raggedGather","int32")},a={outputRaggedRank:r},o=tn.runKernel("RaggedGather",s,a);return{outputNestedSplits:o.slice(0,o.length-1),outputDenseValues:o[o.length-1]}}});const Ys=pn({raggedRange_:function(e,t,n){const r=on(e,"starts","raggedRange"),s={starts:r,limits:on(t,"limits","raggedRange",r.dtype),deltas:on(n,"deltas","raggedRange",r.dtype)},a=tn.runKernel("RaggedRange",s);return{rtNestedSplits:a[0],rtDenseValues:a[1]}}});const Js=pn({raggedTensorToTensor_:function(e,t,n,r,s){const a=on(e,"shape","raggedTensorToTensor","int32"),o=on(t,"values","raggedTensorToTensor"),i={shape:a,values:o,defaultValue:on(n,"defaultValue","raggedTensorToTensor",o.dtype),rowPartitionTensors:r.map(((e,t)=>on(e,`tensors${t}`,"raggedTensorToTensor","int32")))},u={rowPartitionTypes:s};return tn.runKernel("RaggedTensorToTensor",i,u)}});const ea=pn({rand_:function(e,t,n){De(e);const r=ye(e);let s=null;if(null==n||"float32"===n)s=new Float32Array(r);else if("int32"===n)s=new Int32Array(r);else{if("bool"!==n)throw new Error(`Unknown data type ${n}`);s=new Uint8Array(r)}for(let e=0;e<r;e++)s[e]=t();return tn.makeTensor(s,e,n)}});var ta={exports:{}};!function(e,t,n){function r(e){var t,n=this,r=(t=4022871197,function(e){e=String(e);for(var n=0;n<e.length;n++){var r=.02519603282416938*(t+=e.charCodeAt(n));r-=t=r>>>0,t=(r*=t)>>>0,t+=4294967296*(r-=t)}return 2.3283064365386963e-10*(t>>>0)});n.next=function(){var e=2091639*n.s0+2.3283064365386963e-10*n.c;return n.s0=n.s1,n.s1=n.s2,n.s2=e-(n.c=0|e)},n.c=1,n.s0=r(" "),n.s1=r(" "),n.s2=r(" "),n.s0-=r(e),n.s0<0&&(n.s0+=1),n.s1-=r(e),n.s1<0&&(n.s1+=1),n.s2-=r(e),n.s2<0&&(n.s2+=1),r=null}function s(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function a(e,t){var n=new r(e),a=t&&t.state,o=n.next;return o.int32=function(){return 4294967296*n.next()|0},o.double=function(){return o()+11102230246251565e-32*(2097152*o()|0)},o.quick=o,a&&("object"==typeof a&&s(a,n),o.state=function(){return s(n,{})}),o}t&&t.exports?t.exports=a:n&&n.amd?n((function(){return a})):this.alea=a}(0,ta,!1);var na=ta.exports,ra={exports:{}};!function(e,t,n){function r(e){var t=this,n="";t.x=0,t.y=0,t.z=0,t.w=0,t.next=function(){var e=t.x^t.x<<11;return t.x=t.y,t.y=t.z,t.z=t.w,t.w^=t.w>>>19^e^e>>>8},e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),t.next()}function s(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t}function a(e,t){var n=new r(e),a=t&&t.state,o=function(){return(n.next()>>>0)/4294967296};return o.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=n.next,o.quick=o,a&&("object"==typeof a&&s(a,n),o.state=function(){return s(n,{})}),o}t&&t.exports?t.exports=a:n&&n.amd?n((function(){return a})):this.xor128=a}(0,ra,!1);var sa=ra.exports,aa={exports:{}};!function(e,t,n){function r(e){var t=this,n="";t.next=function(){var e=t.x^t.x>>>2;return t.x=t.y,t.y=t.z,t.z=t.w,t.w=t.v,(t.d=t.d+362437|0)+(t.v=t.v^t.v<<4^e^e<<1)|0},t.x=0,t.y=0,t.z=0,t.w=0,t.v=0,e===(0|e)?t.x=e:n+=e;for(var r=0;r<n.length+64;r++)t.x^=0|n.charCodeAt(r),r==n.length&&(t.d=t.x<<10^t.x>>>4),t.next()}function s(e,t){return t.x=e.x,t.y=e.y,t.z=e.z,t.w=e.w,t.v=e.v,t.d=e.d,t}function a(e,t){var n=new r(e),a=t&&t.state,o=function(){return(n.next()>>>0)/4294967296};return o.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=n.next,o.quick=o,a&&("object"==typeof a&&s(a,n),o.state=function(){return s(n,{})}),o}t&&t.exports?t.exports=a:n&&n.amd?n((function(){return a})):this.xorwow=a}(0,aa,!1);var oa=aa.exports,ia={exports:{}};!function(e,t,n){function r(e){var t=this;t.next=function(){var e,n,r=t.x,s=t.i;return e=r[s],n=(e^=e>>>7)^e<<24,n^=(e=r[s+1&7])^e>>>10,n^=(e=r[s+3&7])^e>>>3,n^=(e=r[s+4&7])^e<<7,e=r[s+7&7],n^=(e^=e<<13)^e<<9,r[s]=n,t.i=s+1&7,n},function(e,t){var n,r=[];if(t===(0|t))r[0]=t;else for(t=""+t,n=0;n<t.length;++n)r[7&n]=r[7&n]<<15^t.charCodeAt(n)+r[n+1&7]<<13;for(;r.length<8;)r.push(0);for(n=0;n<8&&0===r[n];++n);for(8==n?r[7]=-1:r[n],e.x=r,e.i=0,n=256;n>0;--n)e.next()}(t,e)}function s(e,t){return t.x=e.x.slice(),t.i=e.i,t}function a(e,t){null==e&&(e=+new Date);var n=new r(e),a=t&&t.state,o=function(){return(n.next()>>>0)/4294967296};return o.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=n.next,o.quick=o,a&&(a.x&&s(a,n),o.state=function(){return s(n,{})}),o}t&&t.exports?t.exports=a:n&&n.amd?n((function(){return a})):this.xorshift7=a}(0,ia,!1);var ua=ia.exports,pa={exports:{}};!function(e,t,n){function r(e){var t=this;t.next=function(){var e,n,r=t.w,s=t.X,a=t.i;return t.w=r=r+1640531527|0,n=s[a+34&127],e=s[a=a+1&127],n^=n<<13,e^=e<<17,n^=n>>>15,e^=e>>>12,n=s[a]=n^e,t.i=a,n+(r^r>>>16)|0},function(e,t){var n,r,s,a,o,i=[],u=128;for(t===(0|t)?(r=t,t=null):(t+="\0",r=0,u=Math.max(u,t.length)),s=0,a=-32;a<u;++a)t&&(r^=t.charCodeAt((a+32)%t.length)),0===a&&(o=r),r^=r<<10,r^=r>>>15,r^=r<<4,r^=r>>>13,a>=0&&(o=o+1640531527|0,s=0==(n=i[127&a]^=r+o)?s+1:0);for(s>=128&&(i[127&(t&&t.length||0)]=-1),s=127,a=512;a>0;--a)r=i[s+34&127],n=i[s=s+1&127],r^=r<<13,n^=n<<17,r^=r>>>15,n^=n>>>12,i[s]=r^n;e.w=o,e.X=i,e.i=s}(t,e)}function s(e,t){return t.i=e.i,t.w=e.w,t.X=e.X.slice(),t}function a(e,t){null==e&&(e=+new Date);var n=new r(e),a=t&&t.state,o=function(){return(n.next()>>>0)/4294967296};return o.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=n.next,o.quick=o,a&&(a.X&&s(a,n),o.state=function(){return s(n,{})}),o}t&&t.exports?t.exports=a:n&&n.amd?n((function(){return a})):this.xor4096=a}(0,pa,!1);var la=pa.exports,ca={exports:{}};!function(e,t,n){function r(e){var t=this,n="";t.next=function(){var e=t.b,n=t.c,r=t.d,s=t.a;return e=e<<25^e>>>7^n,n=n-r|0,r=r<<24^r>>>8^s,s=s-e|0,t.b=e=e<<20^e>>>12^n,t.c=n=n-r|0,t.d=r<<16^n>>>16^s,t.a=s-e|0},t.a=0,t.b=0,t.c=-1640531527,t.d=1367130551,e===Math.floor(e)?(t.a=e/4294967296|0,t.b=0|e):n+=e;for(var r=0;r<n.length+20;r++)t.b^=0|n.charCodeAt(r),t.next()}function s(e,t){return t.a=e.a,t.b=e.b,t.c=e.c,t.d=e.d,t}function a(e,t){var n=new r(e),a=t&&t.state,o=function(){return(n.next()>>>0)/4294967296};return o.double=function(){do{var e=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===e);return e},o.int32=n.next,o.quick=o,a&&("object"==typeof a&&s(a,n),o.state=function(){return s(n,{})}),o}t&&t.exports?t.exports=a:n&&n.amd?n((function(){return a})):this.tychei=a}(0,ca,!1);var ha,da=ca.exports,ma={exports:{}},fa=He({__proto__:null,default:{}});ha=ma,function(e,t,n){var r,s=256,a=n.pow(s,6),o=n.pow(2,52),i=2*o,u=255;function p(u,p,f){var y=[],g=d(h((p=1==p?{entropy:!0}:p||{}).entropy?[u,m(t)]:null==u?function(){try{var n;return r&&(n=r.randomBytes)?n=n(s):(n=new Uint8Array(s),(e.crypto||e.msCrypto).getRandomValues(n)),m(n)}catch(n){var a=e.navigator,o=a&&a.plugins;return[+new Date,e,o,e.screen,m(t)]}}():u,3),y),b=new l(y),x=function(){for(var e=b.g(6),t=a,n=0;e<o;)e=(e+n)*s,t*=s,n=b.g(1);for(;e>=i;)e/=2,t/=2,n>>>=1;return(e+n)/t};return x.int32=function(){return 0|b.g(4)},x.quick=function(){return b.g(4)/4294967296},x.double=x,d(m(b.S),t),(p.pass||f||function(e,t,r,s){return s&&(s.S&&c(s,b),e.state=function(){return c(b,{})}),r?(n.random=e,t):e})(x,g,"global"in p?p.global:this==n,p.state)}function l(e){var t,n=e.length,r=this,a=0,o=r.i=r.j=0,i=r.S=[];for(n||(e=[n++]);a<s;)i[a]=a++;for(a=0;a<s;a++)i[a]=i[o=u&o+e[a%n]+(t=i[a])],i[o]=t;(r.g=function(e){for(var t,n=0,a=r.i,o=r.j,i=r.S;e--;)t=i[a=u&a+1],n=n*s+i[u&(i[a]=i[o=u&o+t])+(i[o]=t)];return r.i=a,r.j=o,n})(s)}function c(e,t){return t.i=e.i,t.j=e.j,t.S=e.S.slice(),t}function h(e,t){var n,r=[],s=typeof e;if(t&&"object"==s)for(n in e)try{r.push(h(e[n],t-1))}catch(e){}return r.length?r:"string"==s?e:e+"\0"}function d(e,t){for(var n,r=e+"",s=0;s<r.length;)t[u&s]=u&(n^=19*t[u&s])+r.charCodeAt(s++);return m(t)}function m(e){return String.fromCharCode.apply(0,e)}if(d(n.random(),t),ha.exports){ha.exports=p;try{r=fa}catch(e){}}else n.seedrandom=p}("undefined"!=typeof self?self:je,[],Math);var ya=na,ga=sa,ba=oa,xa=ua,Na=la,wa=da,ka=ma.exports;ka.alea=ya,ka.xor128=ga,ka.xorwow=ba,ka.xorshift7=xa,ka.xor4096=Na,ka.tychei=wa;var Ta=ka;class va{constructor(e,t,n,r,s){this.mean=e,this.stdDev=t,this.dtype=n,this.nextVal=NaN,this.truncated=r,this.truncated&&(this.upper=this.mean+2*this.stdDev,this.lower=this.mean-2*this.stdDev);const a=s||Math.random();this.random=Ta.alea(a.toString())}nextValue(){if(!isNaN(this.nextVal)){const e=this.nextVal;return this.nextVal=NaN,e}let e,t,n=!1;for(;!n;){let r,s,a;do{r=2*this.random()-1,s=2*this.random()-1,a=r*r+s*s}while(a>=1||0===a);const o=Math.sqrt(-2*Math.log(a)/a);e=this.mean+this.stdDev*r*o,t=this.mean+this.stdDev*s*o,this.truncated&&!this.isValidTruncated(e)||(n=!0)}return this.truncated&&!this.isValidTruncated(t)||(this.nextVal=this.convertValue(t)),this.convertValue(e)}convertValue(e){return null==this.dtype||"float32"===this.dtype?e:Math.round(e)}isValidTruncated(e){return e<=this.upper&&e>=this.lower}}class _a{constructor(e,t,n,r){this.alpha=e,this.beta=1/t,this.dtype=n;const s=r||Math.random();this.randu=Ta.alea(s.toString()),this.randn=new va(0,1,n,!1,this.randu()),this.d=e<1?e+2/3:e-1/3,this.c=1/Math.sqrt(9*this.d)}nextValue(){let e,t,n,r,s,a;for(;;){do{r=this.randn.nextValue(),a=1+this.c*r}while(a<=0);if(a*=a*a,e=r*r,t=1-.331*e*e,n=.5*e+this.d*(1-a+Math.log(a)),s=this.randu(),s<t||Math.log(s)<n)break}return a=1/this.beta*this.d*a,this.alpha<1&&(a*=Math.pow(this.randu(),1/this.alpha)),this.convertValue(a)}convertValue(e){return"float32"===this.dtype?e:Math.round(e)}}class Sa{constructor(e=0,t=1,n,r){if(this.canReturnFloat=()=>null==this.dtype||"float32"===this.dtype,this.min=e,this.range=t-e,this.dtype=n,null==r&&(r=Math.random()),"number"==typeof r&&(r=r.toString()),!this.canReturnFloat()&&this.range<=1)throw new Error(`The difference between ${e} - ${t} <= 1 and dtype is not float`);this.random=Ta.alea(r)}convertValue(e){return this.canReturnFloat()?e:Math.round(e)}nextValue(){return this.convertValue(this.min+this.range*this.random())}}const Ea=pn({randomGamma_:function(e,t,n=1,r="float32",s){if(De(e),null==n&&(n=1),null==r&&(r="float32"),"float32"!==r&&"int32"!==r)throw new Error(`Unsupported data type ${r}`);const a=new _a(t,n,r,s),o=Jn(e,r);for(let e=0;e<o.values.length;e++)o.values[e]=a.nextValue();return o.toTensor()}});const Ia=pn({randomNormal_:function(e,t=0,n=1,r,s){if(De(e),null!=r&&"bool"===r)throw new Error(`Unsupported data type ${r}`);const a=new va(t,n,r,!1,s),o=Jn(e,r);for(let e=0;e<o.values.length;e++)o.values[e]=a.nextValue();return o.toTensor()}});const $a=pn({randomStandardNormal_:function(e,t,n){if(null!=t&&"bool"===t)throw new Error(`Unsupported data type ${t}`);return Ia(e,0,1,t,n)}});const Aa=pn({randomUniform_:function(e,t=0,n=1,r="float32",s){De(e);const a=Jn(e,r),o=new Sa(t,n,null,s);for(let e=0;e<a.values.length;e++)a.values[e]=o.nextValue();return a.toTensor()}});const Da=pn({randomUniformInt_:function(e,t,n,r){return Aa(e,t,n,"int32",r)}});function Oa(e,t,n=1,r="float32"){if(0===n)throw new Error("Cannot have a step of zero");const s={start:e,stop:t,step:n,dtype:r};return tn.runKernel("Range",{},s)}const Ma=pn({real_:function(e){const t={input:on(e,"input","real")};return tn.runKernel("Real",t)}});const Ca=pn({reciprocal_:function(e){const t={x:on(e,"x","reciprocal")};return tn.runKernel("Reciprocal",t)}});const Fa=pn({relu_:function(e){const t={x:on(e,"x","relu")};return tn.runKernel("Relu",t)}});const Ra=pn({relu6_:function(e){const t={x:on(e,"x","relu6")};return tn.runKernel("Relu6",t)}});const za=pn({reverse_:function(e,t){const n={x:on(e,"x","reverse")},r={dims:t};return tn.runKernel("Reverse",n,r)}});const La=pn({reverse1d_:function(e){const t=on(e,"x","reverse");return de(1===t.rank,(()=>`Error in reverse1D: x must be rank 1 but got rank ${t.rank}.`)),za(t,0)}});const Va=pn({reverse2d_:function(e,t){const n=on(e,"x","reverse");return de(2===n.rank,(()=>`Error in reverse2D: x must be rank 2 but got rank ${n.rank}.`)),za(n,t)}});const Ba=pn({reverse3d_:function(e,t){const n=on(e,"x","reverse");return de(3===n.rank,(()=>`Error in reverse3D: x must be rank 3 but got rank ${n.rank}.`)),za(n,t)}});const Pa=pn({reverse4d_:function(e,t){const n=on(e,"x","reverse");return de(4===n.rank,(()=>`Error in reverse4D: x must be rank 4 but got rank ${n.rank}.`)),za(n,t)}});const Ka=pn({round_:function(e){const t={x:on(e,"x","round")};return tn.runKernel("Round",t)}});const qa=pn({rsqrt_:function(e){const t={x:on(e,"x","rsqrt","float32")};return tn.runKernel("Rsqrt",t)}});const Ua=pn({selu_:function(e){const t={x:on(e,"x","selu")};return tn.runKernel("Selu",t)}});const Wa=pn({separableConv2d_:function(e,t,n,r,s,a=[1,1],o="NHWC"){const i=on(e,"x","separableConv2d"),u=on(t,"depthwiseFilter","separableConv2d"),p=on(n,"pointwiseFilter","separableConv2d");let l=i,c=!1;if(3===i.rank&&(c=!0,l=Mn(i,[1,i.shape[0],i.shape[1],i.shape[2]])),"NCHW"===o)throw new Error("separableConv2d currently does not support dataFormat NCHW; only NHWC is supported");de(4===l.rank,(()=>`Error in separableConv2d: input must be rank 4, but got rank ${l.rank}.`)),de(4===u.rank,(()=>`Error in separableConv2d: depthwise filter must be rank 4, but got rank ${u.rank}.`)),de(4===p.rank,(()=>`Error in separableConv2d: pointwise filter must be rank 4, but got rank ${u.rank}.`)),de(1===p.shape[0],(()=>`Error in separableConv2d: the first dimension of pointwise filter  must be 1, but got ${p.shape[0]}.`)),de(1===p.shape[1],(()=>`Error in separableConv2d: the second dimension of pointwise filter must be 1, but got ${p.shape[1]}.`));const h=u.shape[2],d=u.shape[3];de(p.shape[2]===h*d,(()=>`Error in separableConv2d: the third dimension of pointwise filter must be ${h*d}, but got ${p.shape[2]}.`));const m=wr(l,u,r,s,o,a),f=ur(m,p,1,"valid",o);return c?Mn(f,[f.shape[1],f.shape[2],f.shape[3]]):f}});const ja=async function(e,t){const n=on(e,"x","setdiff1d"),r=on(t,"y","setdiff1d");de(n.dtype===r.dtype,(()=>`x and y should have the same dtype, but got x (${n.dtype}) and y (${r.dtype}).`)),de(1===n.rank,(()=>`x should be 1D tensor, but got x (${n.shape}).`)),de(1===r.rank,(()=>`y should be 1D tensor, but got y (${r.shape}).`));const s=await n.data(),a=await r.data(),o=new Set(a);let i=0;for(let e=0;e<s.length;e++)o.has(s[e])||i++;const u=new Rt([i],n.dtype),p=new Rt([i],"int32");for(let e=0,t=0;e<s.length;e++)o.has(s[e])||(u.values[t]=s[e],p.values[t]=e,t++);return[u.toTensor(),p.toTensor()]};const Ga=pn({sign_:function(e){const t={x:on(e,"x","sign")};return tn.runKernel("Sign",t)}});const Ha=pn({sin_:function(e){const t={x:on(e,"x","sin","float32")};return tn.runKernel("Sin",t)}});const Za=pn({sinh_:function(e){const t={x:on(e,"x","sinh")};return tn.runKernel("Sinh",t)}});const Qa=pn({slice1d_:function(e,t,n){const r=on(e,"x","slice1d");return de(1===r.rank,(()=>`slice1d expects a rank-1 tensor, but got a rank-${r.rank} tensor`)),Pn(r,[t],[n])}});const Xa=pn({slice2d_:function(e,t,n){const r=on(e,"x","slice2d");return de(2===r.rank,(()=>`slice2d expects a rank-2 tensor, but got a rank-${r.rank} tensor`)),Pn(r,t,n)}});const Ya=pn({slice3d_:function(e,t,n){const r=on(e,"x","slice3d");return de(3===r.rank,(()=>`slice3d expects a rank-3 tensor, but got a rank-${r.rank} tensor`)),Pn(r,t,n)}});const Ja=pn({slice4d_:function(e,t,n){const r=on(e,"x","slice4d");return de(4===r.rank,(()=>`slice4d expects a rank-4 tensor, but got a rank-${r.rank} tensor`)),Pn(r,t,n)}});const eo=pn({softmax_:function(e,t=-1){const n=on(e,"logits","softmax","float32");if(-1===t&&(t=n.rank-1),t!==n.rank-1)throw Error(`Softmax along a non-last dimension is not yet supported. Logits was rank ${n.rank} and dim was ${t}`);const r={logits:n},s={dim:t};return tn.runKernel("Softmax",r,s)}});const to=pn({fft_:function(e){de("complex64"===e.dtype,(()=>`The dtype for tf.spectral.fft() must be complex64 but got ${e.dtype}.`));const t={input:e};return tn.runKernel("FFT",t)}});const no=pn({ifft_:function(e){de("complex64"===e.dtype,(()=>`The dtype for tf.spectral.ifft() must be complex64 but got ${e.dtype}.`));const t={input:e};return tn.runKernel("IFFT",t)}});const ro=pn({irfft_:function(e){const t=e.shape[e.shape.length-1],n=e.size/t;let r;if(t<=2){const s=Mn(e,[n,t]);r=no(s)}else{const s=[n,2*(t-1)],a=Mn(Ma(e),[n,t]),o=Mn(rs(e),[n,t]),i=za(Pn(a,[0,1],[n,t-2]),1),u=Vn(za(Pn(o,[0,1],[n,t-2]),1),Pr(-1)),p=zn([a,i],1),l=zn([o,u],1),c=Mn(rr(p,l),[s[0],s[1]]);r=no(c)}if(r=Ma(r),3===e.rank&&0!==e.shape[0]){const t=r,n=e.shape[0];r=Mn(r,[n,r.shape[0]/n,r.shape[1]]),t.dispose()}return r}});const so=pn({split_:function(e,t,n=0){const r={x:on(e,"x","split")},s={numOrSizeSplits:t,axis:n};return tn.runKernel("SplitV",r,s)}});const ao=pn({rfft_:function(e,t){de("float32"===e.dtype,(()=>`The dtype for rfft() must be real value but got ${e.dtype}`));let n=e.shape[e.shape.length-1];const r=e.size/n;let s;if(null!=t&&t<n){const r=e.shape.map((e=>0)),a=e.shape.map((e=>e));a[e.shape.length-1]=t,s=Pn(e,r,a),n=t}else if(null!=t&&t>n){const r=e.shape.map((e=>e));r[e.shape.length-1]=t-n,s=zn([e,As(r)],e.shape.length-1),n=t}else s=e;const a=$r(s),o=Mn(rr(s,a),[r,n]),i=to(o),u=Math.floor(n/2)+1,p=Ma(i),l=rs(i),c=so(p,[u,n-u],p.shape.length-1),h=so(l,[u,n-u],l.shape.length-1),d=s.shape.slice();return d[s.shape.length-1]=u,Mn(rr(c[0],h[0]),d)}});const oo=pn({squaredDifference_:function(e,t){let n=on(e,"a","squaredDifference"),r=on(t,"b","squaredDifference");[n,r]=Zt(n,r),Sr(n.shape,r.shape);const s={a:n,b:r};return tn.runKernel("SquaredDifference",s,{})}});const io=pn({squeeze_:function(e,t){const n=on(e,"x","squeeze","string_or_numeric");return Mn(n,function(e,t){const n=[],r=[],s=null!=t&&Array.isArray(t)&&0===t.length,a=null==t||s?null:Ne(t,e).sort();let o=0;for(let t=0;t<e.length;++t){if(null!=a){if(a[o]===t&&1!==e[t])throw new Error(`Can't squeeze axis ${t} since its dim '${e[t]}' is not 1`);(null==a[o]||a[o]>t)&&1===e[t]&&(n.push(e[t]),r.push(t)),a[o]<=t&&o++}1!==e[t]&&(n.push(e[t]),r.push(t))}return{newShape:n,keptDims:r}}(n.shape,t).newShape)}});const uo=pn({stack_:function(e,t=0){const n=un(e,"tensors","stack","string_or_numeric");de(n.length>=1,(()=>"Pass at least one tensor to tf.stack")),n.length>0&&de(t<=n[0].rank,(()=>"Axis must be <= rank of the tensor"));const r=n,s={axis:t};return tn.runKernel("Pack",r,s)}});const po=pn({step_:function(e,t=0){const n={x:on(e,"x","step")},r={alpha:t};return tn.runKernel("Step",n,r)}});const lo=pn({stridedSlice_:function(e,t,n,r,s=0,a=0,o=0,i=0,u=0){const p={x:on(e,"x","stridedSlice","string_or_numeric")},l={begin:t,end:n,strides:r,beginMask:s,endMask:a,ellipsisMask:o,newAxisMask:i,shrinkAxisMask:u};return tn.runKernel("StridedSlice",p,l)}});const co=pn({tan_:function(e){const t={x:on(e,"x","tan","float32")};return tn.runKernel("Tan",t)}});function ho(e,t,n){return Br(e,t,rn(e,n),n)}function mo(e,t){fe(e);const n=rn(e,t);if(1!==n.length)throw new Error("tensor1d() requires values to be a flat/TypedArray");return Br(e,null,n,t)}function fo(e,t,n){if(fe(e),null!=t&&2!==t.length)throw new Error("tensor2d() requires shape to have two numbers");const r=rn(e,n);if(2!==r.length&&1!==r.length)throw new Error("tensor2d() requires values to be number[][] or flat/TypedArray");if(1===r.length&&null==t)throw new Error("tensor2d() requires shape to be provided when `values` are a flat/TypedArray");return Br(e,t,r,n)}function yo(e,t,n){if(t.rank<1)throw new Error(`tf.scatterND() expects the indices to be rank 1 or higher, but the rank was ${t.rank}.`);if(e.rank<1)throw new Error(`tf.scatterND() expects the updates to be rank 1 or higher, but the rank was ${e.rank}.`);if("int32"!==t.dtype)throw new Error(`The dtype of 'indices' should be int32, but got dtype: ${t.dtype}`);if(n.length<1)throw new Error(`Output rank must be greater or equal to 1, but got shape: ${n}`);if(0===n.length){if(0===t.size)throw new Error(`Indices specified for empty output. indices shape: ${t.shape}`);if(0===e.size)throw new Error(`Updates specified for empty output. updates shape: ${e.shape}`)}!function(e,t,n){const r=t.rank>1?t.shape[t.rank-1]:1,s=t.rank>1?t.rank-1:1,a=`Must have updates.shape = indices.shape[:batchDim] + shape[sliceDim:], got updates.shape: ${n.shape}, indices.shape: ${t.shape}, shape: ${e}, sliceDim: ${r}, and batchDim: ${s}.`;if(n.rank<s)throw new Error(a+` update.rank < ${s}. `);if(e.length<r+(n.rank-s))throw new Error(a+` Output shape length < ${r+(n.rank-s)}`);if(n.rank!==s+e.length-r)throw new Error(a+" update.rank != "+(s+e.length-r));for(let e=0;e<s;++e)if(n.shape[e]!==t.shape[e])throw new Error(a+` updates.shape[${e}] (${n.shape[e]}) != indices.shape[${e}] (${t.shape[e]}).`);for(let t=0;t<n.rank-s;++t)if(n.shape[t+s]!==e[t+r])throw new Error(a+` updates.shape[${t+s}] (${n.shape[t+s]}) != shape[${t+s}] (${e[t+s]})`)}(n,t,e)}const go=pn({tensorScatterUpdate_:function(e,t,n){const r=on(e,"tensor","tensorScatterupdate"),s=on(t,"indices","tensorScatterupdate","int32"),a=on(n,"updates","tensorScatterupdate");if(yo(a,s,r.shape),r.dtype!==a.dtype)throw new Error(`tensor and updates must have the same dtype, instead they are ${r.dtype} and ${a.dtype}.`);const o={tensor:r,indices:s,updates:a};return tn.runKernel("TensorScatterUpdate",o,{})}});const bo=pn({topk_:function(e,t=1,n=!0){const r=on(e,"x","topk");if(0===r.rank)throw new Error("topk() expects the input to be of rank 1 or higher");const s=r.shape[r.shape.length-1];if(t<0)throw new Error(`'k' passed to topk() must be >= 0 but got ${t}`);if(t>s)throw new Error(`'k' passed to topk() must be <= the last dimension (${s}) but got ${t}`);const a={x:r},o={k:t,sorted:n},[i,u]=tn.runKernel("TopK",a,o);return{values:i,indices:u}}});const xo=pn({truncatedNormal_:function(e,t=0,n=1,r,s){if(De(e),null!=r&&"bool"===r)throw new Error("Unsupported data type $ { dtype }");const a=new va(t,n,r,!0,s),o=Jn(e,r);for(let e=0;e<o.values.length;e++)o.values[e]=a.nextValue();return o.toTensor()}});const No=pn({unique_:function(e,t=0){const n=on(e,"x","unique","string_or_numeric");de(n.rank>0,(()=>"The input tensor must be at least 1D"));const r={x:n},s={axis:t},[a,o]=tn.runKernel("Unique",r,s);return{values:a,indices:o}}});const wo=pn({unsortedSegmentSum_:function(e,t,n){const r=on(e,"x","unsortedSegmentSum"),s=on(t,"segmentIds","unsortedSegmentSum","int32");de(be(n),(()=>"numSegments must be of dtype int"));const a={x:r,segmentIds:s},o={numSegments:n};return tn.runKernel("UnsortedSegmentSum",a,o)}});const ko=pn({unstack_:function(e,t=0){const n=on(e,"x","unstack","string_or_numeric");de(t>=-n.shape.length&&t<n.shape.length,(()=>`Axis = ${t} is not in [-${n.shape.length}, ${n.shape.length})`));const r={value:n},s={axis:t};return tn.runKernel("Unpack",r,s)}});const To=async function(e){const t=on(e,"condition","whereAsync","bool"),n=await t.data(),r=function(e,t){const n=[];for(let e=0;e<t.length;e++)t[e]&&n.push(e);const r=Jn(e,"int32"),s=Jn([n.length,e.length],"int32");for(let t=0;t<n.length;t++){const a=r.indexToLoc(n[t]),o=t*e.length;s.values.set(a,o)}return s.toTensor()}(t.shape,n);return e!==t&&t.dispose(),r};const vo=async function(e,t,n){const r=on(e,"tensor","boolMask"),s=on(t,"mask","boolMask","bool"),a=null==n?0:n,o=s.rank,i=r.shape;de(o>0,(()=>"mask cannot be scalar")),me(i.slice(a,a+o),s.shape,"mask's shape must match the first K dimensions of tensor's shape,");let u=1;for(let e=a;e<a+o;e++)u*=i[e];const p=i.slice(0,a).concat([u],i.slice(a+o)),l=Mn(r,p),c=Mn(s,[-1]),h=await To(c),d=io(h,[1]),m=es(l,d,a);return e!==r&&r.dispose(),t!==s&&s.dispose(),d.dispose(),l.dispose(),c.dispose(),h.dispose(),m};const _o=pn({transpose_:function(e,t,n){const r=on(e,"x","transpose");if(null==t&&(t=r.shape.map(((e,t)=>t)).reverse()),de(r.rank===t.length,(()=>`Error in transpose: rank of input ${r.rank} must match length of perm ${t}.`)),t.forEach((e=>{de(e>=0&&e<r.rank,(()=>"All entries in 'perm' must be between 0 and "+(r.rank-1)+` but got ${t}`))})),r.rank<=1)return r.clone();const s={x:r},a={perm:t};return"complex64"===r.dtype?(o=()=>{let e=Ma(r),t=rs(r);return e=tn.runKernel("Transpose",{x:e},a),t=tn.runKernel("Transpose",{x:t},a),n&&(t=ms(t)),rr(e,t)},tn.tidy(o,i)):tn.runKernel("Transpose",s,a);var o,i}});const So=pn({movingAverage_:function(e,t,n,r,s=!0){const a=on(e,"v","movingAverage"),o=on(t,"x","movingAverage"),i=on(n,"decay","movingAverage");var u,p;p=o,de((u=a).dtype===p.dtype,(()=>`The dtypes of the first(${u.dtype}) and second(${p.dtype}) input must match`)),de(ge(a.shape,o.shape),(()=>"Shape mismatch in v and x"));const l=Pr(1),c=gs(l,i);let h=Vn(gs(o,a),c);if(s){de(null!=r,(()=>"When using zeroDebias: true, step is required."));const e=on(r,"step","movingAverage");h=_r(h,gs(l,Vr(i,e)))}return dn(a,h)}});const Eo=pn({scatterND_:function(e,t,n){De(n);const r=on(e,"indices","scatterND","int32"),s=on(t,"updates","scatterND");yo(s,r,n);const a={indices:r,updates:s},o={shape:n};return tn.runKernel("ScatterNd",a,o)}});const Io=pn({sparseToDense_:function(e,t,n,r=0){De(n);const s=on(e,"sparseIndices","sparseToDense","int32"),a=on(t,"sparseValues","sparseToDense","string_or_numeric"),o=on(r,"defaultValue","sparseToDense",a.dtype);!function(e,t,n,r){if("int32"!==e.dtype)throw new Error(`tf.sparseToDense() expects the indices to be int32 type, but the dtype was ${e.dtype}.`);if(e.rank>2)throw new Error(`sparseIndices should be a scalar, vector, or matrix, but got shape ${e.shape}.`);const s=e.rank>0?e.shape[0]:1,a=e.rank>1?e.shape[1]:1;if(n.length!==a)throw new Error(`outputShape has incorrect number of elements:, ${n.length}, should be: ${a}.`);const o=t.size;if(0!==t.rank&&(1!==t.rank||o!==s))throw new Error(`sparseValues has incorrect shape ${t.shape}, should be [] or [${s}]`);if(t.dtype!==r.dtype)throw new Error("sparseValues.dtype must match defaultValues.dtype")}(s,a,n,o);const i={sparseIndices:s,sparseValues:a,defaultValue:o},u={outputShape:n};return tn.runKernel("SparseToDense",i,u)}});const $o=pn({gatherND_:function(e,t){const n=on(t,"indices","gatherND","int32"),r={params:on(e,"x","gatherND","string_or_numeric"),indices:n};return tn.runKernel("GatherNd",r)}});const Ao=pn({dropout_:function(e,t,n,r){const s=on(e,"x","dropout");if(de("float32"===s.dtype,(()=>`x has to be a floating point tensor since it's going to be scaled, but got a ${s.dtype} tensor instead.`)),de(t>=0&&t<1,(()=>`rate must be a float in the range [0, 1), but got ${t}.`)),0===t)return e instanceof Lt?s.clone():s;const a=function(e,t){if(null==t)return e.shape.slice();if(ge(e.shape,t))return t;if(e.shape.length===t.length){const n=[];for(let r=0;r<e.shape.length;r++)null==t[r]&&null!=e.shape[r]?n.push(e.shape[r]):n.push(t[r]);return n}return t}(s,n),o=1-t,i=_r(Jr(dn(Aa(a,0,1,"float32",r),o)),o);return Vn(s,i)}});function Do(e){return Math.floor(Math.pow(2,Math.ceil(Math.log(e)/Math.log(2))))}function Oo(e,t,n){const r=1-e%2,s=new Float32Array(e);for(let a=0;a<e;++a){const o=2*Math.PI*a/(e+r-1);s[a]=t-n*Math.cos(o)}return mo(s,"float32")}const Mo=async function(e,t,n=1){const r=on(e,"predictions","inTopK"),s=on(t,"targets","inTopK");de(r.rank>1,(()=>`inTopK() expects the predictions to be of rank 2 or higher, but got ${r.rank}`)),de(r.rank-1===s.rank,(()=>`predictions rank should be 1 larger than targets rank, but got predictions rank ${r.rank} and targets rank ${s.rank}`)),me(r.shape.slice(0,r.shape.length-1),s.shape,"predictions's shape should be align with the targets' shape, except the last dimension.");const a=r.shape[r.shape.length-1];de(n>0&&n<=a,(()=>`'k' passed to inTopK() must be > 0 && <= the predictions last dimension (${a}), but got ${n}`));const o=await r.data(),i=await s.data(),[u,p]=[o.length/a,a],l=function(e,t){return we(e,t)}("bool",u);for(let e=0;e<u;e++){const t=e*p,r=o.subarray(t,t+p),s=[];for(let e=0;e<r.length;e++)s.push({value:r[e],index:e});s.sort(((e,t)=>t.value-e.value)),l[e]=0;for(let t=0;t<n;t++)if(s[t].index===i[e]){l[e]=1;break}}return e!==r&&r.dispose(),t!==s&&s.dispose(),ho(l,s.shape,"bool")};const Co=pn({conv2DBackpropFilter_:function(e,t,n,r,s,a="NHWC",o){let i=e;3===e.rank&&(i=Mn(e,[1,e.shape[0],e.shape[1],e.shape[2]]));let u=t;3===u.rank&&(u=Mn(t,[1,t.shape[0],t.shape[1],t.shape[2]])),de(4===i.rank,(()=>`Error in conv2dDerFilter: input must be rank 4, but got shape ${i.shape}.`)),de(4===u.rank,(()=>`Error in conv2dDerFilter: dy must be rank 4, but got shape ${u.shape}.`)),de(4===n.length,(()=>`Error in conv2dDerFilter: filterShape must be length 4, but got ${n}.`));const p="NHWC"===a?i.shape[3]:i.shape[1],l="NHWC"===a?u.shape[3]:u.shape[1];de(p===n[2],(()=>`Error in conv2dDerFilter: depth of input ${p}) must match input depth in filter (${n[2]}.`)),de(l===n[3],(()=>`Error in conv2dDerFilter: depth of dy (${l}) must match output depth for filter (${n[3]}).`)),On("conv2dDerFilter",s,o);const c={x:i,dy:u},h={strides:r,pad:s,dataFormat:a,dimRoundingMode:o,filterShape:n};return tn.runKernel("Conv2DBackpropFilter",c,h)}});function Fo(e,t,n){if(null==n||"linear"===n)return e;if("relu"===n)return Vn(e,po(t));throw new Error(`Cannot compute gradient for fused activation ${n}.`)}function Ro(e,t){let n=t;const r=function(e,t){const n=[];for(let r=0;r<t.length;r++){const s=e[e.length-r-1],a=t.length-r-1,o=t[a];(null==s||1===s&&o>1)&&n.unshift(a)}return n}(e.shape,t.shape);return r.length>0&&(n=Ur(n,r)),Mn(n,e.shape)}function zo(e,t,n,r){if("linear"===t)return e;if("relu"===t)return Fa(e);if("elu"===t)return Mr(e);if("relu6"===t)return Ra(e);if("prelu"===t)return Zs(e,n);if("leakyrelu"===t)return is(e,r);if("sigmoid"===t)return Bn(e);throw new Error(`Unknown fused activation ${t}.`)}const Lo=(e,t)=>!(e>0)||"linear"===t;const Vo=pn({fusedConv2d_:function({x:e,filter:t,strides:n,pad:r,dataFormat:s="NHWC",dilations:a=[1,1],dimRoundingMode:o,bias:i,activation:u="linear",preluActivationWeights:p,leakyreluAlpha:l}){if(u=u||"linear",!1===Lo(tn.state.gradientDepth,u)){de("NHWC"===s,(()=>`Error in fused conv2d: got dataFormat of ${s} but only NHWC is currently supported for the case of gradient depth is 0 and the activation is not linear.`));let c=ur(e,t,n,r,s,a,o);return null!=i&&(c=dn(c,i)),zo(c,u,p,l)}const c=on(e,"x","conv2d","float32"),h=on(t,"filter","conv2d","float32");let d=c,m=!1;3===c.rank&&(m=!0,d=Mn(c,[1,c.shape[0],c.shape[1],c.shape[2]])),de(4===d.rank,(()=>`Error in fused conv2d: input must be rank 4, but got rank ${d.rank}.`)),de(4===h.rank,(()=>`Error in fused conv2d: filter must be rank 4, but got rank ${h.rank}.`)),On("fused conv2d",r,o);const f="NHWC"===s?d.shape[3]:d.shape[1];de(h.shape[2]===f,(()=>`Error in conv2d: depth of input (${f}) must match input depth for filter ${h.shape[2]}.`)),de(An(n,a),(()=>`Error in conv2D: Either strides or dilations must be 1. Got strides ${n} and dilations '${a}'`));const y=_n(d.shape,h.shape,n,a,r,o);let g,b;if(null!=i&&(g=on(i,"bias","fused conv2d"),[g]=Zt(g,c),"NHWC"===s?Sr(y.outShape,g.shape):(de(g.shape.length<=1,(()=>`Error in fused conv2d: only supports scalar or 1-D Tensor bias for NCHW format but got the bias of rank-${g.shape.length}.`)),de(0===g.shape.length||g.shape[0]===y.outChannels||1===g.shape[0],(()=>`Error in fused conv2d: bias shape (${g.shape}) is not compatible with the number of output channels (${y.outChannels})`)))),null!=p){const e=p.shape;if(de(e.length<=1||3===e.length,(()=>`Error in fused conv2d: only supports scalar, 1-D Tensor or 3-D Tensor PReLU activation weights but got a tensor of rank-${e.length}.`)),1===e.length)de(1===e[0]||e[0]===y.outChannels,(()=>`Error in fused conv2d: PReLU activation weights (${e}) is not compatible with the number of output channels (${y.outChannels}).`));else if(3===e.length)try{Sr(e,y.outShape)}catch(t){const n=`Error in fused conv2d: PReLU activation weights (${e}) is not compatible with the output shape of the conv2d (${y.outShape}).`;throw Error(n)}b=on(p,"prelu weights","fused conv2d")}const x=(e,t)=>{de("NHWC"===s,(()=>`Error in gradient of fused conv2D: got dataFormat of ${s} but only NHWC is currently supported.`));const[o,i,p,l]=t,c=Fo(e,p,u);de($n(a),(()=>`Error in gradient of fused conv2D: dilation rates greater than 1 are not yet supported in gradients. Got dilations '${a}'`));const h=[lr(i.shape,c,o,n,r),Co(i,c,o.shape,n,r)];if(null!=l){const e=Ro(l,c);h.push(e)}return h},N={x:d,filter:h,bias:g,preluActivationWeights:b},w={strides:n,pad:r,dataFormat:s,dilations:a,dimRoundingMode:o,activation:u,leakyreluAlpha:l};if(null==i){const e=ds(((e,t,n)=>{let r=tn.runKernel("FusedConv2D",N,w);return n([t,e,r]),m&&(r=Mn(r,[r.shape[1],r.shape[2],r.shape[3]])),{value:r,gradFunc:x}}));return e(d,h)}{const e=ds(((e,t,n,r)=>{let s=tn.runKernel("FusedConv2D",N,w);return r([t,e,s,n]),m&&(s=Mn(s,[s.shape[1],s.shape[2],s.shape[3]])),{value:s,gradFunc:x}}));return e(d,h,g)}}});const Bo=pn({depthwiseConv2dNativeBackpropFilter_:function(e,t,n,r,s,a=[1,1],o){let i=e;3===e.rank&&(i=Mn(e,[1,e.shape[0],e.shape[1],e.shape[2]]));let u=t;3===u.rank&&(u=Mn(t,[1,t.shape[0],t.shape[1],t.shape[2]]));const p={x:i,dy:u},l={strides:r,pad:s,dimRoundingMode:o,dilations:a,filterShape:n};return tn.runKernel("DepthwiseConv2dNativeBackpropFilter",p,l)}});const Po=pn({depthwiseConv2dNativeBackpropInput_:function(e,t,n,r,s,a=[1,1],o){let i=t,u=!1;3===t.rank&&(u=!0,i=Mn(t,[1,t.shape[0],t.shape[1],t.shape[2]]));const p={dy:i,filter:n},l={strides:r,pad:s,dimRoundingMode:o,dilations:a,inputShape:e},c=tn.runKernel("DepthwiseConv2dNativeBackpropInput",p,l);return u?Mn(c,[c.shape[1],c.shape[2],c.shape[3]]):c}});const Ko=pn({fusedDepthwiseConv2d_:function({x:e,filter:t,strides:n,pad:r,dataFormat:s="NHWC",dilations:a=[1,1],dimRoundingMode:o,bias:i,activation:u="linear",preluActivationWeights:p,leakyreluAlpha:l}){if(!1===Lo(tn.state.gradientDepth,u)){let c=wr(e,t,n,r,s,a,o);return null!=i&&(c=dn(c,i)),zo(c,u,p,l)}const c=on(e,"x","depthwiseConv2d","float32"),h=on(t,"filter","depthwiseConv2d","float32");let d=c,m=!1;3===c.rank&&(m=!0,d=Mn(c,[1,c.shape[0],c.shape[1],c.shape[2]])),de(4===d.rank,(()=>`Error in fused depthwiseConv2d: input must be rank 4, but got rank ${d.rank}.`)),de(4===h.rank,(()=>`Error in fused depthwiseConv2d: filter must be rank 4, but got rank ${h.rank}.`)),de(d.shape[3]===h.shape[2],(()=>`Error in fused depthwiseConv2d: number of input channels (${d.shape[3]}) must match the inChannels dimension in filter ${h.shape[2]}.`)),null==a&&(a=[1,1]),de(An(n,a),(()=>`Error in fused depthwiseConv2d: Either strides or dilations must be 1. Got strides ${n} and dilations '${a}'`)),On("fused depthwiseConv2d",r,o);const f=_n(d.shape,h.shape,n,a,r,o,!0);let y,g;null!=i&&(y=on(i,"bias","fused conv2d"),[y]=Zt(y,c),Sr(f.outShape,y.shape)),null!=p&&(g=on(p,"prelu weights","fused depthwiseConv2d"));const b=(e,t)=>{de($n(a),(()=>`Error in gradient of fused depthwiseConv2d: dilation rates greater than 1 are not yet supported. Got dilations '${a}'`));const[s,i,p,l]=t,c=Fo(e,p,u),h=Po(i.shape,c,s,n,r,a,o),d=Bo(i,c,s.shape,n,r,a,o);if(null!=l){return[h,d,Ro(y,c)]}return[h,d]},x={x:d,filter:h,bias:y,preluActivationWeights:g},N={strides:n,pad:r,dataFormat:s,dilations:a,dimRoundingMode:o,activation:u,leakyreluAlpha:l};if(null==i){const e=ds(((e,t,n)=>{let r=tn.runKernel("FusedDepthwiseConv2D",x,N);return n([t,e,r]),m&&(r=Mn(r,[r.shape[1],r.shape[2],r.shape[3]])),{value:r,gradFunc:b}}));return e(d,h)}{const e=ds(((e,t,n,r)=>{let s=tn.runKernel("FusedDepthwiseConv2D",x,N);return r([t,e,s,n]),m&&(s=Mn(s,[s.shape[1],s.shape[2],s.shape[3]])),{value:s,gradFunc:b}}));return e(d,h,y)}}});var qo={__proto__:null,conv2d:Vo,depthwiseConv2d:Ko,matMul:pn({fusedMatMul_:function({a:e,b:t,transposeA:n=!1,transposeB:r=!1,bias:s,activation:a="linear",preluActivationWeights:o,leakyreluAlpha:i=.2}){if(!1===Lo(tn.state.gradientDepth,a)){let u=Ln(e,t,n,r);return null!=s&&(u=dn(u,s)),zo(u,a,o,i)}let u=on(e,"a","fused matMul"),p=on(t,"b","fused matMul");[u,p]=Zt(u,p);const l=n?u.shape[u.rank-2]:u.shape[u.rank-1],c=r?p.shape[p.rank-1]:p.shape[p.rank-2],h=n?u.shape[u.rank-1]:u.shape[u.rank-2],d=r?p.shape[p.rank-2]:p.shape[p.rank-1],m=u.shape.slice(0,-2),f=p.shape.slice(0,-2),y=ye(m),g=ye(f);de(l===c,(()=>`Error in fused matMul: inner shapes (${l}) and (${c}) of Tensors with shapes ${u.shape} and ${p.shape} and transposeA=${n} and transposeB=${r} must match.`));const b=Sr(u.shape.slice(0,-2),p.shape.slice(0,-2)).concat([h,d]),x=Mn(u,n?[y,l,h]:[y,h,l]),N=Mn(p,r?[g,d,c]:[g,c,d]);let w,k;null!=s&&(w=on(s,"bias","fused matMul"),[w]=Zt(w,u),Sr(b,w.shape)),null!=o&&(k=on(o,"prelu weights","fused matMul"));const T=(e,t)=>{const[o,i,u,p]=t,l=Fo(Mn(e,u.shape),u,a);let c,h;if(n||r?!n&&r?(c=Ln(l,i,!1,!1),h=Ln(l,o,!0,!1)):n&&!r?(c=Ln(i,l,!1,!0),h=Ln(o,l,!1,!1)):(c=Ln(i,l,!0,!0),h=Ln(l,o,!0,!0)):(c=Ln(l,i,!1,!0),h=Ln(o,l,!0,!1)),null!=s){return[c,h,Ro(p,l)]}return[c,h]},v={a:x,b:N,bias:w,preluActivationWeights:k},_={transposeA:n,transposeB:r,activation:a,leakyreluAlpha:i};if(null==s){const e=ds(((e,t,n)=>{const r=tn.runKernel("_FusedMatMul",v,_);return n([e,t,r]),{value:Mn(r,b),gradFunc:T}}));return e(x,N)}{const e=ds(((e,t,n,r)=>{const s=tn.runKernel("_FusedMatMul",v,_);return r([e,t,s,n]),{value:Mn(s,b),gradFunc:T}}));return e(x,N,w)}}})};const Uo=pn({hammingWindow_:function(e){return Oo(e,.54,.46)}});const Wo=pn({hannWindow_:function(e){return Oo(e,.5,.5)}});const jo=pn({frame_:function(e,t,n,r=!1,s=0){let a=0;const o=[];for(;a+t<=e.size;)o.push(Pn(e,a,t)),a+=n;if(r)for(;a<e.size;){const r=a+t-e.size,i=zn([Pn(e,a,t-r),tr([r],s)]);o.push(i),a+=n}return 0===o.length?fo([],[0,t]):Mn(zn(o),[o.length,t])}});const Go=pn({stft_:function(e,t,n,r,s=Wo){null==r&&(r=Do(t));const a=jo(e,t,n),o=Vn(a,s(t));return ao(o,r)}});const Ho=pn({cropAndResize_:function(e,t,n,r,s="bilinear",a=0){const o=on(e,"image","cropAndResize"),i=on(t,"boxes","cropAndResize","float32"),u=on(n,"boxInd","cropAndResize","int32"),p=i.shape[0];de(4===o.rank,(()=>`Error in cropAndResize: image must be rank 4,but got rank ${o.rank}.`)),de(2===i.rank&&4===i.shape[1],(()=>`Error in cropAndResize: boxes must be have size [${p},4] but had shape ${i.shape}.`)),de(1===u.rank&&u.shape[0]===p,(()=>`Error in cropAndResize: boxInd must be have size [${p}] but had shape ${i.shape}.`)),de(2===r.length,(()=>`Error in cropAndResize: cropSize must be of length 2, but got length ${r.length}.`)),de(r[0]>=1&&r[1]>=1,(()=>`cropSize must be atleast [1,1], but was ${r}`)),de("bilinear"===s||"nearest"===s,(()=>`method must be bilinear or nearest, but was ${s}`));const l={image:o,boxes:i,boxInd:u},c={method:s,extrapolationValue:a,cropSize:r};return tn.runKernel("CropAndResize",l,c)}});const Zo=pn({flipLeftRight_:function(e){const t=on(e,"image","flipLeftRight","float32");de(4===t.rank,(()=>`Error in flipLeftRight: image must be rank 4,but got rank ${t.rank}.`));const n={image:t};return tn.runKernel("FlipLeftRight",n,{})}});const Qo=pn({grayscaleToRGB_:function(e){const t=on(e,"image","grayscaleToRGB"),n=t.rank-1,r=t.shape[n];de(t.rank>=2,(()=>`Error in grayscaleToRGB: images must be at least rank 2, but got rank ${t.rank}.`)),de(1===r,(()=>`Error in grayscaleToRGB: last dimension of a grayscale image should be size 1, but got size ${r}.`));const s=new Array(t.rank);return s.fill(1,0,n),s[n]=3,Xr(t,s)}});const Xo=pn({rgbToGrayscale_:function(e){const t=on(e,"image","RGBToGrayscale"),n=t.rank-1,r=t.shape[n];de(t.rank>=2,(()=>`Error in RGBToGrayscale: images must be at least rank 2, but got rank ${t.rank}.`)),de(3===r,(()=>`Error in RGBToGrayscale: last dimension of an RGB image should be size 3, but got size ${r}.`));const s=t.dtype,a=vn(t,"float32"),o=mo([.2989,.587,.114]);let i;switch(t.rank){case 2:i=Or("ij,j->i",a,o);break;case 3:i=Or("ijk,k->ij",a,o);break;case 4:i=Or("ijkl,l->ijk",a,o);break;case 5:i=Or("ijklm,m->ijkl",a,o);break;case 6:i=Or("ijklmn,n->ijklm",a,o);break;default:throw new Error("Not a valid tensor rank.")}return i=Zr(i,-1),vn(i,s)}});const Yo=pn({rotateWithOffset_:function(e,t,n=0,r=.5){const s=on(e,"image","rotateWithOffset","float32");de(4===s.rank,(()=>`Error in rotateWithOffset: image must be rank 4,but got rank ${s.rank}.`));const a={image:s},o={radians:t,fillValue:n,center:r};return tn.runKernel("RotateWithOffset",a,o)}});function Jo(e,t,n,r,s,a){null==r&&(r=.5),null==s&&(s=Number.NEGATIVE_INFINITY),null==a&&(a=0);const o=e.shape[0];return n=Math.min(n,o),de(0<=r&&r<=1,(()=>`iouThreshold must be in [0, 1], but was '${r}'`)),de(2===e.rank,(()=>`boxes must be a 2D tensor, but was of rank '${e.rank}'`)),de(4===e.shape[1],(()=>`boxes must have 4 columns, but 2nd dimension was ${e.shape[1]}`)),de(1===t.rank,(()=>"scores must be a 1D tensor")),de(t.shape[0]===o,(()=>`scores has incompatible shape with boxes. Expected ${o}, but was ${t.shape[0]}`)),de(0<=a&&a<=1,(()=>`softNmsSigma must be in [0, 1], but was '${a}'`)),{maxOutputSize:n,iouThreshold:r,scoreThreshold:s,softNmsSigma:a}}const ei=pn({nonMaxSuppression_:function(e,t,n,r=.5,s=Number.NEGATIVE_INFINITY){const a=on(e,"boxes","nonMaxSuppression","float32"),o=on(t,"scores","nonMaxSuppression","float32"),i=Jo(a,o,n,r,s),u={maxOutputSize:n=i.maxOutputSize,iouThreshold:r=i.iouThreshold,scoreThreshold:s=i.scoreThreshold};return tn.runKernel("NonMaxSuppressionV3",{boxes:a,scores:o},u)}});function ti(e,t,n){const r=function(e,t,n){return function(e,t,n){let r=0,s=e.length,a=0,o=!1;for(;r<s;){a=r+(s-r>>>1);const i=n(t,e[a]);i>0?r=a+1:(s=a,o=!i)}return o?r:-r-1}(e,t,n||ni)}(e,t,n),s=r<0?-(r+1):r;e.splice(s,0,t)}function ni(e,t){return e>t?1:e<t?-1:0}function ri(e,t,n,r,s,a,o=!1,i=!1,u=!1){const p=[];for(let e=0;e<t.length;e++)t[e]>s&&p.push({score:t[e],boxIndex:e,suppressBeginIndex:0});p.sort(oi);const l=a>0?-.5/a:0,c=[],h=[];for(;c.length<n&&p.length>0;){const t=p.pop(),{score:n,boxIndex:a,suppressBeginIndex:o}=t;if(n<s)break;let i=!1;for(let n=c.length-1;n>=o;--n){const o=si(e,a,c[n]);if(o>=r){i=!0;break}if(t.score=t.score*ai(r,l,o),t.score<=s)break}t.suppressBeginIndex=c.length,i||(t.score===n?(c.push(a),h.push(t.score)):t.score>s&&ti(p,t,oi))}const d=c.length,m=n-d;i&&m>0&&(c.push(...new Array(m).fill(0)),h.push(...new Array(m).fill(0)));const f={selectedIndices:c};return o&&(f.selectedScores=h),u&&(f.validOutputs=d),f}function si(e,t,n){const r=e.subarray(4*t,4*t+4),s=e.subarray(4*n,4*n+4),a=Math.min(r[0],r[2]),o=Math.min(r[1],r[3]),i=Math.max(r[0],r[2]),u=Math.max(r[1],r[3]),p=Math.min(s[0],s[2]),l=Math.min(s[1],s[3]),c=Math.max(s[0],s[2]),h=Math.max(s[1],s[3]),d=(i-a)*(u-o),m=(c-p)*(h-l);if(d<=0||m<=0)return 0;const f=Math.max(a,p),y=Math.max(o,l),g=Math.min(i,c),b=Math.min(u,h),x=Math.max(g-f,0)*Math.max(b-y,0);return x/(d+m-x)}function ai(e,t,n){const r=Math.exp(t*n*n);return n<=e?r:0}function oi(e,t){return e.score-t.score||e.score===t.score&&t.boxIndex-e.boxIndex}const ii=async function(e,t,n,r=.5,s=Number.NEGATIVE_INFINITY){const a=on(e,"boxes","nonMaxSuppressionAsync"),o=on(t,"scores","nonMaxSuppressionAsync"),i=Jo(a,o,n,r,s);n=i.maxOutputSize,r=i.iouThreshold,s=i.scoreThreshold;const u=await Promise.all([a.data(),o.data()]),p=u[0],l=u[1],{selectedIndices:c}=function(e,t,n,r,s){return ri(e,t,n,r,s,0)}(p,l,n,r,s);return a!==e&&a.dispose(),o!==t&&o.dispose(),mo(c,"int32")};const ui=pn({nonMaxSuppressionWithScore_:function(e,t,n,r=.5,s=Number.NEGATIVE_INFINITY,a=0){const o=on(e,"boxes","nonMaxSuppression"),i=on(t,"scores","nonMaxSuppression"),u=Jo(o,i,n,r,s,a),p={boxes:o,scores:i},l={maxOutputSize:n=u.maxOutputSize,iouThreshold:r=u.iouThreshold,scoreThreshold:s=u.scoreThreshold,softNmsSigma:a=u.softNmsSigma},c=tn.runKernel("NonMaxSuppressionV5",p,l);return{selectedIndices:c[0],selectedScores:c[1]}}});const pi=async function(e,t,n,r=.5,s=Number.NEGATIVE_INFINITY,a=0){const o=on(e,"boxes","nonMaxSuppressionAsync"),i=on(t,"scores","nonMaxSuppressionAsync"),u=Jo(o,i,n,r,s,a);n=u.maxOutputSize,r=u.iouThreshold,s=u.scoreThreshold,a=u.softNmsSigma;const p=await Promise.all([o.data(),i.data()]),l=p[0],c=p[1],{selectedIndices:h,selectedScores:d}=function(e,t,n,r,s,a){return ri(e,t,n,r,s,a,!0)}(l,c,n,r,s,a);return o!==e&&o.dispose(),i!==t&&i.dispose(),{selectedIndices:mo(h,"int32"),selectedScores:mo(d)}};const li=pn({nonMaxSuppressionPadded_:function(e,t,n,r=.5,s=Number.NEGATIVE_INFINITY,a=!1){const o=on(e,"boxes","nonMaxSuppression"),i=on(t,"scores","nonMaxSuppression"),u=Jo(o,i,n,r,s,null),p={boxes:o,scores:i},l={maxOutputSize:u.maxOutputSize,iouThreshold:u.iouThreshold,scoreThreshold:u.scoreThreshold,padToMaxOutputSize:a},c=tn.runKernel("NonMaxSuppressionV4",p,l);return{selectedIndices:c[0],validOutputs:c[1]}}});const ci=async function(e,t,n,r=.5,s=Number.NEGATIVE_INFINITY,a=!1){const o=on(e,"boxes","nonMaxSuppressionAsync"),i=on(t,"scores","nonMaxSuppressionAsync"),u=Jo(o,i,n,r,s,null),p=u.maxOutputSize,l=u.iouThreshold,c=u.scoreThreshold,[h,d]=await Promise.all([o.data(),i.data()]),{selectedIndices:m,validOutputs:f}=function(e,t,n,r,s,a){return ri(e,t,n,r,s,0,!1,a,!0)}(h,d,p,l,c,a);return o!==e&&o.dispose(),i!==t&&i.dispose(),{selectedIndices:mo(m,"int32"),validOutputs:Pr(f,"int32")}};const hi=pn({resizeBilinear_:function(e,t,n=!1,r=!1){const s=on(e,"images","resizeBilinear");de(3===s.rank||4===s.rank,(()=>`Error in resizeBilinear: x must be rank 3 or 4, but got rank ${s.rank}.`)),de(2===t.length,(()=>`Error in resizeBilinear: new shape must 2D, but got shape ${t}.`)),de(!1===r||!1===n,(()=>"Error in resizeBilinear: If halfPixelCenters is true, alignCorners must be false."));let a=s,o=!1;3===s.rank&&(o=!0,a=Mn(s,[1,s.shape[0],s.shape[1],s.shape[2]]));const i={images:a},u={alignCorners:n,halfPixelCenters:r,size:t},p=tn.runKernel("ResizeBilinear",i,u);return o?Mn(p,[p.shape[1],p.shape[2],p.shape[3]]):p}});const di=pn({resizeNearestNeighbor_:function(e,t,n=!1,r=!1){const s=on(e,"images","resizeNearestNeighbor");de(3===s.rank||4===s.rank,(()=>`Error in resizeNearestNeighbor: x must be rank 3 or 4, but got rank ${s.rank}.`)),de(2===t.length,(()=>`Error in resizeNearestNeighbor: new shape must 2D, but got shape ${t}.`)),de("float32"===s.dtype||"int32"===s.dtype,(()=>"`images` must have `int32` or `float32` as dtype")),de(!1===r||!1===n,(()=>"Error in resizeNearestNeighbor: If halfPixelCenters is true, alignCorners must be false."));let a=s,o=!1;3===s.rank&&(o=!0,a=Mn(s,[1,s.shape[0],s.shape[1],s.shape[2]]));const i={images:a},u={alignCorners:n,halfPixelCenters:r,size:t},p=tn.runKernel("ResizeNearestNeighbor",i,u);return o?Mn(p,[p.shape[1],p.shape[2],p.shape[3]]):p}});const mi=pn({threshold_:function(e,t="binary",n=!1,r=.5){const s=on(e,"image","threshold"),a=s.shape[0]*s.shape[1];let o,i,u,p,l=Vn(mo([r]),255);if(de(3===s.rank,(()=>`Error in threshold: image must be rank 3,but got rank ${s.rank}.`)),de(3===s.shape[2]||1===s.shape[2],(()=>`Error in threshold: image color channel must be equal to 3 or 1but got ${s.shape[2]}.`)),de("int32"===s.dtype||"float32"===s.dtype,(()=>`Error in dtype: image dtype must be int32 or float32,but got dtype ${s.dtype}.`)),de("otsu"===t||"binary"===t,(()=>`Method must be binary or otsu, but was ${t}`)),3===s.shape[2]){[o,i,u]=so(s,[1,1,1],-1);const e=Vn(o,.2989),t=Vn(i,.587),n=Vn(u,.114);p=dn(dn(e,t),n)}else p=e;if("otsu"===t){l=function(e,t){let n,r,s,a,o,i,u=mo([-1]),p=mo([0]),l=mo([0]);for(let c=0;c<e.size-1;c++){n=Pn(e,0,c+1),r=Pn(e,c+1),o=_r(Ur(n),t),i=_r(Ur(r),t);const h=Ur(Vn(n,Oa(0,n.size)));s=_r(h,Ur(n));const d=tr(r.shape,n.size),m=dn(Oa(0,r.size),d),f=Vn(r,m);a=_r(Ur(f),Ur(r));const y=gs(s,a),g=gs(s,a),b=Vn(o,i);l=Vn(Vn(b,y),g);const x=ts(l,p);p=Ir(x,l,p),u=Ir(x,mo([c]),u)}return u}(Zn(vn(Ka(p),"int32"),ho([]),256),a)}const c=n?ps(p,l):ts(p,l);return vn(Vn(c,255),"int32")}});const fi=pn({transform_:function(e,t,n="nearest",r="constant",s=0,a){const o=on(e,"image","transform","float32"),i=on(t,"transforms","transform","float32");de(4===o.rank,(()=>`Error in transform: image must be rank 4,but got rank ${o.rank}.`)),de(2===i.rank&&(i.shape[0]===o.shape[0]||1===i.shape[0])&&8===i.shape[1],(()=>"Error in transform: Input transform should be batch x 8 or 1 x 8")),de(null==a||2===a.length,(()=>`Error in transform: outputShape must be [height, width] or null, but got ${a}.`));const u={image:o,transforms:i},p={interpolation:n,fillMode:r,fillValue:s,outputShape:a};return tn.runKernel("Transform",u,p)}});const yi=pn({bandPart_:function(e,t,n){const r=on(e,"a","bandPart");de(r.rank>=2,(()=>`bandPart(): Rank must be at least 2, got ${r.rank}.`));const s=r.shape,[a,o]=r.shape.slice(-2);let i,u;"number"==typeof t?(de(t%1==0,(()=>`bandPart(): numLower must be an integer, got ${t}.`)),de(t<=a,(()=>`bandPart(): numLower (${t}) must not be greater than the number of rows (${a}).`)),i=on(t<0?a:t,"numLower","bandPart")):(de("int32"===t.dtype,(()=>"bandPart(): numLower's dtype must be an int32.")),i=Ir(us(t,0),a,Os(t,a))),"number"==typeof n?(de(n%1==0,(()=>`bandPart(): numUpper must be an integer, got ${n}.`)),de(n<=o,(()=>`bandPart(): numUpper (${n}) must not be greater than the number of columns (${o}).`)),u=on(n<0?o:n,"numUpper","bandPart")):(de("int32"===n.dtype,(()=>"bandPart(): numUpper's dtype must be an int32.")),u=Ir(us(n,0),o,Os(n,o)));const p=Mn(Oa(0,a,1,"int32"),[-1,1]),l=Oa(0,o,1,"int32"),c=gs(p,l),h=Ns(ps(c,i),ns(c,ms(u))),d=As([a,o],r.dtype);return Mn(uo(ko(Mn(r,[-1,a,o])).map((e=>Ir(h,e,d)))),s)}});const gi=pn({gramSchmidt_:function(e){let t;if(Array.isArray(e)){t=!1,de(null!=e&&e.length>0,(()=>"Gram-Schmidt process: input must not be null, undefined, or empty"));const n=e[0].shape[0];for(let t=1;t<e.length;++t)de(e[t].shape[0]===n,(()=>`Gram-Schmidt: Non-unique lengths found in the input vectors: (${e[t].shape[0]} vs. ${n})`))}else t=!0,e=so(e,e.shape[0],0).map((e=>io(e,[0])));de(e.length<=e[0].shape[0],(()=>`Gram-Schmidt: Number of vectors (${e.length}) exceeds number of dimensions (${e[0].shape[0]}).`));const n=[],r=e;for(let t=0;t<e.length;++t)n.push(tn.tidy((()=>{let e=r[t];if(t>0)for(let r=0;r<t;++r){const t=Vn(Ur(Vn(n[r],e)),n[r]);e=gs(e,t)}return _r(e,jr(e,"euclidean"))})));return t?uo(n,0):n}});function bi(e,t=!1){return tn.tidy((()=>{de(2===e.shape.length,(()=>`qr2d() requires a 2D Tensor, but got a ${e.shape.length}D Tensor.`));const n=e.shape[0],r=e.shape[1];let s=Yr(n),a=Rn(e);const o=fo([[1]],[1,1]);let i=Rn(o);const u=n>=r?r:n;for(let e=0;e<u;++e){const t=a,u=i,p=s;[i,a,s]=tn.tidy((()=>{const t=Pn(a,[e,e],[n-e,1]),u=jr(t),p=Pn(a,[e,e],[1,1]),l=Ir(ts(p,0),fo([[-1]]),fo([[1]])),c=gs(p,Vn(l,u)),h=_r(t,c);i=1===h.shape[0]?Rn(o):zn([o,Pn(h,[1,0],[h.shape[0]-1,h.shape[1]])],0);const d=ms(_r(Ln(l,c),u)),m=Pn(a,[e,0],[n-e,r]),f=Vn(d,i),y=_o(i);if(0===e)a=gs(m,Ln(f,Ln(y,m)));else{const t=gs(m,Ln(f,Ln(y,m)));a=zn([Pn(a,[0,0],[e,r]),t],0)}const g=_o(f),b=Pn(s,[0,e],[n,s.shape[1]-e]);if(0===e)s=gs(b,Ln(Ln(b,i),g));else{const t=gs(b,Ln(Ln(b,i),g));s=zn([Pn(s,[0,0],[n,e]),t],1)}return[i,a,s]})),Qt([t,u,p]).forEach((e=>e.dispose()))}return!t&&n>r&&(s=Pn(s,[0,0],[n,r]),a=Pn(a,[0,0],[r,r])),[s,a]}))}const xi=pn({qr_:function(e,t=!1){if(de(e.rank>=2,(()=>`qr() requires input tensor to have a rank >= 2, but got rank ${e.rank}`)),2===e.rank)return bi(e,t);{const n=e.shape.slice(0,e.shape.length-2).reduce(((e,t)=>e*t)),r=ko(Mn(e,[n,e.shape[e.shape.length-2],e.shape[e.shape.length-1]]),0),s=[],a=[];r.forEach((e=>{const[n,r]=bi(e,t);s.push(n),a.push(r)}));return[Mn(uo(s,0),e.shape),Mn(uo(a,0),e.shape)]}}});var Ni;!function(e){e[e.NONE=0]="NONE",e[e.MEAN=1]="MEAN",e[e.SUM=2]="SUM",e[e.SUM_BY_NONZERO_WEIGHTS=3]="SUM_BY_NONZERO_WEIGHTS"}(Ni||(Ni={}));const wi=pn({computeWeightedLoss_:function(e,t,n=Ni.SUM_BY_NONZERO_WEIGHTS){const r=on(e,"losses","computeWeightedLoss");let s=null;null!=t&&(s=on(t,"weights","computeWeightedLoss"));const a=null==s?r:Vn(r,s);if(n===Ni.NONE)return a;if(n===Ni.SUM)return Ur(a);if(n===Ni.MEAN){if(null==s)return $s(a);{const e=r.size/s.size,t=_r(Ur(a),Ur(s));return e>1?_r(t,Pr(e)):t}}if(n===Ni.SUM_BY_NONZERO_WEIGHTS){if(null==s)return _r(Ur(a),Pr(r.size));{const e=Vn(s,Ds(r.shape)),t=vn(Ur(Ls(e,Pr(0))),"float32");return _r(Ur(a),t)}}throw Error(`Unknown reduction: ${n}`)}});const ki=pn({absoluteDifference_:function(e,t,n,r=Ni.SUM_BY_NONZERO_WEIGHTS){const s=on(e,"labels","absoluteDifference"),a=on(t,"predictions","absoluteDifference");let o=null;null!=n&&(o=on(n,"weights","absoluteDifference")),me(s.shape,a.shape,"Error in absoluteDifference: ");const i=ln(gs(s,a));return wi(i,o,r)}});const Ti=pn({cosineDistance_:function(e,t,n,r,s=Ni.SUM_BY_NONZERO_WEIGHTS){const a=on(e,"labels","cosineDistance"),o=on(t,"predictions","cosineDistance");let i=null;null!=r&&(i=on(r,"weights","cosineDistance")),me(a.shape,o.shape,"Error in cosineDistance: ");const u=Pr(1),p=gs(u,Ur(Vn(a,o),n,!0));return wi(p,i,s)}});const vi=pn({hingeLoss_:function(e,t,n,r=Ni.SUM_BY_NONZERO_WEIGHTS){let s=on(e,"labels","hingeLoss");const a=on(t,"predictions","hingeLoss");let o=null;null!=n&&(o=on(n,"weights","hingeLoss")),me(s.shape,a.shape,"Error in hingeLoss: ");const i=Pr(1);s=gs(Vn(Pr(2),s),i);const u=Fa(gs(i,Vn(s,a)));return wi(u,o,r)}});const _i=pn({huberLoss_:function(e,t,n,r=1,s=Ni.SUM_BY_NONZERO_WEIGHTS){const a=on(e,"labels","huberLoss"),o=on(t,"predictions","huberLoss");let i=null;null!=n&&(i=on(n,"weights","huberLoss")),me(a.shape,o.shape,"Error in huberLoss: ");const u=Pr(r),p=ln(gs(o,a)),l=Os(p,u),c=gs(p,l),h=dn(Vn(Pr(.5),qr(l)),Vn(u,c));return wi(h,i,s)}});const Si=pn({logLoss_:function(e,t,n,r=1e-7,s=Ni.SUM_BY_NONZERO_WEIGHTS){const a=on(e,"labels","logLoss"),o=on(t,"predictions","logLoss");let i=null;null!=n&&(i=on(n,"weights","logLoss")),me(a.shape,o.shape,"Error in logLoss: ");const u=Pr(1),p=Pr(r),l=ms(Vn(a,cs(dn(o,p)))),c=Vn(gs(u,a),cs(dn(gs(u,o),p))),h=gs(l,c);return wi(h,i,s)}});const Ei=pn({meanSquaredError_:function(e,t,n,r=Ni.SUM_BY_NONZERO_WEIGHTS){const s=on(e,"labels","meanSquaredError"),a=on(t,"predictions","meanSquaredError");let o=null;null!=n&&(o=on(n,"weights","meanSquaredError")),me(s.shape,a.shape,"Error in meanSquaredError: ");const i=oo(s,a);return wi(i,o,r)}});const Ii=pn({sigmoidCrossEntropy_:function(e,t,n,r=0,s=Ni.SUM_BY_NONZERO_WEIGHTS){let a=on(e,"multiClassLabels","sigmoidCrossEntropy");const o=on(t,"logits","sigmoidCrossEntropy");let i=null;if(null!=n&&(i=on(n,"weights","sigmoidCrossEntropy")),me(a.shape,o.shape,"Error in sigmoidCrossEntropy: "),r>0){const e=Pr(r),t=Pr(1),n=Pr(.5);a=dn(Vn(a,gs(t,e)),Vn(n,e))}const u=function(e,t){const n=on(e,"labels","sigmoidCrossEntropyWithLogits"),r=on(t,"logits","sigmoidCrossEntropyWithLogits");me(n.shape,r.shape,"Error in sigmoidCrossEntropyWithLogits: ");const s=Fa(r),a=Vn(r,n),o=hs(Hr(ms(ln(r))));return dn(gs(s,a),o)}(a,o);return wi(u,i,s)}});const $i=pn({softmaxCrossEntropy_:function(e,t,n,r=0,s=Ni.SUM_BY_NONZERO_WEIGHTS){let a=on(e,"onehotLabels","softmaxCrossEntropy");const o=on(t,"logits","softmaxCrossEntropy");let i=null;if(null!=n&&(i=on(n,"weights","softmaxCrossEntropy")),me(a.shape,o.shape,"Error in softmaxCrossEntropy: "),r>0){const e=Pr(r),t=Pr(1),n=Pr(a.shape[1]);a=dn(Vn(a,gs(t,e)),_r(e,n))}const u=function(e,t,n=-1){if(-1===n&&(n=t.rank-1),n!==t.rank-1)throw Error(`Softmax cross entropy along a non-last dimension is not yet supported. Labels / logits was rank ${t.rank} and dim was ${n}`);const r=ds(((e,t,r)=>{const s=xs(t,[n],!0),a=gs(vn(t,"float32"),s);r([e,a]);const o=ms(Vn(a,e));return{value:Ur(o,[n]),gradFunc:(e,t)=>{const[r,s]=t,a=Rr(e.shape,[n]);return[Vn(Mn(e,a),gs(vn(r,"float32"),Hr(s))),Vn(Mn(e,a),gs(Hr(s),vn(r,"float32")))]}}}));return r(e,t)}(a,o);return wi(u,i,s)}});const Ai=pn({sparseFillEmptyRows_:function(e,t,n,r){const s=on(e,"indices","sparseFillEmptyRows","int32"),a=on(t,"values","sparseFillEmptyRows"),o=on(n,"denseShape","sparseFillEmptyRows","int32"),i=on(r,"defaultValue","sparseFillEmptyRows",a.dtype);if(2!==s.rank)throw new Error(`Indices should be Tensor2D but received shape\n        ${s.shape}`);if(1!==a.rank)throw new Error(`Values should be Tensor1D but received shape ${a.shape}`);if(1!==o.rank)throw new Error(`Dense shape should be Tensor1D but received shape ${o.shape}`);if(0!==i.rank)throw new Error(`Default value should be a scalar but received shape ${i.shape}`);const u={indices:s,values:a,denseShape:o,defaultValue:i},p=tn.runKernel("SparseFillEmptyRows",u);return{outputIndices:p[0],outputValues:p[1],emptyRowIndicator:p[2],reverseIndexMap:p[3]}}});const Di=pn({sparseReshape_:function(e,t,n){const r=on(e,"inputIndices","sparseReshape","int32"),s=on(t,"inputShape","sparseReshape","int32"),a=on(n,"newShape","sparseReshape","int32");if(2!==r.rank)throw new Error(`Input indices should be Tensor2D but received shape\n        ${r.shape}`);if(1!==s.rank)throw new Error(`Input shape should be Tensor1D but received shape ${s.shape}`);if(1!==a.rank)throw new Error(`New shape should be Tensor1D but received shape ${a.shape}`);const o={inputIndices:r,inputShape:s,newShape:a},i=tn.runKernel("SparseReshape",o);return{outputIndices:i[0],outputShape:i[1]}}});const Oi=pn({sparseSegmentMean_:function(e,t,n){const r=on(e,"data","sparseSegmentMean"),s=on(t,"indices","sparseSegmentMean","int32"),a=on(n,"segmentIds","sparseSegmentMean","int32");if(r.rank<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.rank)throw new Error(`Indices should be Tensor1D but received shape\n          ${s.shape}`);if(1!==a.rank)throw new Error(`Segment ids should be Tensor1D but received shape\n          ${a.shape}`);const o={data:r,indices:s,segmentIds:a};return tn.runKernel("SparseSegmentMean",o)}});const Mi=pn({sparseSegmentSum_:function(e,t,n){const r=on(e,"data","sparseSegmentSum"),s=on(t,"indices","sparseSegmentSum","int32"),a=on(n,"segmentIds","sparseSegmentSum","int32");if(r.rank<1)throw new Error("Data should be at least 1 dimensional but received scalar");if(1!==s.rank)throw new Error(`Indices should be Tensor1D but received shape\n         ${s.shape}`);if(1!==a.rank)throw new Error(`Segment ids should be Tensor1D but received shape\n         ${a.shape}`);const o={data:r,indices:s,segmentIds:a};return tn.runKernel("SparseSegmentSum",o)}});const Ci=pn({stringNGrams_:function(e,t,n,r,s,a,o,i){const u=on(e,"data","stringNGrams","string");if("string"!==u.dtype)throw new Error("Data must be of datatype string");if(1!==u.shape.length)throw new Error(`Data must be a vector, saw: ${u.shape}`);const p=on(t,"dataSplits","stringNGrams");if("int32"!==p.dtype)throw new Error("Data splits must be of datatype int32");const l={separator:n,nGramWidths:r,leftPad:s,rightPad:a,padWidth:o,preserveShortSequences:i},c={data:u,dataSplits:p},h=tn.runKernel("StringNGrams",c,l);return{nGrams:h[0],nGramsSplits:h[1]}}});var Fi={__proto__:null,OP_SCOPE_SUFFIX:"__op",abs:ln,acos:cn,acosh:hn,add:dn,addN:mn,all:fn,any:yn,argMax:gn,argMin:bn,asin:xn,asinh:Nn,atan:wn,atan2:kn,atanh:Tn,avgPool:Cn,avgPool3d:Fn,basicLSTMCell:qn,batchNorm:Wn,batchNorm2d:jn,batchNorm3d:Gn,batchNorm4d:Hn,batchToSpaceND:Un,bincount:Zn,bitwiseAnd:Qn,booleanMaskAsync:vo,broadcastArgs:Xn,broadcastTo:Yn,buffer:Jn,cast:vn,ceil:er,clipByValue:nr,clone:Rn,complex:rr,concat:zn,concat1d:sr,concat2d:ar,concat3d:or,concat4d:ir,conv1d:pr,conv2d:ur,conv2dTranspose:cr,conv3d:hr,conv3dTranspose:mr,cos:fr,cosh:yr,cosineWindow:Oo,cumprod:gr,cumsum:br,denseBincount:xr,depthToSpace:Nr,depthwiseConv2d:wr,diag:kr,dilation2d:Tr,div:_r,divNoNan:Ar,dot:Dr,dropout:Ao,einsum:Or,elu:Mr,enclosingPowerOfTwo:Do,ensureShape:Cr,equal:Er,erf:Fr,euclideanNorm:Gr,exp:Hr,expandDims:Zr,expm1:Qr,eye:Yr,fft:to,fill:tr,floor:Jr,floorDiv:vr,fused:qo,gather:es,gatherND:$o,greater:ts,greaterEqual:ns,ifft:no,imag:rs,image:{flipLeftRight:Zo,grayscaleToRGB:Qo,resizeNearestNeighbor:di,resizeBilinear:hi,rgbToGrayscale:Xo,rotateWithOffset:Yo,cropAndResize:Ho,nonMaxSuppression:ei,nonMaxSuppressionAsync:ii,nonMaxSuppressionWithScore:ui,nonMaxSuppressionWithScoreAsync:pi,nonMaxSuppressionPadded:li,nonMaxSuppressionPaddedAsync:ci,threshold:mi,transform:fi},inTopKAsync:Mo,irfft:ro,isFinite:ss,isInf:as,isNaN:os,leakyRelu:is,less:us,lessEqual:ps,linalg:{bandPart:yi,gramSchmidt:gi,qr:xi},linspace:function(e,t,n){if(n<=0)throw new Error("The number of values should be positive.");const r={start:e,stop:t,num:n};return tn.runKernel("LinSpace",{},r)},localResponseNormalization:ls,log:cs,log1p:hs,logSigmoid:ys,logSoftmax:bs,logSumExp:xs,logicalAnd:Ns,logicalNot:ws,logicalOr:ks,logicalXor:Ts,losses:{absoluteDifference:ki,computeWeightedLoss:wi,cosineDistance:Ti,hingeLoss:vi,huberLoss:_i,logLoss:Si,meanSquaredError:Ei,sigmoidCrossEntropy:Ii,softmaxCrossEntropy:$i},lowerBound:function(e,t){return vs(e,t,"left")},matMul:Ln,max:zr,maxPool:_s,maxPool3d:Ss,maxPoolWithArgmax:Es,maximum:Is,mean:$s,meshgrid:function(e,t,{indexing:n="xy"}={}){if("xy"!==n&&"ij"!==n)throw new TypeError(`${n} is not a valid third argument to meshgrid`);if(void 0===e)return[];let r=on(e,"x","meshgrid",e instanceof Lt?e.dtype:"float32");if(void 0===t)return[r];let s=on(t,"y","meshgrid",t instanceof Lt?t.dtype:"float32");const a=ye(r.shape),o=ye(s.shape);return"xy"===n?(r=Mn(r,[1,-1]),s=Mn(s,[-1,1]),[Ln(Ds([o,1],r.dtype),r),Ln(s,Ds([1,a],s.dtype))]):(r=Mn(r,[-1,1]),s=Mn(s,[1,-1]),[Ln(r,Ds([1,o],r.dtype)),Ln(Ds([a,1],s.dtype),s)])},min:Lr,minimum:Os,mirrorPad:Ms,mod:Cs,moments:Fs,movingAverage:So,mul:Vn,multiRNNCell:Rs,multinomial:zs,neg:ms,norm:jr,notEqual:Ls,oneHot:Vs,ones:Ds,onesLike:Bs,op:pn,outerProduct:Ps,pad:Ks,pad1d:qs,pad2d:Us,pad3d:Ws,pad4d:js,pool:Hs,pow:Vr,prelu:Zs,print:function(e,t=!1){console.log(e.toString(t))},prod:Qs,raggedGather:Xs,raggedRange:Ys,raggedTensorToTensor:Js,rand:ea,randomGamma:Ea,randomNormal:Ia,randomStandardNormal:$a,randomUniform:Aa,randomUniformInt:Da,range:Oa,real:Ma,reciprocal:Ca,relu:Fa,relu6:Ra,reshape:Mn,reverse:za,reverse1d:La,reverse2d:Va,reverse3d:Ba,reverse4d:Pa,rfft:ao,round:Ka,rsqrt:qa,scalar:Pr,scatterND:Eo,searchSorted:vs,selu:Ua,separableConv2d:Wa,setdiff1dAsync:ja,sigmoid:Bn,sign:Ga,signal:{hammingWindow:Uo,hannWindow:Wo,frame:jo,stft:Go},sin:Ha,sinh:Za,slice:Pn,slice1d:Qa,slice2d:Xa,slice3d:Ya,slice4d:Ja,softmax:eo,softplus:fs,spaceToBatchND:Gs,sparse:{sparseFillEmptyRows:Ai,sparseReshape:Di,sparseSegmentMean:Oi,sparseSegmentSum:Mi},sparseToDense:Io,spectral:{fft:to,ifft:no,rfft:ao,irfft:ro},split:so,sqrt:Kr,square:qr,squaredDifference:oo,squeeze:io,stack:uo,step:po,stridedSlice:lo,string:{stringNGrams:Ci,stringSplit:pn({stringSplit_:function(e,t,n=!0){const r=on(e,"input","stringSplit","string"),s=on(t,"delimiter","stringSplit","string");if(1!==r.rank)throw new Error(`Input should be Tensor1D but received shape ${r.shape}`);if(0!==s.rank)throw new Error(`Delimiter should be a scalar but received shape ${s.shape}`);const a={skipEmpty:n},o={input:r,delimiter:s},i=tn.runKernel("StringSplit",o,a);return{indices:i[0],values:i[1],shape:i[2]}}}),stringToHashBucketFast:pn({stringToHashBucketFast_:function(e,t){const n=on(e,"input","stringToHashBucketFast","string"),r={numBuckets:t};if(t<=0)throw new Error("Number of buckets must be at least 1");const s={input:n};return tn.runKernel("StringToHashBucketFast",s,r)}}),staticRegexReplace:pn({staticRegexReplace_:function(e,t,n,r=!0){const s=on(e,"input","staticRegexReplace","string"),a={pattern:t,rewrite:n,replaceGlobal:r};return tn.runKernel("StaticRegexReplace",{x:s},a)}})},sub:gs,sum:Ur,tan:co,tanh:Kn,tensor:ho,tensor1d:mo,tensor2d:fo,tensor3d:function(e,t,n){if(fe(e),null!=t&&3!==t.length)throw new Error("tensor3d() requires shape to have three numbers");const r=rn(e,n);if(3!==r.length&&1!==r.length)throw new Error("tensor3d() requires values to be number[][][] or flat/TypedArray");if(1===r.length&&null==t)throw new Error("tensor3d() requires shape to be provided when `values` are a flat array");return Br(e,t,r,n)},tensor4d:function(e,t,n){if(fe(e),null!=t&&4!==t.length)throw new Error("tensor4d() requires shape to have four numbers");const r=rn(e,n);if(4!==r.length&&1!==r.length)throw new Error("tensor4d() requires values to be number[][][][] or flat/TypedArray");if(1===r.length&&null==t)throw new Error("tensor4d() requires shape to be provided when `values` are a flat array");return Br(e,t,r,n)},tensor5d:function(e,t,n){if(fe(e),null!=t&&5!==t.length)throw new Error("tensor5d() requires shape to have five numbers");const r=rn(e,n);if(5!==r.length&&1!==r.length)throw new Error("tensor5d() requires values to be number[][][][][] or flat/TypedArray");if(1===r.length&&null==t)throw new Error("tensor5d() requires shape to be provided when `values` are a flat array");return Br(e,t,r,n)},tensor6d:function(e,t,n){if(fe(e),null!=t&&6!==t.length)throw new Error("tensor6d() requires shape to have six numbers");const r=rn(e,n);if(6!==r.length&&1!==r.length)throw new Error("tensor6d() requires values to be number[][][][][][] or flat/TypedArray");if(1===r.length&&null==t)throw new Error("tensor6d() requires shape to be provided when `values` are a flat array");return Br(e,t=t||r,r,n)},tensorScatterUpdate:go,tile:Xr,topk:bo,transpose:_o,truncatedNormal:xo,unique:No,unsortedSegmentSum:wo,unstack:ko,upperBound:function(e,t){return vs(e,t,"right")},variable:function(e,t=!0,n,r){return tn.makeVariable(e,t,n,r)},where:Ir,whereAsync:To,zeros:As,zerosLike:$r};function Ri(e,t,r=""){if("number"!=typeof e&&"number"!=typeof t){n.assert(e.length===t.length,(()=>r+` Shapes ${e} and ${t} must match`));for(let s=0;s<e.length;s++){const a=e[s],o=t[s];n.assert(a<0||o<0||a===o,(()=>r+` Shapes ${e} and ${t} must match`))}}}function zi(e){return"number"!=typeof e&&!e.some((e=>e<0))}function Li(e,t,n){let r=Vi(e,n);const s=!zi(r);if(s&&0===t.length)throw new Error(`Tried to calculate elements of an empty list with non-fully-defined elementShape: ${r}`);if(s&&t.forEach((e=>{r=Vi(e.shape,r)})),!zi(r))throw new Error(`Non-fully-defined elementShape: ${r}`);return r}function Vi(e,t){if("number"==typeof e)return t;if("number"==typeof t)return e;if(e.length!==t.length)throw new Error(`Incompatible ranks during merge: ${e} vs. ${t}`);const n=[];for(let r=0;r<e.length;++r){const s=e[r],a=t[r];if(s>=0&&a>=0&&s!==a)throw new Error(`Incompatible shape during merge: ${e} vs. ${t}`);n[r]=s>=0?s:a}return n}class Bi{constructor(e,t,n,r,o,i,u){this.name=e,this.dtype=t,this.maxSize=n,this.elementShape=r,this.identicalElementShapes=o,this.dynamicSize=i,this.clearAfterRead=u,this.tensors=[],this.closed_=!1,this.idTensor=s(0),a(this.idTensor)}get id(){return this.idTensor.id}get closed(){return this.closed_}clearAndClose(e){this.tensors.forEach((t=>{null!=e&&e.has(t.tensor.id)||t.tensor.dispose()})),this.tensors=[],this.closed_=!0,this.idTensor.dispose()}size(){return this.tensors.length}read(e){if(this.closed_)throw new Error(`TensorArray ${this.name} has already been closed.`);if(e<0||e>=this.size())throw new Error(`Tried to read from index ${e}, but array size is: ${this.size()}`);const t=this.tensors[e];if(t.cleared)throw new Error(`TensorArray ${this.name}: Could not read index ${e} twice because it was cleared after a previous read (perhaps try setting clear_after_read = false?).`);return this.clearAfterRead&&(t.cleared=!0),t.read=!0,t.tensor}readMany(e){return e.map((e=>this.read(e)))}write(e,t){if(this.closed_)throw new Error(`TensorArray ${this.name} has already been closed.`);if(e<0||!this.dynamicSize&&e>=this.maxSize)throw new Error(`Tried to write to index ${e}, but array is not resizeable and size is: ${this.maxSize}`);const n=this.tensors[e]||{};if(t.dtype!==this.dtype)throw new Error(`TensorArray ${this.name}: Could not write to TensorArray index ${e},\n          because the value dtype is ${t.dtype}, but TensorArray dtype is ${this.dtype}.`);if(0!==this.size()||null!=this.elementShape&&0!==this.elementShape.length||(this.elementShape=t.shape),Ri(this.elementShape,t.shape,`TensorArray ${this.name}: Could not write to TensorArray index ${e}.`),n.read)throw new Error(`TensorArray ${this.name}: Could not write to TensorArray index ${e}, because it has already been read.`);if(n.written)throw new Error(`TensorArray ${this.name}: Could not write to TensorArray index ${e}, because it has already been written.`);n.tensor=t,a(t),n.written=!0,this.tensors[e]=n}writeMany(e,t){if(e.length!==t.length)throw new Error(`TensorArray ${this.name}: could not write multiple tensors,because the index size: ${e.length} is not the same as tensors size: ${t.length}.`);e.forEach(((e,n)=>this.write(e,t[n])))}gather(e,t){if(t&&t!==this.dtype)throw new Error(`TensorArray dtype is ${this.dtype} but gather requested dtype ${t}`);if(e)e=e.slice(0,this.size());else{e=[];for(let t=0;t<this.size();t++)e.push(t)}if(0===e.length)return o([],[0].concat(this.elementShape));const n=this.readMany(e);return Ri(this.elementShape,n[0].shape,"TensorArray shape mismatch: "),i(n,0)}concat(e){if(e&&e!==this.dtype)throw new Error(`TensorArray dtype is ${this.dtype} but concat requested dtype ${e}`);if(0===this.size())return o([],[0].concat(this.elementShape));const t=[];for(let e=0;e<this.size();e++)t.push(e);const n=this.readMany(t);return Ri(this.elementShape,n[0].shape,`TensorArray shape mismatch: tensor array shape (${this.elementShape}) vs first tensor shape (${n[0].shape})`),u(n,0)}scatter(e,t){if(t.dtype!==this.dtype)throw new Error(`TensorArray dtype is ${this.dtype} but tensor has dtype ${t.dtype}`);if(e.length!==t.shape[0])throw new Error(`Expected len(indices) == tensor.shape[0], but saw: ${e.length} vs. ${t.shape[0]}`);const n=Math.max(...e);if(!this.dynamicSize&&n>=this.maxSize)throw new Error(`Max index must be < array size (${n}  vs. ${this.maxSize})`);this.writeMany(e,p(t,0))}split(e,t){if(t.dtype!==this.dtype)throw new Error(`TensorArray dtype is ${this.dtype} but tensor has dtype ${t.dtype}`);let n=0;const r=e.map((e=>(n+=e,n)));if(n!==t.shape[0])throw new Error(`Expected sum of lengths to be equal to\n          tensor.shape[0], but sum of lengths is\n        ${n}, and tensor's shape is: ${t.shape}`);if(!this.dynamicSize&&e.length!==this.maxSize)throw new Error(`TensorArray's size is not equal to the size of lengths (${this.maxSize} vs. ${e.length}), and the TensorArray is not marked as dynamically resizeable`);const s=0===n?0:t.size/n,a=[];l((()=>{t=c(t,[1,n,s]);for(let n=0;n<e.length;++n){const o=[0,0===n?0:r[n-1],0],i=[1,e[n],s];a[n]=c(h(t,o,i),this.elementShape)}return a}));const o=[];for(let t=0;t<e.length;t++)o[t]=t;this.writeMany(o,a)}}class Pi{get id(){return this.idTensor.id}constructor(e,t,n,r=-1){this.tensors=e,this.elementShape=t,this.elementDtype=n,null!=e&&e.forEach((e=>{if(n!==e.dtype)throw new Error(`Invalid data types; op elements ${n}, but list elements ${e.dtype}`);Ri(t,e.shape,"TensorList shape mismatch: "),a(e)})),this.idTensor=s(0),this.maxNumElements=r,a(this.idTensor)}copy(){return new Pi([...this.tensors],this.elementShape,this.elementDtype)}clearAndClose(e){this.tensors.forEach((t=>{null!=e&&e.has(t.id)||t.dispose()})),this.tensors.length=0,this.idTensor.dispose()}size(){return this.tensors.length}stack(e,t,n=-1){if(t!==this.elementDtype)throw new Error(`Invalid data types; op elements ${t}, but list elements ${this.elementDtype}`);if(-1!==n&&this.tensors.length!==n)throw new Error(`Operation expected a list with ${n} elements but got a list with ${this.tensors.length} elements.`);Ri(e,this.elementShape,"TensorList shape mismatch: ");const r=Li(this.elementShape,this.tensors,e);return l((()=>{const e=this.tensors.map((e=>c(e,r)));return i(e,0)}))}popBack(e,t){if(t!==this.elementDtype)throw new Error(`Invalid data types; op elements ${t}, but list elements ${this.elementDtype}`);if(0===this.size())throw new Error("Trying to pop from an empty list.");const n=Li(this.elementShape,this.tensors,e),r=this.tensors.pop();return r.kept=!1,Ri(r.shape,e,"TensorList shape mismatch: "),c(r,n)}pushBack(e){if(e.dtype!==this.elementDtype)throw new Error(`Invalid data types; op elements ${e.dtype}, but list elements ${this.elementDtype}`);if(Ri(e.shape,this.elementShape,"TensorList shape mismatch: "),this.maxNumElements===this.size())throw new Error("Trying to push element into a full list.");a(e),this.tensors.push(e)}resize(e){if(e<0)throw new Error(`TensorListResize expects size to be non-negative. Got: ${e}`);if(-1!==this.maxNumElements&&e>this.maxNumElements)throw new Error(`TensorListResize input size ${e} is greater maxNumElement ${this.maxNumElements}.`);const t=new Pi([],this.elementShape,this.elementDtype,this.maxNumElements);t.tensors.length=e;for(let n=0;n<Math.min(this.tensors.length,e);++n)t.tensors[n]=this.tensors[n];return t}getItem(e,t,n){if(n!==this.elementDtype)throw new Error(`Invalid data types; op elements ${n}, but list elements ${this.elementDtype}`);if(e<0||e>this.tensors.length)throw new Error(`Trying to access element ${e} in a list with ${this.tensors.length} elements.`);if(null==this.tensors[e])throw new Error(`element at index ${e} is null.`);Ri(this.tensors[e].shape,t,"TensorList shape mismatch: ");const r=Li(this.elementShape,this.tensors,t);return c(this.tensors[e],r)}setItem(e,t){if(t.dtype!==this.elementDtype)throw new Error(`Invalid data types; op elements ${t.dtype}, but list elements ${this.elementDtype}`);if(e<0||-1!==this.maxNumElements&&e>=this.maxNumElements)throw new Error(`Trying to set element ${e} in a list with max ${this.maxNumElements} elements.`);Ri(this.elementShape,t.shape,"TensorList shape mismatch: "),a(t),null!=this.tensors[e]&&(this.tensors[e].kept=!1),this.tensors[e]=t}gather(e,t,n){if(t!==this.elementDtype)throw new Error(`Invalid data types; op elements ${t}, but list elements ${this.elementDtype}`);Ri(this.elementShape,n,"TensorList shape mismatch: "),e=e.slice(0,this.size());const r=Li(this.elementShape,this.tensors,n);return 0===e.length?o([],[0].concat(r)):l((()=>{const t=e.map((e=>c(this.tensors[e],r)));return i(t,0)}))}concat(e,t){if(e&&e!==this.elementDtype)throw new Error(`TensorList dtype is ${this.elementDtype} but concat requested dtype ${e}`);Ri(this.elementShape,t,"TensorList shape mismatch: ");const n=Li(this.elementShape,this.tensors,t);return 0===this.size()?o([],[0].concat(n)):l((()=>{const e=this.tensors.map((e=>c(e,n)));return u(e,0)}))}}const Ki=async(e,t,n)=>{switch(e.op){case"If":case"StatelessIf":{const r=T("thenBranch",e,t,n),s=T("elseBranch",e,t,n),a=T("cond",e,t,n),o=T("args",e,t,n);return(await a.data())[0]?n.functionMap[r].executeFunctionAsync(o,n.tensorArrayMap,n.tensorListMap):n.functionMap[s].executeFunctionAsync(o,n.tensorArrayMap,n.tensorListMap)}case"While":case"StatelessWhile":{const r=T("body",e,t,n),s=T("cond",e,t,n),a=T("args",e,t,n),o=await n.functionMap[s].executeFunctionAsync(a,n.tensorArrayMap,n.tensorListMap),i=a.map((e=>e.id));let u=await o[0].data();o.forEach((e=>{e.kept||-1!==i.indexOf(e.id)||e.dispose()}));let p=a;for(;u[0];){const e=p;p=await n.functionMap[r].executeFunctionAsync(p,n.tensorArrayMap,n.tensorListMap);const t=p.map((e=>e.id));e.forEach((e=>{e.kept||-1!==i.indexOf(e.id)||-1!==t.indexOf(e.id)||e.dispose()}));const a=await n.functionMap[s].executeFunctionAsync(p,n.tensorArrayMap,n.tensorListMap);u=await a[0].data(),a.forEach((e=>{e.kept||-1!==i.indexOf(e.id)||-1!==t.indexOf(e.id)||e.dispose()}))}return p}case"LoopCond":return[A(T("pred",e,t,n))];case"Switch":{const r=T("pred",e,t,n);let s=T("data",e,t,n);return s.kept||(s=A(s)),(await r.data())[0]?[void 0,s]:[s,void 0]}case"Merge":{const r=e.inputNames.find((e=>void 0!==v(e,t,n)));if(r){return[A(v(r,t,n))]}return}case"Enter":{const r=T("frameName",e,t,n),s=T("tensor",e,t,n);return n.enterFrame(r),[A(s)]}case"Exit":{const r=T("tensor",e,t,n);return n.exitFrame(),[A(r)]}case"NextIteration":{const r=T("tensor",e,t,n);return n.nextIteration(),[A(r)]}case"TensorArrayV3":{const r=T("size",e,t,n),a=T("dtype",e,t,n),o=T("elementShape",e,t,n),i=T("dynamicSize",e,t,n),u=T("clearAfterRead",e,t,n),p=T("identicalElementShapes",e,t,n),l=T("name",e,t,n),c=new Bi(l,a,r,o,p,i,u);return n.addTensorArray(c),[c.idTensor,s(1)]}case"TensorArrayWriteV3":{const r=T("tensorArrayId",e,t,n),s=T("index",e,t,n),a=T("tensor",e,t,n),o=n.getTensorArray(r.id);return o.write(s,a),[o.idTensor]}case"TensorArrayReadV3":{const r=T("tensorArrayId",e,t,n),s=T("index",e,t,n);return[n.getTensorArray(r.id).read(s)]}case"TensorArrayGatherV3":{const r=T("tensorArrayId",e,t,n),s=T("indices",e,t,n),a=T("dtype",e,t,n);return[n.getTensorArray(r.id).gather(s,a)]}case"TensorArrayScatterV3":{const r=T("tensorArrayId",e,t,n),s=T("indices",e,t,n),a=T("tensor",e,t,n),o=n.getTensorArray(r.id);return o.scatter(s,a),[o.idTensor]}case"TensorArrayConcatV3":{const r=T("tensorArrayId",e,t,n),s=n.getTensorArray(r.id),a=T("dtype",e,t,n);return[s.concat(a)]}case"TensorArraySplitV3":{const r=T("tensorArrayId",e,t,n),s=T("tensor",e,t,n),a=T("lengths",e,t,n),o=n.getTensorArray(r.id);return o.split(a,s),[o.idTensor]}case"TensorArraySizeV3":{const r=T("tensorArrayId",e,t,n),a=n.getTensorArray(r.id);return[s(a.size(),"int32")]}case"TensorArrayCloseV3":{const r=T("tensorArrayId",e,t,n),s=n.getTensorArray(r.id);return s.clearAndClose(),[s.idTensor]}case"TensorListSetItem":{const r=T("tensorListId",e,t,n),s=T("index",e,t,n),a=T("tensor",e,t,n),o=n.getTensorList(r.id);return o.setItem(s,a),[o.idTensor]}case"TensorListGetItem":{const r=T("tensorListId",e,t,n),s=T("index",e,t,n),a=T("elementShape",e,t,n),o=T("elementDType",e,t,n);return[n.getTensorList(r.id).getItem(s,a,o)]}case"TensorListScatterV2":case"TensorListScatter":{const r=T("indices",e,t,n),s=function(e,t,n,r){if(t.length!==e.shape[0])throw new Error(`Expected len(indices) == tensor.shape[0], but saw: ${t.length} vs. ${e.shape[0]}`);const s=Math.max(...t);if(null!=r&&-1!==r&&s>=r)throw new Error(`Max index must be < array size (${s}  vs. ${r})`);const a=new Pi([],n,e.dtype,r),o=p(e,0);return t.forEach(((e,t)=>{a.setItem(e,o[t])})),a}(T("tensor",e,t,n),r,T("elementShape",e,t,n),T("numElements",e,t,n));return n.addTensorList(s),[s.idTensor]}case"TensorListReserve":case"EmptyTensorList":{const r=T("elementShape",e,t,n),s=T("elementDType",e,t,n);let a;a="TensorListReserve"===e.op?"numElements":"maxNumElements";const o=T(a,e,t,n),i=function(e,t,n,r){return new Pi([],e,t,r)}(r,s,0,"TensorListReserve"===e.op?-1:o);return n.addTensorList(i),[i.idTensor]}case"TensorListGather":{const r=T("tensorListId",e,t,n),s=T("indices",e,t,n),a=T("elementShape",e,t,n),o=T("elementDType",e,t,n);return[n.getTensorList(r.id).gather(s,o,a)]}case"TensorListStack":{const r=T("tensorListId",e,t,n),s=T("elementShape",e,t,n),a=T("elementDType",e,t,n),o=T("numElements",e,t,n);return[n.getTensorList(r.id).stack(s,a,o)]}case"TensorListFromTensor":{const r=function(e,t,n){const r=e.dtype;if(e.shape.length<1)throw new Error(`Tensor must be at least a vector, but saw shape: ${e.shape}`);if(e.dtype!==n)throw new Error(`Invalid data types; op elements ${e.dtype}, but list elements ${n}`);Ri(e.shape.slice(1),t,"TensorList shape mismatch: ");const s=p(e);return new Pi(s,t,r)}(T("tensor",e,t,n),T("elementShape",e,t,n),T("elementDType",e,t,n));return n.addTensorList(r),[r.idTensor]}case"TensorListConcat":case"TensorListConcatV2":{const r=T("tensorListId",e,t,n),s=n.getTensorList(r.id),a=T("dtype",e,t,n),o=T("elementShape",e,t,n);return[s.concat(a,o)]}case"TensorListPushBack":{const r=T("tensorListId",e,t,n),s=T("tensor",e,t,n),a=n.getTensorList(r.id);return a.pushBack(s),[a.idTensor]}case"TensorListPopBack":{const r=T("tensorListId",e,t,n),s=T("elementShape",e,t,n),a=T("elementDType",e,t,n);return[n.getTensorList(r.id).popBack(s,a)]}case"TensorListSplit":{const r=T("tensor",e,t,n),s=T("elementShape",e,t,n),a=function(e,t,n){let r=0;const s=t.map((e=>(r+=e,r)));if(r!==e.shape[0])throw new Error(`Expected sum of lengths to be equal to\n          tensor.shape[0], but sum of lengths is\n        ${r}, and tensor's shape is: ${e.shape}`);const a=Vi(e.shape.slice(1),n),o=0===r?0:e.size/r,i=l((()=>{const n=[];e=c(e,[1,r,o]);for(let r=0;r<t.length;++r){const i=[0,0===r?0:s[r-1],0],u=[1,t[r],o];n[r]=c(h(e,i,u),a)}return e.dispose(),n})),u=new Pi([],n,e.dtype,t.length);for(let e=0;e<i.length;e++)u.setItem(e,i[e]);return u}(r,T("lengths",e,t,n),s);return n.addTensorList(a),[a.idTensor]}case"TensorListLength":{const r=T("tensorListId",e,t,n),a=n.getTensorList(r.id);return[s(a.size(),"int32")]}case"TensorListResize":{const r=T("tensorListId",e,t,n),s=T("size",e,t,n),a=n.getTensorList(r.id).resize(s);return n.addTensorList(a),[a.idTensor]}default:throw TypeError(`Node type ${e.op} is not implemented`)}};function qi(e,t,n){const[r,s]=T("fusedOps",e,t,n),a="biasadd"===r,o=!a,i="prelu"===s,u="fusedbatchnorm"===r,p=T("numArgs",e,t,n);if(a){if(i&&2!==p)throw new Error("FusedConv2d and DepthwiseConv2d with BiasAdd and Prelu must have two extra arguments: bias and alpha.");if(!i&&a&&1!==p)throw new Error("FusedConv2d and DepthwiseConv2d with BiasAdd must have one extra argument: bias.")}if(u)throw new Error("FusedConv2d and DepthwiseConv2d with FusedBatchNorm is not supported");const l=T("strides",e,t,n),c=$(e,t,n),h=T("dataFormat",e,t,n).toUpperCase(),d=T("dilations",e,t,n);let[m,f]=T("args",e,t,n);o&&(f=m,m=void 0);return{stride:l,pad:c,dataFormat:h,dilations:d,biasArg:m,preluArg:f,activationFunc:s,leakyreluAlpha:T("leakyreluAlpha",e,t,n)}}function Ui(e,t,n){return{boxes:T("boxes",e,t,n),scores:T("scores",e,t,n),maxOutputSize:T("maxOutputSize",e,t,n),iouThreshold:T("iouThreshold",e,t,n),scoreThreshold:T("scoreThreshold",e,t,n),softNmsSigma:T("softNmsSigma",e,t,n)}}class Wi{get id(){return this.handle.id}constructor(e,t){this.keyDType=e,this.valueDType=t,this.handle=s(0),this.tensorMap=new Map,a(this.handle)}clearAndClose(){this.tensorMap.forEach((e=>e.dispose())),this.tensorMap.clear(),this.handle.dispose()}size(){return this.tensorMap.size}tensorSize(){return Pr(this.size(),"int32")}async import(e,t){this.checkKeyAndValueTensor(e,t);const r=await e.data();return this.tensorMap.forEach((e=>e.dispose())),this.tensorMap.clear(),l((()=>{const e=p(t),s=r.length,o=e.length;n.assert(s===o,(()=>`The number of elements doesn't match, keys has ${s} elements, the values has ${o} elements.`));for(let t=0;t<s;t++){const n=r[t],s=e[t];a(s),this.tensorMap.set(n,s)}return this.handle}))}async find(e,t){this.checkKeyAndValueTensor(e,t);const n=await e.data();return l((()=>{const e=[];for(let r=0;r<n.length;r++){const s=n[r],a=this.findWithDefault(s,t);e.push(a)}return i(e)}))}findWithDefault(e,t){const n=this.tensorMap.get(e);return null!=n?n:t}checkKeyAndValueTensor(e,t){if(e.dtype!==this.keyDType)throw new Error(`Expect key dtype ${this.keyDType}, but got ${e.dtype}`);if(t.dtype!==this.valueDType)throw new Error(`Expect value dtype ${this.valueDType}, but got ${t.dtype}`)}}function ji(t,r,s,a,o=e.tidy){const i=((e,t,r)=>{switch(e.category){case"arithmetic":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"BiasAdd":case"AddV2":case"Add":return[r.add(T("a",e,t,n),T("b",e,t,n))];case"AddN":return[r.addN(T("tensors",e,t,n))];case"FloorMod":case"Mod":return[r.mod(T("a",e,t,n),T("b",e,t,n))];case"Mul":return[r.mul(T("a",e,t,n),T("b",e,t,n))];case"RealDiv":case"Div":return[r.div(T("a",e,t,n),T("b",e,t,n))];case"DivNoNan":return[r.divNoNan(T("a",e,t,n),T("b",e,t,n))];case"FloorDiv":return[r.floorDiv(T("a",e,t,n),T("b",e,t,n))];case"Sub":return[r.sub(T("a",e,t,n),T("b",e,t,n))];case"Minimum":return[r.minimum(T("a",e,t,n),T("b",e,t,n))];case"Maximum":return[r.maximum(T("a",e,t,n),T("b",e,t,n))];case"Pow":return[r.pow(T("a",e,t,n),T("b",e,t,n))];case"SquaredDifference":return[r.squaredDifference(T("a",e,t,n),T("b",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"basic_math":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Abs":case"ComplexAbs":return[r.abs(T("x",e,t,n))];case"Acos":return[r.acos(T("x",e,t,n))];case"Acosh":return[r.acosh(T("x",e,t,n))];case"Asin":return[r.asin(T("x",e,t,n))];case"Asinh":return[r.asinh(T("x",e,t,n))];case"Atan":return[r.atan(T("x",e,t,n))];case"Atan2":return[r.atan2(T("x",e,t,n),T("y",e,t,n))];case"Atanh":return[r.atanh(T("x",e,t,n))];case"Ceil":return[r.ceil(T("x",e,t,n))];case"Complex":return[r.complex(T("real",e,t,n),T("imag",e,t,n))];case"Cos":return[r.cos(T("x",e,t,n))];case"Cosh":return[r.cosh(T("x",e,t,n))];case"Elu":return[r.elu(T("x",e,t,n))];case"Erf":return[r.erf(T("x",e,t,n))];case"Exp":return[r.exp(T("x",e,t,n))];case"Expm1":return[r.expm1(T("x",e,t,n))];case"Floor":return[r.floor(T("x",e,t,n))];case"Log":return[r.log(T("x",e,t,n))];case"Log1p":return[r.log1p(T("x",e,t,n))];case"Imag":return[r.imag(T("x",e,t,n))];case"Neg":return[r.neg(T("x",e,t,n))];case"Reciprocal":return[r.reciprocal(T("x",e,t,n))];case"Real":return[r.real(T("x",e,t,n))];case"Relu":return[r.relu(T("x",e,t,n))];case"Round":return[r.round(T("x",e,t,n))];case"Selu":return[r.selu(T("x",e,t,n))];case"Sigmoid":return[r.sigmoid(T("x",e,t,n))];case"Sin":return[r.sin(T("x",e,t,n))];case"Sign":return[r.sign(T("x",e,t,n))];case"Sinh":return[r.sinh(T("x",e,t,n))];case"Softplus":return[r.softplus(T("x",e,t,n))];case"Sqrt":return[r.sqrt(T("x",e,t,n))];case"Square":return[r.square(T("x",e,t,n))];case"Tanh":return[r.tanh(T("x",e,t,n))];case"Tan":return[r.tan(T("x",e,t,n))];case"ClipByValue":return[r.clipByValue(T("x",e,t,n),T("clipValueMin",e,t,n),T("clipValueMax",e,t,n))];case"Relu6":return[r.relu6(T("x",e,t,n))];case"Rsqrt":return[r.rsqrt(v(e.inputNames[0],t,n))];case"LeakyRelu":return[r.leakyRelu(T("x",e,t,n),T("alpha",e,t,n))];case"Prelu":return[r.prelu(T("x",e,t,n),T("alpha",e,t,n))];case"IsNan":return[r.isNaN(v(e.inputNames[0],t,n))];case"IsInf":return[r.isInf(v(e.inputNames[0],t,n))];case"IsFinite":return[r.isFinite(v(e.inputNames[0],t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"control":return Ki(e,t,r);case"convolution":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Conv1D":{const s=T("stride",e,t,n),a=T("pad",e,t,n),o=T("dataFormat",e,t,n).toUpperCase(),i=T("dilation",e,t,n);return[r.conv1d(T("x",e,t,n),T("filter",e,t,n),s,a,o,i)]}case"Conv2D":{const s=T("strides",e,t,n),a=$(e,t,n),o=T("dataFormat",e,t,n).toUpperCase(),i=T("dilations",e,t,n);return[r.conv2d(T("x",e,t,n),T("filter",e,t,n),[s[1],s[2]],a,o,[i[1],i[2]])]}case"_FusedConv2D":{const{stride:s,pad:a,dataFormat:o,dilations:i,biasArg:u,preluArg:p,activationFunc:l,leakyreluAlpha:c}=qi(e,t,n);return[r.fused.conv2d({x:T("x",e,t,n),filter:T("filter",e,t,n),strides:[s[1],s[2]],pad:a,dataFormat:o,dilations:[i[1],i[2]],bias:u,activation:l,preluActivationWeights:p,leakyreluAlpha:c})]}case"FusedDepthwiseConv2dNative":{const{stride:s,pad:a,dataFormat:o,dilations:i,biasArg:u,preluArg:p,activationFunc:l,leakyreluAlpha:c}=qi(e,t,n);return[r.fused.depthwiseConv2d({x:T("x",e,t,n),filter:T("filter",e,t,n),strides:[s[1],s[2]],pad:a,dataFormat:o,dilations:[i[1],i[2]],bias:u,activation:l,preluActivationWeights:p,leakyreluAlpha:c})]}case"Conv2DBackpropInput":case"Conv2dTranspose":{const s=T("outputShape",e,t,n),a=T("strides",e,t,n),o=$(e,t,n);return[r.conv2dTranspose(T("x",e,t,n),T("filter",e,t,n),s,[a[1],a[2]],o)]}case"DepthwiseConv2dNative":case"DepthwiseConv2d":{const s=T("strides",e,t,n),a=$(e,t,n),o=T("dilations",e,t,n),i=T("dataFormat",e,t,n).toUpperCase();return[r.depthwiseConv2d(T("input",e,t,n),T("filter",e,t,n),[s[1],s[2]],a,i,[o[1],o[2]])]}case"Conv3D":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("dataFormat",e,t,n).toUpperCase(),i=T("dilations",e,t,n);return[r.conv3d(T("x",e,t,n),T("filter",e,t,n),[s[1],s[2],s[3]],a,o,[i[1],i[2],i[3]])]}case"AvgPool":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("kernelSize",e,t,n);return[r.avgPool(T("x",e,t,n),[o[1],o[2]],[s[1],s[2]],a)]}case"MaxPool":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("kernelSize",e,t,n);return[r.maxPool(T("x",e,t,n),[o[1],o[2]],[s[1],s[2]],a)]}case"MaxPoolWithArgmax":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("kernelSize",e,t,n),i=T("includeBatchInIndex",e,t,n),{result:u,indexes:p}=r.maxPoolWithArgmax(T("x",e,t,n),[o[1],o[2]],[s[1],s[2]],a,i);return[u,p]}case"AvgPool3D":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("kernelSize",e,t,n);return[r.avgPool3d(T("x",e,t,n),[o[1],o[2],o[3]],[s[1],s[2],s[3]],a)]}case"MaxPool3D":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("kernelSize",e,t,n);return[r.maxPool3d(T("x",e,t,n),[o[1],o[2],o[3]],[s[1],s[2],s[3]],a)]}case"Dilation2D":{const s=T("strides",e,t,n),a=T("pad",e,t,n),o=T("dilations",e,t,n),i=s[1],u=s[2],p=o[1],l=o[2];return[r.dilation2d(T("x",e,t,n),T("filter",e,t,n),[i,u],a,[p,l],"NHWC")]}default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"creation":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Fill":{const s=T("shape",e,t,n),a=T("dtype",e,t,n),o=T("value",e,t,n);return[r.fill(s,o,a)]}case"LinSpace":{const s=T("start",e,t,n),a=T("stop",e,t,n),o=T("num",e,t,n);return[r.linspace(s,a,o)]}case"Multinomial":{const s=T("logits",e,t,n),a=T("numSamples",e,t,n),o=T("seed",e,t,n);return[r.multinomial(s,a,o)]}case"OneHot":{const s=T("indices",e,t,n),a=T("depth",e,t,n),o=T("onValue",e,t,n),i=T("offValue",e,t,n),u=T("dtype",e,t,n);return[r.oneHot(s,a,o,i,u)]}case"Ones":return[r.ones(T("shape",e,t,n),T("dtype",e,t,n))];case"OnesLike":return[r.onesLike(T("x",e,t,n))];case"RandomStandardNormal":return[r.randomStandardNormal(T("shape",e,t,n),T("dtype",e,t,n),T("seed",e,t,n))];case"RandomUniform":return[r.randomUniform(T("shape",e,t,n),T("minval",e,t,n),T("maxval",e,t,n),T("dtype",e,t,n))];case"RandomUniformInt":return[r.randomUniformInt(T("shape",e,t,n),T("minval",e,t,n),T("maxval",e,t,n),T("seed",e,t,n))];case"Range":{const s=T("start",e,t,n),a=T("stop",e,t,n),o=T("step",e,t,n);return[r.range(s,a,o,T("dtype",e,t,n))]}case"TruncatedNormal":{const s=T("shape",e,t,n),a=T("mean",e,t,n),o=T("stdDev",e,t,n),i=T("seed",e,t,n);return[r.truncatedNormal(s,a,o,T("dtype",e,t,n),i)]}case"Zeros":return[r.zeros(T("shape",e,t,n),T("dtype",e,t,n))];case"ZerosLike":return[r.zerosLike(T("x",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"dynamic":return(async(e,t,n,r,s=Fi)=>{switch(e.op){case"NonMaxSuppressionV5":{const{boxes:r,scores:a,maxOutputSize:o,iouThreshold:i,scoreThreshold:u,softNmsSigma:p}=Ui(e,t,n),l=await s.image.nonMaxSuppressionWithScoreAsync(r,a,o,i,u,p);return[l.selectedIndices,l.selectedScores]}case"NonMaxSuppressionV4":{const{boxes:r,scores:a,maxOutputSize:o,iouThreshold:i,scoreThreshold:u}=Ui(e,t,n),p=T("padToMaxOutputSize",e,t,n),l=await s.image.nonMaxSuppressionPaddedAsync(r,a,o,i,u,p);return[l.selectedIndices,l.validOutputs]}case"NonMaxSuppressionV3":case"NonMaxSuppressionV2":{const{boxes:r,scores:a,maxOutputSize:o,iouThreshold:i,scoreThreshold:u}=Ui(e,t,n);return[await s.image.nonMaxSuppressionAsync(r,a,o,i,u)]}case"Where":{const r=s.cast(T("condition",e,t,n),"bool"),a=[await s.whereAsync(r)];return r.dispose(),a}case"ListDiff":return s.setdiff1dAsync(T("x",e,t,n),T("y",e,t,n));default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r);case"evaluation":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"LowerBound":{const s=T("sortedSequence",e,t,n),a=T("values",e,t,n);return[r.lowerBound(s,a)]}case"TopKV2":{const s=T("x",e,t,n),a=T("k",e,t,n),o=T("sorted",e,t,n),i=r.topk(s,a,o);return[i.values,i.indices]}case"UpperBound":{const s=T("sortedSequence",e,t,n),a=T("values",e,t,n);return[r.upperBound(s,a)]}case"Unique":{const s=T("x",e,t,n),a=r.unique(s);return[a.values,a.indices]}case"UniqueV2":{const s=T("x",e,t,n),a=T("axis",e,t,n),o=r.unique(s,a);return[o.values,o.indices]}default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"image":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"ResizeBilinear":{const s=T("images",e,t,n),a=T("size",e,t,n),o=T("alignCorners",e,t,n),i=T("halfPixelCenters",e,t,n);return[r.image.resizeBilinear(s,[a[0],a[1]],o,i)]}case"ResizeNearestNeighbor":{const s=T("images",e,t,n),a=T("size",e,t,n),o=T("alignCorners",e,t,n),i=T("halfPixelCenters",e,t,n);return[r.image.resizeNearestNeighbor(s,[a[0],a[1]],o,i)]}case"CropAndResize":{const s=T("image",e,t,n),a=T("boxes",e,t,n),o=T("boxInd",e,t,n),i=T("cropSize",e,t,n),u=T("method",e,t,n),p=T("extrapolationValue",e,t,n);return[r.image.cropAndResize(s,a,o,i,u,p)]}case"ImageProjectiveTransformV3":{const s=T("images",e,t,n),a=T("transforms",e,t,n),o=T("outputShape",e,t,n),i=T("fillValue",e,t,n),u=T("interpolation",e,t,n),p=T("fillMode",e,t,n);return[r.image.transform(s,a,u.toLowerCase(),p.toLowerCase(),i,o)]}default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"graph":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Const":return t[e.name];case"PlaceholderWithDefault":const s=T("default",e,t,n);return[v(e.name,t,n)||s];case"Placeholder":return[v(e.name,t,n)];case"Identity":case"StopGradient":case"FakeQuantWithMinMaxVars":case"Snapshot":return[A(T("x",e,t,n))];case"IdentityN":return T("x",e,t,n).map((e=>A(e)));case"Shape":return[r.tensor1d(T("x",e,t,n).shape,"int32")];case"ShapeN":return T("x",e,t,n).map((e=>r.tensor1d(e.shape)));case"Size":return[r.scalar(T("x",e,t,n).size,"int32")];case"Rank":return[r.scalar(T("x",e,t,n).rank,"int32")];case"NoOp":return[r.scalar(1)];case"Print":const a=T("x",e,t,n),o=T("data",e,t,n),i=T("message",e,t,n),u=T("summarize",e,t,n);console.warn("The graph has a tf.print() operation,usually used for debugging, which slows down performance."),console.log(i);for(let e=0;e<o.length;e++)console.log(Array.prototype.slice.call(o[e].dataSync()).slice(0,u));return[a];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"logical":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Equal":return[r.equal(T("a",e,t,n),T("b",e,t,n))];case"NotEqual":return[r.notEqual(T("a",e,t,n),T("b",e,t,n))];case"Greater":return[r.greater(T("a",e,t,n),T("b",e,t,n))];case"GreaterEqual":return[r.greaterEqual(T("a",e,t,n),T("b",e,t,n))];case"Less":return[r.less(T("a",e,t,n),T("b",e,t,n))];case"LessEqual":return[r.lessEqual(T("a",e,t,n),T("b",e,t,n))];case"LogicalAnd":return[r.logicalAnd(T("a",e,t,n),T("b",e,t,n))];case"LogicalNot":return[r.logicalNot(T("a",e,t,n))];case"LogicalOr":return[r.logicalOr(T("a",e,t,n),T("b",e,t,n))];case"Select":case"SelectV2":return[r.where(T("condition",e,t,n),T("a",e,t,n),T("b",e,t,n))];case"BitwiseAnd":return[r.bitwiseAnd(T("a",e,t,n),T("b",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"matrices":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"BatchMatMul":case"BatchMatMulV2":case"MatMul":return[r.matMul(T("a",e,t,n),T("b",e,t,n),T("transposeA",e,t,n),T("transposeB",e,t,n))];case"Einsum":return[r.einsum(T("equation",e,t,n),...T("tensors",e,t,n))];case"Transpose":return[r.transpose(T("x",e,t,n),T("perm",e,t,n))];case"_FusedMatMul":const[s,a]=T("fusedOps",e,t,n),o="biasadd"===s,i="prelu"===a,u=T("numArgs",e,t,n),p=T("leakyreluAlpha",e,t,n);if(o){if(i&&2!==u)throw new Error("Fused MatMul with BiasAdd and Prelu must have two extra arguments: bias and alpha.");if(!i&&1!==u)throw new Error("Fused MatMul with BiasAdd must have one extra argument: bias.")}const[l,c]=T("args",e,t,n);return[r.fused.matMul({a:T("a",e,t,n),b:T("b",e,t,n),transposeA:T("transposeA",e,t,n),transposeB:T("transposeB",e,t,n),bias:l,activation:a,preluActivationWeights:c,leakyreluAlpha:p})];case"MatrixBandPart":return[r.linalg.bandPart(T("a",e,t,n),T("numLower",e,t,n),T("numUpper",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"normalization":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"EuclideanNorm":return[r.euclideanNorm(T("x",e,t,n),T("axis",e,t,n),T("keepDims",e,t,n))];case"FusedBatchNorm":case"FusedBatchNormV2":case"FusedBatchNormV3":return[r.batchNorm(T("x",e,t,n),T("mean",e,t,n),T("variance",e,t,n),T("offset",e,t,n),T("scale",e,t,n),T("epsilon",e,t,n))];case"LRN":return[r.localResponseNormalization(T("x",e,t,n),T("radius",e,t,n),T("bias",e,t,n),T("alpha",e,t,n),T("beta",e,t,n))];case"Softmax":return[r.softmax(T("x",e,t,n))];case"LogSoftmax":return[r.logSoftmax(T("x",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"ragged":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"RaggedGather":{const{outputNestedSplits:s,outputDenseValues:a}=r.raggedGather(T("paramsNestedSplits",e,t,n),T("paramsDenseValues",e,t,n),T("indices",e,t,n),T("outputRaggedRank",e,t,n));return s.concat(a)}case"RaggedRange":{const{rtNestedSplits:s,rtDenseValues:a}=r.raggedRange(T("starts",e,t,n),T("limits",e,t,n),T("splits",e,t,n));return[s,a]}case"RaggedTensorToTensor":return[r.raggedTensorToTensor(T("shape",e,t,n),T("values",e,t,n),T("defaultValue",e,t,n),T("rowPartitionTensors",e,t,n),T("rowPartitionTypes",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"reduction":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Max":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.max(T("x",e,t,n),s,a)]}case"Mean":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.mean(T("x",e,t,n),s,a)]}case"Min":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.min(T("x",e,t,n),s,a)]}case"Sum":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.sum(T("x",e,t,n),s,a)]}case"All":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.all(T("x",e,t,n),s,a)]}case"Any":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.any(T("x",e,t,n),s,a)]}case"ArgMax":{const s=T("axis",e,t,n);return[r.argMax(T("x",e,t,n),s)]}case"ArgMin":{const s=T("axis",e,t,n);return[r.argMin(T("x",e,t,n),s)]}case"Prod":{const s=T("axis",e,t,n),a=T("keepDims",e,t,n);return[r.prod(T("x",e,t,n),s,a)]}case"Cumprod":{const s=T("axis",e,t,n),a=T("exclusive",e,t,n),o=T("reverse",e,t,n);return[r.cumprod(T("x",e,t,n),s,a,o)]}case"Cumsum":{const s=T("axis",e,t,n),a=T("exclusive",e,t,n),o=T("reverse",e,t,n);return[r.cumsum(T("x",e,t,n),s,a,o)]}case"Bincount":const s=T("x",e,t,n),a=T("weights",e,t,n),o=T("size",e,t,n);return[r.bincount(s,a,o)];case"DenseBincount":{const s=T("x",e,t,n),a=T("weights",e,t,n),o=T("size",e,t,n),i=T("binaryOutput",e,t,n);return[r.denseBincount(s,a,o,i)]}default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"slice_join":return o((()=>((e,t,r,s=Fi)=>{switch(e.op){case"ConcatV2":case"Concat":{const n=T("n",e,t,r),a=T("axis",e,t,r);let o=T("tensors",e,t,r);return o=o.slice(0,n),[s.concat(o,a)]}case"Gather":{const n=T("x",e,t,r),a=T("indices",e,t,r);return[s.gather(n,s.cast(a,"int32"),0)]}case"GatherV2":{const n=T("axis",e,t,r),a=T("batchDims",e,t,r),o=T("x",e,t,r),i=T("indices",e,t,r);return[s.gather(o,s.cast(i,"int32"),n,a)]}case"Reverse":{const n=T("dims",e,t,r),a=[];for(let e=0;e<n.length;e++)n[e]&&a.push(e);const o=T("x",e,t,r);return[s.reverse(o,a)]}case"ReverseV2":{const n=T("axis",e,t,r),a=T("x",e,t,r);return[s.reverse(a,n)]}case"Slice":{const n=T("begin",e,t,r),a=T("size",e,t,r);return[s.slice(T("x",e,t,r),n,a)]}case"StridedSlice":{const n=T("begin",e,t,r),a=T("end",e,t,r),o=T("strides",e,t,r),i=T("beginMask",e,t,r),u=T("endMask",e,t,r),p=T("ellipsisMask",e,t,r),l=T("newAxisMask",e,t,r),c=T("shrinkAxisMask",e,t,r),h=T("x",e,t,r);return[s.stridedSlice(h,n,a,o,i,u,p,l,c)]}case"Pack":return l((()=>{const a=T("axis",e,t,r),o=T("tensors",e,t,r),i=o[0].shape,u=s.squeeze(o[0]).shape,p=o.map((e=>{const t=n.arraysEqual(e.shape,i);if(!t&&!n.arraysEqual(s.squeeze(e).shape,u))throw new Error("the input tensors shape does not match");return t?e:s.reshape(e,i)}));return[s.stack(p,a)]}));case"Unpack":{const n=T("axis",e,t,r),a=T("tensor",e,t,r);return s.unstack(a,n)}case"Tile":{const n=T("reps",e,t,r);return[s.tile(T("x",e,t,r),n)]}case"Split":case"SplitV":{const n=T("axis",e,t,r),a=T("numOrSizeSplits",e,t,r),o=T("x",e,t,r);return s.split(o,a,n)}case"ScatterNd":{const n=T("indices",e,t,r),a=T("values",e,t,r),o=T("shape",e,t,r);return[s.scatterND(n,a,o)]}case"GatherNd":{const n=T("x",e,t,r),a=T("indices",e,t,r);return[s.gatherND(n,a)]}case"SparseToDense":{const n=T("sparseIndices",e,t,r),a=T("outputShape",e,t,r),o=T("sparseValues",e,t,r),i=T("defaultValue",e,t,r);return[s.sparseToDense(n,o,a,o.dtype===i.dtype?i:s.cast(i,o.dtype))]}case"TensorScatterUpdate":{const n=T("indices",e,t,r),a=T("values",e,t,r),o=T("tensor",e,t,r);return[s.tensorScatterUpdate(o,n,a)]}default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"sparse":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"SparseFillEmptyRows":{const{outputIndices:s,outputValues:a,emptyRowIndicator:o,reverseIndexMap:i}=r.sparse.sparseFillEmptyRows(T("indices",e,t,n),T("values",e,t,n),T("denseShape",e,t,n),T("defaultValue",e,t,n));return[s,a,o,i]}case"SparseReshape":{const{outputIndices:s,outputShape:a}=r.sparse.sparseReshape(T("inputIndices",e,t,n),T("inputShape",e,t,n),T("newShape",e,t,n));return[s,a]}case"SparseSegmentMean":return[r.sparse.sparseSegmentMean(T("data",e,t,n),T("indices",e,t,n),T("segmentIds",e,t,n))];case"SparseSegmentSum":return[r.sparse.sparseSegmentSum(T("data",e,t,n),T("indices",e,t,n),T("segmentIds",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"spectral":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"FFT":return[r.fft(T("x",e,t,n))];case"IFFT":return[r.ifft(T("x",e,t,n))];case"RFFT":return[r.rfft(T("x",e,t,n))];case"IRFFT":return[r.irfft(T("x",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"string":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"StaticRegexReplace":return[r.string.staticRegexReplace(T("input",e,t,n),T("pattern",e,t,n),T("rewrite",e,t,n),T("replaceGlobal",e,t,n))];case"StringNGrams":{const{nGrams:s,nGramsSplits:a}=r.string.stringNGrams(T("data",e,t,n),T("dataSplits",e,t,n),T("separator",e,t,n),T("nGramWidths",e,t,n),T("leftPad",e,t,n),T("rightPad",e,t,n),T("padWidth",e,t,n),T("preserveShortSequences",e,t,n));return[s,a]}case"StringSplit":{const{indices:s,values:a,shape:o}=r.string.stringSplit(T("input",e,t,n),T("delimiter",e,t,n),T("skipEmpty",e,t,n));return[s,a,o]}case"StringToHashBucketFast":return[r.string.stringToHashBucketFast(T("input",e,t,n),T("numBuckets",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"transformation":return o((()=>((e,t,n,r=Fi)=>{switch(e.op){case"Cast":return[r.cast(T("x",e,t,n),T("dtype",e,t,n))];case"ExpandDims":{const s=T("axis",e,t,n);return[r.expandDims(T("x",e,t,n),s)]}case"Squeeze":{const s=T("axis",e,t,n);return[r.squeeze(T("x",e,t,n),s)]}case"Reshape":return[r.reshape(T("x",e,t,n),T("shape",e,t,n))];case"EnsureShape":return[r.ensureShape(T("x",e,t,n),T("shape",e,t,n))];case"MirrorPad":return[r.mirrorPad(T("x",e,t,n),T("padding",e,t,n),T("mode",e,t,n))];case"PadV2":case"Pad":return[r.pad(T("x",e,t,n),T("padding",e,t,n),T("constantValue",e,t,n))];case"SpaceToBatchND":{const s=T("blockShape",e,t,n),a=T("paddings",e,t,n);return[r.spaceToBatchND(T("x",e,t,n),s,a)]}case"BatchToSpaceND":{const s=T("blockShape",e,t,n),a=T("crops",e,t,n);return[r.batchToSpaceND(T("x",e,t,n),s,a)]}case"DepthToSpace":{const s=T("blockSize",e,t,n),a=T("dataFormat",e,t,n).toUpperCase();return[r.depthToSpace(T("x",e,t,n),s,a)]}case"BroadcastTo":return[r.broadcastTo(T("x",e,t,n),T("shape",e,t,n))];case"BroadcastArgs":return[r.broadcastArgs(T("s0",e,t,n),T("s1",e,t,n))];default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r)));case"hash_table":return(async(e,t,n,r)=>{switch(e.op){case"HashTable":case"HashTableV2":{const s=r.getHashTableHandleByName(e.name);if(null!=s)return[s];{const s=T("keyDType",e,t,n),a=T("valueDType",e,t,n),o=new Wi(s,a);return r.addHashTable(e.name,o),[o.handle]}}case"InitializeTable":case"InitializeTableV2":case"LookupTableImport":case"LookupTableImportV2":{const s=T("tableHandle",e,t,n,r),a=T("keys",e,t,n),o=T("values",e,t,n),i=r.getHashTableById(s.id);return[await i.import(a,o)]}case"LookupTableFind":case"LookupTableFindV2":{const s=T("tableHandle",e,t,n,r),a=T("keys",e,t,n),o=T("defaultValue",e,t,n),i=r.getHashTableById(s.id);return[await i.find(a,o)]}case"LookupTableSize":case"LookupTableSizeV2":{const s=T("tableHandle",e,t,n,r);return[r.getHashTableById(s.id).tensorSize()]}default:throw TypeError(`Node type ${e.op} is not implemented`)}})(e,t,r,a);case"custom":const s=w(e.op);if(s&&s.customExecutor)return s.customExecutor(new ce(e,t,r));throw TypeError(`Custom op ${e.op} is not registered.`);default:throw TypeError(`Unknown op '${e.op}'. File an issue at https://github.com/tensorflow/tfjs/issues so we can add it, or register a custom execution with tf.registerOp()`)}})(t,r,s);return e.util.isPromise(i)?i.then((e=>[].concat(e))):[].concat(i)}class Gi{constructor(e={},t={},n={},r={},s){this.weightMap=e,this.tensorArrayMap=t,this.tensorListMap=n,this.functionMap=r,this.parseNodeNameCache=s,this.rootContext={id:0,frameName:"",iterationId:0},this.contexts=[this.rootContext],this.lastId=0,this.generateCurrentContextIds()}newFrame(e,t){return{id:e,frameName:t,iterationId:0}}set currentContext(e){this.contexts!==e&&(this.contexts=e,this.generateCurrentContextIds())}get currentContext(){return this.contexts}get currentContextId(){return this._currentContextIds[0]}get currentContextIds(){return this._currentContextIds}generateCurrentContextIds(){const e=[];for(let t=0;t<this.contexts.length-1;t++){const n=this.contexts.slice(0,this.contexts.length-t);e.push(this.contextIdforContexts(n))}e.push(""),this._currentContextIds=e}contextIdforContexts(e){return e?e.map((e=>0===e.id&&0===e.iterationId?"":`${e.frameName}-${e.iterationId}`)).join("/"):""}enterFrame(e){this.contexts&&(this.lastId++,this.contexts=this.contexts.slice(),this.contexts.push(this.newFrame(this.lastId,e)),this._currentContextIds.unshift(this.contextIdforContexts(this.contexts)))}exitFrame(){if(!(this.contexts&&this.contexts.length>1))throw new Error("Cannot exit frame, the context is empty");this.contexts=this.contexts.slice(),this.contexts.splice(-1),this.currentContextIds.shift()}nextIteration(){if(!(this.contexts&&this.contexts.length>0))throw new Error("Cannot increase frame iteration, the context is empty");{this.contexts=this.contexts.slice(),this.lastId++;const e=Object.assign({},this.contexts[this.contexts.length-1]);e.iterationId+=1,e.id=this.lastId,this.contexts.splice(-1,1,e),this._currentContextIds.splice(0,1,this.contextIdforContexts(this.contexts))}}getWeight(e){return this.weightMap[e]}addTensorArray(e){this.tensorArrayMap[e.id]=e}getTensorArray(e){return this.tensorArrayMap[e]}addTensorList(e){this.tensorListMap[e.id]=e}getTensorList(e){return this.tensorListMap[e]}dispose(e){for(const t in this.tensorArrayMap)this.tensorArrayMap[t].clearAndClose(e);for(const t in this.tensorListMap)this.tensorListMap[t].clearAndClose(e)}}function Hi(e,t,n,r){const s=new Set,a=[];let o=null,i=null;const u=new Set,p=new Set(Object.keys(e).map((e=>I(e)[0])));r=r||[];const l=new Set(r.map((e=>I(e.name)[0]))),c=[...t];for(;c.length>0;){const e=c.pop();(eu(e)||tu(e)||nu(e))&&null==o&&(o=e,i=o.children.map((e=>e.name)).filter((e=>s.has(e)))),s.add(e.name),null==n[e.name]&&(p.has(e.name)||l.has(e.name)||(0!==e.inputs.length?e.inputs.forEach((e=>{u.has(e.name)||(u.add(e.name),c.push(e))})):a.push(e.name)))}return{inputs:e,outputs:t,usedNodes:s,missingInputs:a,dynamicNode:o,syncInputs:i}}function Zi(e,t){const{usedNodes:n,inputs:r}=t,s=Object.keys(r).map((e=>I(e)[0])).map((t=>e.nodes[t])),a=e.initNodes||[],o=e=>n.has("string"==typeof e?e:e.name);function i(e){return[...new Map(e.map((e=>[e.name,e]))).values()]}const u=i([...s,...e.weights,...a]).filter(o),p=i([...u,...Object.values(e.nodes)]).filter(o),l=new Map(p.map((e=>[e.name,e]))),c={};for(const e of p){c[e.name]=c[e.name]||0;for(const t of e.children)o(t)||(c[t.name]=Number.POSITIVE_INFINITY),c[t.name]=(c[t.name]||0)+1}const h=Object.entries(c).filter((([,e])=>0===e)).map((([e])=>e)),d=[...h];for(;h.length>0;){const e=h.pop(),t=l.get(e);for(const e of t.children.filter(o))0==--c[e.name]&&(d.push(e.name),h.push(e.name))}const m=function(e,t){const n=new Map(e.map((e=>[e.name,e]))),r=t.map((e=>e.name)),s=new Set(r);for(;r.length>0;){const e=r.pop(),t=n.get(e);for(const e of t.children)n.has(e.name)&&!s.has(e.name)&&(s.add(e.name),r.push(e.name))}return e.filter((e=>s.has(e.name)))}(d.map((e=>l.get(e))),u);return function(e,t){const n=new Map(e.map(((e,t)=>[e.name,t]))),r=new Set(t.map((e=>e.name))),s=e=>r.has("string"==typeof e?e:e.name),a=new Set(e.map((e=>e.name))),o=e=>a.has("string"==typeof e?e:e.name);for(const t of e){for(const e of t.children.filter(o)){if(!n.has(e.name))throw new Qi(`Child ${e.name} of node ${t.name} is unreachable.`);if(n.get(t.name)>n.get(e.name))throw new Qi(`Node ${t.name} is scheduled to run after its child ${e.name}.`)}if(!s(t))for(const e of t.inputs){if(!n.has(e.name))throw new Qi(`Input ${e.name} of node ${t.name} is unreachable.`);if(n.get(e.name)>n.get(t.name))throw new Qi(`Node ${t.name} is scheduled to run before its input ${e.name}.`)}}}(m,u),m}class Qi extends Error{constructor(e){super(`NodesExecutionOrderError: ${e}`)}}const Xi=new Set(["Switch","Merge","Enter","Exit","NextIteration","StatelessIf","StatelessWhile","if","While"]),Yi=new Set(["NonMaxSuppressionV2","NonMaxSuppressionV3","NonMaxSuppressionV5","Where"]),Ji=new Set(["HashTable","HashTableV2","LookupTableImport","LookupTableImportV2","LookupTableFind","LookupTableFindV2","LookupTableSize","LookupTableSizeV2"]);function eu(e){return Xi.has(e.op)}function tu(e){return Yi.has(e.op)}function nu(e){return Ji.has(e.op)}class ru{get weightIds(){return this.parent?this.parent.weightIds:this._weightIds}get functionExecutorMap(){return this.parent?this.parent.functionExecutorMap:this._functionExecutorMap}get weightMap(){return this.parent?this.parent.weightMap:this._weightMap}set weightMap(e){const t=Object.keys(e).map((t=>e[t].map((e=>e.id))));this._weightIds=[].concat(...t),this._weightMap=e}set resourceManager(e){this._resourceManager=e}get inputs(){return this._inputs.map((e=>({name:e.name,shape:e.attrParams.shape?e.attrParams.shape.value:void 0,dtype:e.attrParams.dtype?e.attrParams.dtype.value:void 0})))}get outputs(){return this._outputs.map((e=>({name:e.name,shape:e.attrParams.shape?e.attrParams.shape.value:void 0,dtype:e.attrParams.dtype?e.attrParams.dtype.value:void 0})))}get inputNodes(){return this._inputs.map((e=>e.signatureKey||e.name))}get outputNodes(){return this._outputs.map((e=>{const t=e.signatureKey||e.name;return e.defaultOutput?`${t}:${e.defaultOutput}`:t}))}get functions(){return Object.keys(this._functions).reduce(((e,t)=>(e[t]=this._functions[t].signature,e)),{})}constructor(e,t){this.graph=e,this.parent=t,this.compiledMap=new Map,this.parseNodeNameCache=new Map,this._weightMap={},this.SEPARATOR=",",this._functions={},this._functionExecutorMap={},this.keepIntermediateTensors=!1,this._outputs=e.outputs,this._inputs=e.inputs,this._initNodes=e.initNodes,this._signature=e.signature,this._functions=e.functions,null!=e.functions&&Object.keys(e.functions).forEach((t=>{this._functionExecutorMap[t]=new ru(e.functions[t],this)}))}getCompilationKey(e,t){const n=e.map((e=>e.name)).sort(),r=t.map((e=>e.name)).sort();return n.join(this.SEPARATOR)+"--"+r.join(this.SEPARATOR)}compile(e,t){const n=Hi(e,t,this.weightMap,this._initNodes),{missingInputs:r,dynamicNode:s,syncInputs:a}=n;if(null!=s)throw new Error(`This execution contains the node '${s.name}', which has the dynamic op '${s.op}'. Please use model.executeAsync() instead. Alternatively, to avoid the dynamic ops, specify the inputs [${a}]`);if(r.length>0){const n=t.map((e=>e.name)),s=Object.keys(e);throw new Error(`Cannot compute the outputs [${n}] from the provided inputs [${s}]. Missing the following inputs: [${r}]`)}const o=Zi(this.graph,n),i=function(e){const t=new Map(e.map(((e,t)=>[e.name,t]))),n=Number.MAX_SAFE_INTEGER,r=e.map(((e,t)=>eu(e)?n:t)),s=e=>{const n=r[t.get(e.name)];return null==n?-1:n},a=e.map(((e,t)=>e.children.map(s).reduce(((e,t)=>Math.max(e,t)),r[t]))),o=new Map;for(let t=0;t<e.length;++t){const r=a[t];if(r===n)continue;const s=e[t],i=e[r];o.has(i.name)||o.set(i.name,[]),o.get(i.name).push(s)}return o}(o);return{orderedNodes:o,nodeLiveUntilMap:i}}cloneAndKeepTensor(e){if(null==e)return null;const t=e.clone();return a(t),t}cloneTensorList(e){if(!e)return null;const t=e.map((e=>this.cloneAndKeepTensor(e)));return t}cloneTensorMap(e){return Object.fromEntries(Object.entries(e).map((([e,t])=>[e,this.cloneTensorList(t)])))}execute(e,r){this.disposeIntermediateTensors(),e=this.mapInputs(e);const s=Object.keys(e).sort();this.checkInputs(e),this.checkInputShapeAndType(e),r=this.mapOutputs(r),this.checkOutputs(r);const a=s.map((e=>this.graph.nodes[I(e)[0]])),o=r.map((e=>I(e)[0])),i=new Set(o);let u=o.map((e=>this.graph.nodes[e]));0===u.length&&(u=this._outputs);const p=this.getCompilationKey(a,u);let c=this.compiledMap.get(p);null==c&&(c=this.compile(e,u),this.compiledMap.set(p,c));try{this.keepIntermediateTensors=t().getBool("KEEP_INTERMEDIATE_TENSORS")}catch(e){this.keepIntermediateTensors=!1,console.warn(e.message)}const h={},d={};return l((()=>{const t=new Gi(this.weightMap,h,d,this.functionExecutorMap,this.parseNodeNameCache),s=Object.assign({},this.weightMap);this.keepIntermediateTensors&&(this.clonedTensorsMap=this.cloneTensorMap(this.weightMap)),Object.keys(e).forEach((n=>{const[r,a]=I(n,t),o=[];o[a]=e[n],s[r]=o,this.keepIntermediateTensors&&(this.clonedTensorsMap[r]=this.cloneTensorList(o))}));const a=this.getFrozenTensorIds(s),{orderedNodes:o,nodeLiveUntilMap:u}=c;for(const e of o){if(s[e.name])continue;const r=ji(e,s,t,this._resourceManager);if(n.isPromise(r))throw new Error(`The execution of the op '${e.op}' returned a promise. Please use model.executeAsync() instead.`);s[e.name]=r,this.keepIntermediateTensors&&(this.clonedTensorsMap[e.name]=this.cloneTensorList(r)),this.checkTensorForDisposalWithNodeLiveUntilInfo(e,s,t,a,i,u.get(e.name))}return null==this.parent&&t.dispose(a),r.map((e=>v(e,s,t)))}))}getFrozenTensorIds(e){const t=[].concat.apply([],Object.keys(e).map((t=>e[t])).map((e=>e.map((e=>e.id)))));return new Set(t)}checkTensorForDisposal(e,t,n,r,s,a,o){if(!eu(t)&&!a.has(e)){for(const r of n[e])null!=r&&(o[r.id]=(o[r.id]||0)+t.children.length);for(const e of t.inputs){if(eu(e))continue;const t=_(e.name,n,r);if(null!=t)for(const e of t){if(!e||e.kept||s.has(e.id))continue;const t=o[e.id];1===t?(e.dispose(),delete o[e.id]):null!=t&&o[e.id]--}}}}checkTensorForDisposalWithNodeLiveUntilInfo(e,t,n,r,s,a){function o(e){return eu(e)||s.has(e.name)}if(!eu(e)&&null!=a)for(const e of a){if(o(e))continue;const s=_(e.name,t,n);for(const e of s)!e||e.kept||r.has(e.id)||e.dispose()}}async executeAsync(e,t){return this._executeAsync(e,t)}disposeIntermediateTensors(){this.clonedTensorsMap&&(Object.values(this.clonedTensorsMap).forEach((e=>{for(const t of e)t&&!t.isDisposed&&t.dispose()})),this.clonedTensorsMap=null)}getIntermediateTensors(){return this.clonedTensorsMap}async _executeAsync(e,n,r=!1,s={},a={}){this.disposeIntermediateTensors(),r||(e=this.mapInputs(e),this.checkInputs(e),this.checkInputShapeAndType(e),n=this.mapOutputs(n),this.checkOutputs(n));try{this.keepIntermediateTensors=t().getBool("KEEP_INTERMEDIATE_TENSORS")}catch(e){this.keepIntermediateTensors=!1,console.warn(e.message)}const o=new Gi(this.weightMap,s,a,this.functionExecutorMap,this.parseNodeNameCache);this.keepIntermediateTensors&&(this.clonedTensorsMap=this.cloneTensorMap(this.weightMap));const i=await this.executeWithControlFlow(e,o,n,r),u=n.map((e=>v(e,i,o))),p=u.map((e=>e.id)),l=Object.keys(e).map((t=>e[t].id)),c=new Set([...p,...l,...this.weightIds]);return Object.values(i).forEach((e=>{e.forEach((e=>{!e||e.isDisposed||c.has(e.id)||e.dispose()}))})),null==this.parent&&o.dispose(c),u}async executeFunctionAsync(e,t,n){const r=e.reduce(((e,t,n)=>(e[this.inputs[n].name]=t,e)),{});return this._executeAsync(r,this.outputNodes,!0,t,n)}async executeWithControlFlow(e,t,n,r){const s=Object.keys(e),a=s.map((e=>this.graph.nodes[I(e)[0]])),o=n.map((e=>I(e)[0])),i=new Set(o);let u=o.map((e=>this.graph.nodes[e]));0===u.length&&(u=this._outputs);const{usedNodes:p,missingInputs:l,dynamicNode:c,syncInputs:h}=Hi(e,u,this.weightMap,this._initNodes),d=[...a,...this.graph.weights,...this._initNodes||[]].map((e=>({node:e,contexts:t.currentContext}))),m=Object.assign({},this.weightMap);Object.keys(e).forEach((t=>{const[n,r]=I(t),s=[];s[r]=e[t],m[n]=s}));const f={},y=this.getFrozenTensorIds(m),g={};for(;d.length>0;){const e=this.processStack(a,d,t,m,g,y,i,f,p);await Promise.all(e)}null!=c||r||console.warn("This model execution did not contain any nodes with control flow or dynamic output shapes. You can use model.execute() instead.");const b=u.filter((e=>!eu(e)&&!v(e.name,m,t))).map((e=>e.name));if(b.length>0){let e="";throw null!=c&&(e=`Alternatively, to avoid the dynamic ops, use model.execute() and specify the inputs [${h}]`),new Error(`Cannot compute the outputs [${b}] from the provided inputs [${s}]. Consider providing the following inputs: [${l}]. ${e}`)}return m}processStack(e,t,r,s,a,o,i,u,p){const l=[];for(;t.length>0;){const e=t.pop();r.currentContext=e.contexts;let c="";if("Enter"===e.node.op&&T("isConstant",e.node,s,r)&&([c]=S(e.node.name,r)),null==s[e.node.name]){const h=ji(e.node,s,r,this._resourceManager);c||([c]=S(e.node.name,r));const d=r.currentContext;n.isPromise(h)?l.push(h.then((n=>(s[c]=n,this.keepIntermediateTensors&&(this.clonedTensorsMap[c]=this.cloneTensorList(n)),r.currentContext=d,this.checkTensorForDisposal(c,e.node,s,r,o,i,u),this.processChildNodes(e.node,t,r,s,a,p),n)))):(s[c]=h,this.keepIntermediateTensors&&(this.clonedTensorsMap[c]=this.cloneTensorList(h)),this.checkTensorForDisposal(c,e.node,s,r,o,i,u),this.processChildNodes(e.node,t,r,s,a,p))}else this.processChildNodes(e.node,t,r,s,a,p)}return l}processChildNodes(e,t,n,r,s,a){e.children.forEach((e=>{const[o]=S(e.name,n);!s[o]&&a.has(e.name)&&("Merge"===e.op?e.inputNames.some((e=>!!v(e,r,n)))&&(s[o]=!0,t.push({contexts:n.currentContext,node:e})):e.inputNames.every((e=>!!v(e,r,n)))&&(s[o]=!0,t.push({contexts:n.currentContext,node:e})))}))}dispose(){Object.keys(this.weightMap).forEach((e=>this.weightMap[e].forEach((e=>e.dispose()))))}checkInputShapeAndType(e){Object.keys(e).forEach((t=>{const r=e[t],[s]=I(t),a=this.graph.nodes[s];if(a.attrParams.shape&&a.attrParams.shape.value){const e=a.attrParams.shape.value,t=e.length===r.shape.length&&r.shape.every(((t,n)=>-1===e[n]||e[n]===t));n.assert(t,(()=>`The shape of dict['${a.name}'] provided in model.execute(dict) must be [${e}], but was [${r.shape}]`))}a.attrParams.dtype&&a.attrParams.dtype.value&&n.assert(r.dtype===a.attrParams.dtype.value,(()=>`The dtype of dict['${a.name}'] provided in model.execute(dict) must be ${a.attrParams.dtype.value}, but was ${r.dtype}`))}))}mapInputs(e){var t,n;const r={};for(const s in e){const a=null===(n=null===(t=this._signature)||void 0===t?void 0:t.inputs)||void 0===n?void 0:n[s];null!=a?r[a.name]=e[s]:r[s]=e[s]}return r}checkInputs(e){const t=Object.keys(e).filter((e=>{const[t]=I(e);return null==this.graph.nodes[t]}));if(t.length>0)throw new Error(`The dict provided in model.execute(dict) has keys: [${t}] that are not part of graph`)}mapOutputs(e){return e.map((e=>{var t,n;const r=null===(n=null===(t=this._signature)||void 0===t?void 0:t.outputs)||void 0===n?void 0:n[e];return null!=r?r.name:e}),{})}checkOutputs(e){e.forEach((e=>{const[t]=I(e);if(!this.graph.nodes[t])throw new Error(`The output '${e}' is not found in the graph`)}))}}class su{constructor(e={},t={}){this.hashTableNameToHandle=e,this.hashTableMap=t}addHashTable(e,t){this.hashTableNameToHandle[e]=t.handle,this.hashTableMap[t.id]=t}getHashTableHandleByName(e){return this.hashTableNameToHandle[e]}getHashTableById(e){return this.hashTableMap[e]}dispose(){for(const e in this.hashTableMap)this.hashTableMap[e].clearAndClose(),delete this.hashTableMap[e];for(const e in this.hashTableNameToHandle)this.hashTableNameToHandle[e].dispose(),delete this.hashTableNameToHandle[e]}}const au={float32:4,float16:2,int32:4,uint16:2,uint8:1,bool:1,complex64:8};async function ou(e,t){const n=ye(e.shape);let r;if("quantization"in e){const t=e.quantization;r=au[t.dtype]}else{if("string"===e.dtype){let e=0;for(let r=0;r<n;r++)e+=4+new Uint32Array(await t(e,e+4))[0];return e}r=au[e.dtype]}return n*r}function iu(e,t){const n=e.name,r=e.dtype,s=e.shape,a=ye(s);let o,i=0;if("quantization"in e){const s=e.quantization;if("uint8"===s.dtype||"uint16"===s.dtype){if(!("min"in s)||!("scale"in s))throw new Error(`Weight ${e.name} with quantization ${s.dtype} doesn't have corresponding metadata min and scale.`)}else{if("float16"!==s.dtype)throw new Error(`Weight ${e.name} has unknown quantization dtype ${s.dtype}. Supported quantization dtypes are: 'uint8', 'uint16', and 'float16'.`);if("float32"!==r)throw new Error(`Weight ${e.name} is quantized with ${s.dtype} which only supports weights of type float32 not ${r}.`)}const u=au[s.dtype],p="uint8"===s.dtype?new Uint8Array(t):new Uint16Array(t);if("float32"===r)if("uint8"===s.dtype||"uint16"===s.dtype){o=new Float32Array(p.length);for(let e=0;e<p.length;e++){const t=p[e];o[e]=t*s.scale+s.min}}else{if("float16"!==s.dtype)throw new Error(`Unsupported quantization type ${s.dtype} for weight type float32.`);{const e=function(){const e=function(){const e=e=>{let t=e<<13,n=0;for(;0==(8388608&t);)n-=8388608,t<<=1;return t&=-8388609,n+=947912704,t|n},t=new Uint32Array(2048);t[0]=0;for(let n=1;n<1024;n++)t[n]=e(n);for(let e=1024;e<2048;e++)t[e]=939524096+(e-1024<<13);return t}(),t=function(){const e=new Uint32Array(64);e[0]=0,e[31]=1199570944,e[32]=2147483648,e[63]=3347054592;for(let t=1;t<31;t++)e[t]=t<<23;for(let t=33;t<63;t++)e[t]=2147483648+(t-32<<23);return e}(),n=function(){const e=new Uint32Array(64);for(let t=0;t<64;t++)e[t]=1024;return e[0]=e[32]=0,e}();return r=>{const s=new ArrayBuffer(4*r.length),a=new Uint32Array(s);for(let s=0;s<r.length;s++){const o=r[s],i=e[n[o>>10]+(1023&o)]+t[o>>10];a[s]=i}return new Float32Array(s)}}();o=e(p)}}else{if("int32"!==r)throw new Error(`Unsupported dtype in weight '${n}': ${r}`);if("uint8"!==s.dtype&&"uint16"!==s.dtype)throw new Error(`Unsupported quantization type ${s.dtype} for weight type int32.`);o=new Int32Array(p.length);for(let e=0;e<p.length;e++){const t=p[e];o[e]=Math.round(t*s.scale+s.min)}}i+=a*u}else if("string"===r){const n=ye(e.shape);o=[];for(let e=0;e<n;e++){const e=new Uint32Array(t.slice(i,i+4))[0];i+=4;const n=new Uint8Array(t.slice(i,i+e));o.push(n),i+=e}}else{const e=au[r];if("float32"===r)o=new Float32Array(t);else if("int32"===r)o=new Int32Array(t);else{if("bool"!==r){if("complex64"===r){o=new Float32Array(t);const e=new Float32Array(o.length/2),n=new Float32Array(o.length/2);for(let t=0;t<e.length;t++)e[t]=o[2*t],n[t]=o[2*t+1];const r=ho(e,s,"float32"),a=ho(n,s,"float32"),i=rr(r,a);return r.dispose(),a.dispose(),i}throw new Error(`Unsupported dtype in weight '${n}': ${r}`)}o=new Uint8Array(t)}i+=a*e}return ho(o,s,r)}async function uu(e,t,n){let r=new Uint8Array(t);for(;r.byteLength<n;){const{done:t,value:s}=await e.read();if(t&&null==s){const e=n-r.byteLength;throw new Error(`Reader is done but ${e} bytes are still expected`)}const a=new Uint8Array(r.length+s.byteLength);a.set(r,0),a.set(new Uint8Array(s),r.length),r=a}return r.buffer}class pu{get modelVersion(){return this.version}get inputNodes(){return this.executor.inputNodes}get outputNodes(){return this.executor.outputNodes}get inputs(){return this.executor.inputs}get outputs(){return this.executor.outputs}get weights(){return this.executor.weightMap}get metadata(){return this.artifacts.userDefinedMetadata}get modelSignature(){return this.signature}get modelStructuredOutputKeys(){return this.structuredOutputKeys}constructor(e,t={},n=d){this.modelUrl=e,this.loadOptions=t,this.version="n/a",this.io=n,null==t&&(this.loadOptions={}),this.resourceManager=new su}findIOHandler(){const e=this.modelUrl;if(null!=e.load)this.handler=e;else if(null!=this.loadOptions.requestInit)this.handler=this.io.browserHTTPRequest(e,this.loadOptions);else{const t=this.io.getLoadHandlers(e,this.loadOptions);if(0===t.length)t.push(this.io.browserHTTPRequest(e,this.loadOptions));else if(t.length>1)throw new Error(`Found more than one (${t.length}) load handlers for URL '${[e]}'`);this.handler=t[0]}}load(){if(this.findIOHandler(),null==this.handler.load)throw new Error("Cannot proceed with model loading because the IOHandler provided does not have the `load` method implemented.");const e=this.handler.load();return n.isPromise(e)?e.then((e=>null==e.getWeightStream?this.loadSync(e):this.loadStreaming(e))):this.loadSync(e)}loadSync(e){const t=this.io.decodeWeights(e.weightData,e.weightSpecs);return this.loadWithWeightMap(e,t)}async loadStreaming(e){if(null==e.getWeightStream)throw new Error("Model artifacts missing streamWeights function");const t=await async function(e,t){const n={},r=e.getReader();let s=new ArrayBuffer(0);for(const e of t){const t=await ou(e,(async(e,t)=>(s=await uu(r,s,t),s.slice(e,t))));s=await uu(r,s,t);const a=s.slice(0,t);s=s.slice(t);const o=iu(e,a);if(n[e.name]=o,"webgpu"===tn.backendName){const e=tn.backend;"uploadToGPU"in e&&ye(o.shape)>=Fe().get("WEBGPU_CPU_HANDOFF_SIZE_THRESHOLD")&&e.uploadToGPU(o.dataId)}}return n}(e.getWeightStream(),e.weightSpecs);return this.loadWithWeightMap(e,t)}loadWithWeightMap(e,t){this.artifacts=e;const n=this.artifacts.modelTopology;let r=this.artifacts.signature;if(null!=this.artifacts.userDefinedMetadata){const e=this.artifacts.userDefinedMetadata;null!=e.signature&&(r=e.signature),null!=e.structuredOutputKeys&&(this.structuredOutputKeys=e.structuredOutputKeys)}if(this.signature=r,this.version=`${n.versions.producer}.${n.versions.minConsumer}`,this.executor=new ru(Q.Instance.transformGraph(n,this.signature)),this.executor.weightMap=this.convertTensorMapToTensorsMap(t),this.executor.resourceManager=this.resourceManager,null!=e.modelInitializer&&null!=e.modelInitializer.node){const t=Q.Instance.transformGraph(e.modelInitializer);this.initializer=new ru(t),this.initializer.weightMap=this.executor.weightMap,this.initializer.resourceManager=this.resourceManager,this.initializerSignature=e.initializerSignature}return!0}async save(e,t){if("string"==typeof e){const t=this.io.getSaveHandlers(e);if(0===t.length)throw new Error(`Cannot find any save handlers for URL '${e}'`);if(t.length>1)throw new Error(`Found more than one (${t.length}) save handlers for URL '${e}'`);e=t[0]}if(null==e.save)throw new Error("GraphModel.save() cannot proceed because the IOHandler provided does not have the `save` attribute defined.");return e.save(this.artifacts)}addStructuredOutputNames(e){if(this.structuredOutputKeys){const t={};return(e instanceof m?[e]:e).forEach(((e,n)=>t[this.structuredOutputKeys[n]]=e)),t}return e}predict(e,t){const n=this.execute(e,this.outputNodes);return this.addStructuredOutputNames(n)}async predictAsync(e,t){const n=await this.executeAsync(e,this.outputNodes);return this.addStructuredOutputNames(n)}normalizeInputs(e){var t;if(!(e instanceof m||Array.isArray(e))){const n=null===(t=this.signature)||void 0===t?void 0:t.inputs;if(null!=n)for(const t in n){const r=n[t];null!=r.resourceId&&(e[t]=this.resourceIdToCapturedInput[r.resourceId])}return e}e=Array.isArray(e)?e:[e];const n=Object.keys(this.resourceIdToCapturedInput).length;if(e.length+n!==this.inputNodes.length)throw new Error(`Input tensor count mismatch, the graph model has ${this.inputNodes.length-n} non-resource placeholders, while there are ${e.length} input tensors provided.`);let r=0;return this.inputNodes.reduce(((t,n)=>{var s,a,o;const i=null===(o=null===(a=null===(s=this.signature)||void 0===s?void 0:s.inputs)||void 0===a?void 0:a[n])||void 0===o?void 0:o.resourceId;return t[n]=null!=i?this.resourceIdToCapturedInput[i]:e[r++],t}),{})}normalizeOutputs(e){return e=e||this.outputNodes,Array.isArray(e)?e:[e]}executeInitializerGraph(){return null==this.initializer?[]:null==this.initializerSignature?this.initializer.execute({},[]):this.initializer.execute({},Object.keys(this.initializerSignature.outputs))}async executeInitializerGraphAsync(){return null==this.initializer?[]:null==this.initializerSignature?this.initializer.executeAsync({},[]):this.initializer.executeAsync({},Object.keys(this.initializerSignature.outputs))}setResourceIdToCapturedInput(e){if(this.resourceIdToCapturedInput={},this.initializerSignature){const t=this.initializerSignature.outputs,n=Object.keys(t);for(let r=0;r<n.length;r++){const s=t[n[r]];this.resourceIdToCapturedInput[s.resourceId]=e[r]}}}execute(e,t){null==this.resourceIdToCapturedInput&&this.setResourceIdToCapturedInput(this.executeInitializerGraph()),e=this.normalizeInputs(e),t=this.normalizeOutputs(t);const n=this.executor.execute(e,t);return n.length>1?n:n[0]}async executeAsync(e,t){null==this.resourceIdToCapturedInput&&this.setResourceIdToCapturedInput(await this.executeInitializerGraphAsync()),e=this.normalizeInputs(e),t=this.normalizeOutputs(t);const n=await this.executor.executeAsync(e,t);return n.length>1?n:n[0]}getIntermediateTensors(){return this.executor.getIntermediateTensors()}disposeIntermediateTensors(){this.executor.disposeIntermediateTensors()}convertTensorMapToTensorsMap(e){return Object.keys(e).reduce(((t,n)=>(t[n]=[e[n]],t)),{})}dispose(){this.executor.dispose(),this.initializer&&(this.initializer.dispose(),this.resourceIdToCapturedInput&&f(this.resourceIdToCapturedInput)),this.resourceManager.dispose()}}async function lu(e,t={},n=d){if(null==e)throw new Error("modelUrl in loadGraphModel() cannot be null. Please provide a url or an IOHandler that loads the model");null==t&&(t={}),t.fromTFHub&&"string"==typeof e&&(e=function(e){e.endsWith("/")||(e+="/");return`${e}model.json?tfjs-format=file`}(e));const r=new pu(e,t,n);return await r.load(),r}function cu(e){if(null==e)throw new Error("modelUrl in loadGraphModelSync() cannot be null. Please provide model artifacts or an IOHandler that loads the model");let t;if(e instanceof Array){const[n,r]=e;if(!n)throw new Error("modelJSON must be the first element of the array");if(!(r&&r instanceof ArrayBuffer))throw new Error("An ArrayBuffer of weights must be the second element of the array");if(!("modelTopology"in n))throw new Error("Model JSON is missing 'modelTopology'");if(!("weightsManifest"in n))throw new Error("Model JSON is missing 'weightsManifest'");const s=d.getWeightSpecs(n.weightsManifest),a=d.getModelArtifactsForJSONSync(n,s,r);t=d.fromMemorySync(a)}else if("load"in e)t=e;else{if(!("modelTopology"in e&&"weightSpecs"in e&&"weightData"in e))throw new Error("Unknown model format");t=d.fromMemorySync(e)}const n=new pu(t);return n.load(),n}const hu="4.22.0";export{pu as GraphModel,k as deregisterOp,lu as loadGraphModel,cu as loadGraphModelSync,N as registerOp,hu as version_converter};
//# sourceMappingURL=tf-converter.fesm.min.js.map
