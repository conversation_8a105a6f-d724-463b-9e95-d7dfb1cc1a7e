export declare function arraysToFloat32Arrays(arrays: number[][]): Float32Array[];
export declare function inputOutputArraysToFloat32Arrays(input: number[][], output: number[][]): Float32Array[];
export declare function arrayToFloat32Arrays(array: number[]): Float32Array[];
export declare function inputOutputArrayToFloat32Arrays(input: number[], output: number[]): Float32Array[];
export declare function arrayToFloat32Array(array: number[]): Float32Array;
export declare function objectsToFloat32Arrays(objects: Array<Record<string, number>>, table: Record<string, number>, length: number): Float32Array[];
export declare function inputOutputObjectsToFloat32Arrays(input: Array<Record<string, number>>, output: Array<Record<string, number>>, inputTable: Record<string, number>, outputTable: Record<string, number>, inputLength: number, outputLength: number): Float32Array[];
export declare function objectToFloat32Arrays(object: Record<string, number>): Float32Array[];
export declare function inputOutputObjectToFloat32Arrays(input: Record<string, number>, output: Record<string, number>): Float32Array[];
export declare function objectToFloat32Array(object: Record<string, number>, table: Record<string, number>, length: number): Float32Array;
//# sourceMappingURL=cast.d.ts.map