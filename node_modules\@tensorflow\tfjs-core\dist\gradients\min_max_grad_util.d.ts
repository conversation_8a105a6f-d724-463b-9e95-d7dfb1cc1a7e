/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/gradients/min_max_grad_util" />
import { Tensor } from '../tensor';
/**
 * Gradient helper function for the min and max operations.
 */
export declare function gradForMinAndMax<T extends Tensor>(dy: T, y: T, xOrig: Tensor, origAxes: number[]): {
    x: () => Tensor<import("@tensorflow/tfjs-core/dist/types").Rank>;
};
