{"version": 3, "file": "rnn.d.ts", "sourceRoot": "", "sources": ["../../src/recurrent/rnn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,GAAG,EAAE,MAAM,iBAAiB,CAAC;AACtC,OAAO,EAAE,0BAA0B,EAAE,MAAM,mBAAmB,CAAC;AAC/D,OAAO,EAEL,cAAc,EACd,kBAAkB,EACnB,MAAM,6BAA6B,CAAC;AAGrC,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAE/C,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAGtD,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,kBAAkB,CAAC;AAEpD,MAAM,WAAW,SAAS;IACxB,aAAa,EAAE,OAAO,CAAC;IACvB,KAAK,EAAE,MAAM,CAAC;IACd,YAAY,EAAE,oBAAoB,EAAE,CAAC;IACrC,MAAM,EAAE,MAAM,CAAC;IACf,SAAS,EAAE,QAAQ,EAAE,CAAC;IACtB,WAAW,EAAE,MAAM,EAAE,CAAC;IACtB,mBAAmB,EAAE,MAAM,EAAE,EAAE,CAAC;IAChC,eAAe,EAAE,YAAY,GAAG,MAAM,CAAC;CACxC;AAED,MAAM,WAAW,WAAW;IAC1B,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,aAAa,EAAE,cAAc,CAAC;IAC9B,IAAI,CAAC,EAAE,QAAQ,CAAC;CACjB;AAED,MAAM,WAAW,eAAe;IAC9B,SAAS,EAAE,MAAM,CAAC;IAClB,UAAU,EAAE,MAAM,CAAC;IACnB,YAAY,EAAE,MAAM,EAAE,CAAC;IACvB,UAAU,EAAE,MAAM,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;IAClB,SAAS,EAAE,MAAM,CAAC;IAClB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,MAAM,CAAC;IAChB,mBAAmB,EAAE,MAAM,CAAC;IAC5B,aAAa,EAAE,kBAAkB,CAAC;CACnC;AAED,MAAM,WAAW,mBAAmB;IAClC,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;IAC1C,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,KAAK,IAAI,CAAC;IACxC,cAAc,EAAE,MAAM,CAAC;IACvB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,oBAAoB;IACnC,UAAU,EAAE,MAAM,CAAC;IACnB,WAAW,EAAE,MAAM,CAAC;IACpB,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;IAC1C,SAAS,EAAE,MAAM,CAAC;IAClB,YAAY,EAAE,MAAM,CAAC;IACrB,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,UAAU,KAAK,IAAI,CAAC;IACxC,cAAc,EAAE,MAAM,CAAC;IACvB,OAAO,EAAE,MAAM,GAAG,UAAU,CAAC;CAC9B;AAED,eAAO,MAAM,aAAa,EAAE,mBAQ3B,CAAC;AAEF,MAAM,WAAW,eAAe;IAC9B,CAAC,GAAG,EAAE,MAAM,GAAG,YAAY,GAAG,MAAM,CAAC;CACtC;AAED,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IAE3D,MAAM,EAAE,YAAY,CAAC;IAErB,UAAU,EAAE,YAAY,CAAC;IAEzB,IAAI,EAAE,MAAM,CAAC;CACd;AAED,eAAO,MAAM,QAAQ,QAAO,WAa3B,CAAC;AAEF,MAAM,WAAW,UAAU;IACzB,UAAU,EAAE,MAAM,CAAC;IACnB,KAAK,EAAE,MAAM,CAAC;CACf;AAED,MAAM,WAAW,uBAAuB;IACtC,MAAM,EAAE,UAAU,CAAC;IACnB,YAAY,EAAE,MAAM,EAAE,EAAE,CAAC;IACzB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,qBAAa,GAAG;IACd,OAAO,EAAE,WAAW,CAAqB;IACzC,SAAS,EAAE,mBAAmB,CAAwB;IACtD,SAAS,EAAE;QAAE,CAAC,KAAK,EAAE,MAAM,GAAG,YAAY,CAAA;KAAE,CAAM;IAClD,IAAI,SAAK;IACT,YAAY,SAAK;IACjB,KAAK,EAAE,SAAS,CASb;IAEH,kBAAkB,EAAE,MAAM,EAAE,CAAM;gBAEtB,OAAO,GAAE,OAAO,CAAC,WAAW,GAAG,mBAAmB,CAAM;IAYpE,UAAU,IAAI,IAAI;IASlB,kBAAkB,IAAI,eAAe,EAAE;IAgBvC,cAAc,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,eAAe;IAWrE,WAAW,CACT,QAAQ,EAAE,QAAQ,EAClB,WAAW,EAAE,MAAM,EACnB,cAAc,EAAE,MAAM,EACtB,WAAW,EAAE,eAAe,GAC3B,MAAM;IAmBT,iBAAiB,IAAI,YAAY;IAWjC,oBAAoB,IAAI;QAAE,eAAe,EAAE,YAAY,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE;IAczE,YAAY,IAAI,IAAI;IAuCpB,QAAQ,IAAI,SAAS;IAmCrB,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM;IA0BnC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,IAAI;IAWpC,aAAa,IAAI,IAAI;IAoCrB,IAAI,UAAU,IAAI,OAAO,CAOxB;IAED,aAAa,IAAI,IAAI;IAMrB,GAAG,CAAC,QAAQ,GAAE,KAAU,EAAE,SAAS,UAAQ,EAAE,WAAW,SAAI,GAAG,MAAM;IA4ErE;;;;OAIG;IACH,mBAAmB,IAAI,IAAI;IAM3B;;;;;;;;OAQG;IACH,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,mBAAmB,CAAC,GAAG,IAAI;IAQlE,uBAAuB,CAAC,OAAO,EAAE,0BAA0B,GAAG,IAAI;IA6ClE,YAAY,CAAC,GAAG,EAAE,GAAG,GAAG,SAAS,GAAG,OAAO,GAAG,IAAI;IAUlD,SAAS,CAAC,YAAY,CACpB,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,EAC9B,OAAO,EAAE,OAAO,CAAC,mBAAmB,CAAC,GACpC,uBAAuB;IAmB1B,KAAK,CACH,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,EAC9B,SAAS,GAAE,OAAO,CAAC,mBAAmB,CAAM,GAC3C,UAAU;IA0Db,SAAS,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;IAE5B,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,EAAE,EAAE;IAQrC,MAAM,IAAI,QAAQ;IA8BlB,QAAQ,CAAC,IAAI,EAAE,QAAQ,GAAG,IAAI;IAoD9B,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK,MAAM,GAAG,WAAW;IA2NrD,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE,YAAY,CAAC,EAAE,OAAO,GAAG,MAAM;CAU9D;AAED,MAAM,WAAW,QAAQ;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,OAAO,EAAE,eAAe,CAAC;IACzB,SAAS,EAAE,oBAAoB,CAAC;IAChC,KAAK,EAAE,WAAW,CAAC;IACnB,YAAY,EAAE,KAAK,CAAC;QAAE,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,CAAA;KAAE,CAAC,CAAC;IACtD,eAAe,EAAE,WAAW,CAAC;IAC7B,MAAM,EAAE,WAAW,CAAC;CACrB;AAED,wBAAgB,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAEtC;AAED,oBAAY,WAAW,GAAG,CACxB,QAAQ,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,MAAM,EAC5C,SAAS,CAAC,EAAE,OAAO,EACnB,WAAW,CAAC,EAAE,MAAM,KACjB,MAAM,CAAC"}