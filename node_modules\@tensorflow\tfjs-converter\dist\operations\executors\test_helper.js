/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
export function createNumberAttr(value) {
    return { value, type: 'number' };
}
export function createNumberAttrFromIndex(inputIndex) {
    return { inputIndexStart: inputIndex, type: 'number' };
}
export function createStrAttr(str) {
    return { value: str, type: 'string' };
}
export function createStrArrayAttr(strs) {
    return { value: strs, type: 'string[]' };
}
export function createBoolAttr(value) {
    return { value, type: 'bool' };
}
export function createTensorShapeAttr(value) {
    return { value, type: 'shape' };
}
export function createShapeAttrFromIndex(inputIndex) {
    return { inputIndexStart: inputIndex, type: 'shape' };
}
export function createNumericArrayAttr(value) {
    return { value, type: 'number[]' };
}
export function createNumericArrayAttrFromIndex(inputIndex) {
    return { inputIndexStart: inputIndex, type: 'number[]' };
}
export function createBooleanArrayAttrFromIndex(inputIndex) {
    return { inputIndexStart: inputIndex, type: 'bool[]' };
}
export function createTensorAttr(index) {
    return { inputIndexStart: index, type: 'tensor' };
}
export function createTensorsAttr(index, paramLength) {
    return { inputIndexStart: index, inputIndexEnd: paramLength, type: 'tensors' };
}
export function createDtypeAttr(dtype) {
    return { value: dtype, type: 'dtype' };
}
export function validateParam(node, opMappers, tfOpName) {
    const opMapper = tfOpName != null ?
        opMappers.find(mapper => mapper.tfOpName === tfOpName) :
        opMappers.find(mapper => mapper.tfOpName === node.op);
    const matched = Object.keys(node.inputParams).every(key => {
        const value = node.inputParams[key];
        const def = opMapper.inputs.find(param => param.name === key);
        return def && def.type === value.type &&
            def.start === value.inputIndexStart && def.end === value.inputIndexEnd;
    }) &&
        Object.keys(node.attrParams).every(key => {
            const value = node.attrParams[key];
            const def = opMapper.attrs.find(param => param.name === key);
            return def && def.type === value.type;
        });
    if (!matched) {
        console.log('node = ', node);
        console.log('opMapper = ', opMapper);
    }
    return matched;
}
export function uncapitalize(name) {
    return name.charAt(0).toLowerCase() + name.slice(1);
}
//# sourceMappingURL=data:application/json;base64,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