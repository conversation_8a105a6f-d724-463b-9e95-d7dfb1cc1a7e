{"version": 3, "file": "feed-forward.d.ts", "sourceRoot": "", "sources": ["../src/feed-forward.ts"], "names": [], "mappings": "AAAA,OAAO,EAAuB,YAAY,EAAW,MAAM,QAAQ,CAAC;AACpE,OAAO,EAAE,gBAAgB,EAAE,MAAM,gCAAgC,CAAC;AAClE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,SAAS,CAAC;AAE7C,OAAO,EAAE,gBAAgB,EAAgB,WAAW,EAAU,MAAM,UAAU,CAAC;AAE/E,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAOhE,MAAM,WAAW,wBAAwB,CACvC,SAAS,SAAS,gBAAgB,GAAG,YAAY,GAAG,MAAM,EAAE,GAAG,YAAY,EAC3E,UAAU,SAAS,gBAAgB,GAAG,YAAY,GAAG,MAAM,EAAE,GAAG,YAAY;IAE5E,KAAK,EAAE,SAAS,CAAC;IACjB,MAAM,EAAE,UAAU,CAAC;CACpB;AAED,MAAM,WAAW,kCAAkC;IACjD,KAAK,EAAE,YAAY,CAAC;IACpB,MAAM,EAAE,YAAY,CAAC;CACtB;AAED,MAAM,WAAW,2BAA2B;IAC1C,KAAK,EAAE,YAAY,CAAC;IACpB,MAAM,EAAE,YAAY,CAAC;CACtB;AAED,MAAM,WAAW,eAAe;IAC9B,UAAU,EAAE,MAAM,CAAC;IACnB,KAAK,EAAE,MAAM,CAAC;CACf;AAED,oBAAY,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAC;AAC3C,oBAAY,mBAAmB,GAAG,CAAC,MAAM,EAAE,eAAe,KAAK,IAAI,CAAC;AAEpE,MAAM,WAAW,2BAA2B;IAC1C,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,GAAG,CAAC,EAAE,OAAO,GAAG,GAAG,CAAC;IACpB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,QAAQ,CAAC,EAAE,mBAAmB,CAAC;IAC/B,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED,MAAM,WAAW,mBAAmB;IAClC,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,KAAK,MAAM,CAAC,CAAC;IACzE,UAAU,CAAC,EAAE,MAAM,MAAM,CAAC;IAC1B,WAAW,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,MAAM,CAAC;IAC5D,UAAU,CAAC,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;IACtC,UAAU,CAAC,EAAE,CACX,aAAa,EAAE,MAAM,EACrB,QAAQ,EAAE,OAAO,CAAC,eAAe,CAAC,KAC/B,OAAO,CAAC;IACb,MAAM,CAAC,EAAE,OAAO,CAAC;IAGjB,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAClB,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC;CAClB;AAED,MAAM,WAAW,+BAA+B;IAC9C,MAAM,EAAE,eAAe,CAAC;IACxB,YAAY,EAAE,2BAA2B,EAAE,CAAC;IAC5C,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,eAAO,MAAM,QAAQ,EAAE,mBAWtB,CAAC;AAEF,eAAO,MAAM,aAAa,EAAE,2BAS3B,CAAC;AAEF,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,EAAE,CAAC;IAChB,MAAM,EAAE,UAAU,EAAE,CAAC;IACrB,eAAe,EAAE,MAAM,CAAC;IACxB,gBAAgB,EAAE,MAAM,CAAC;CAC1B;AAED,qBAAa,WAAW,CACtB,SAAS,SAAS,gBAAgB,GAAG,YAAY,GAAG,MAAM,EAAE,GAAG,YAAY,EAC3E,UAAU,SAAS,gBAAgB,GAAG,YAAY,GAAG,MAAM,EAAE,GAAG,YAAY;IAE5E,MAAM,CAAC,wBAAwB,CAC7B,OAAO,EAAE,OAAO,CAAC,2BAA2B,CAAC,GAC5C,IAAI;IAyCP;;;OAGG;IACH,aAAa,CAAC,GAAG,EAAE,GAAG,GAAG,SAAS,GAAG,OAAO,GAAG,IAAI;IAWnD,sBAAsB,CAAC,IAAI,EAAE,OAAO,CAAC,2BAA2B,CAAC,GAAG,IAAI;IAgBxE,SAAS,EAAE,OAAO,CAAC,2BAA2B,CAAC,CAAM;IACrD,OAAO,EAAE,mBAAmB,CAAC;IAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAQ;IAC/B,WAAW,EAAE,MAAM,GAAG,IAAI,CAAQ;IAClC,aAAa,EAAE,MAAM,EAAE,GAAG,IAAI,CAAQ;IACtC,YAAY,EAAE,MAAM,GAAG,IAAI,CAAQ;IACnC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAQ;IAC/B,gBAAgB,EAAE,gBAAgB,GAAG,IAAI,CAAQ;IACjD,WAAW,EAAE,WAAW,GAAG,IAAI,CAAQ;IACvC,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAQ;IACxC,YAAY,EAAE,WAAW,GAAG,IAAI,CAAQ;IACxC,kBAAkB,EAAE,MAAM,GAAG,IAAI,CAAQ;gBAC7B,OAAO,GAAE,mBAAwB;IAQ7C,qBAAqB,IAAI,MAAM,EAAE;IAwBjC,iBAAiB,IAAI,MAAM,EAAE;IAkB7B,oBAAoB,CAAC,aAAa,EAAE,MAAM,GAAG,MAAM,EAAE;IAiBrD,UAAU,IAAI,IAAI;IAQlB,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI;IAyBxC,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,UAAU;IA8BjC,QAAQ,CAAC,KAAK,EAAE,YAAY,GAAG,YAAY;IAS3C,KAAK,CACH,IAAI,EAAE,KAAK,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,EAC5D,OAAO,GAAE,OAAO,CAAC,2BAA2B,CAAM,GACjD,eAAe;IAiBZ,UAAU,CACd,IAAI,EAAE,KAAK,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,EAC5D,OAAO,GAAE,OAAO,CAAC,2BAA2B,CAAM,GACjD,OAAO,CAAC,eAAe,CAAC;IA6B3B,aAAa,CACX,MAAM,EAAE,eAAe,EACvB,OAAO,EAAE,MAAM,EACf,cAAc,EAAE,MAAM,MAAM,EAC5B,aAAa,EAAE,MAAM,IAAI,GACxB,OAAO;IAsCV,aAAa,CACX,IAAI,EAAE,KAAK,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,EAC5D,OAAO,EAAE,OAAO,CAAC,2BAA2B,CAAC,GAC5C,+BAA+B;IAsBlC,mBAAmB,IAAI,IAAI;IAM3B,uBAAuB,CAAC,YAAY,EAAE,2BAA2B,EAAE,GAAG,MAAM;IAwB5E;;;OAGG;IACH,cAAc,CAAC,IAAI,EAAE,2BAA2B,EAAE,GAAG,IAAI;IAMzD,aAAa,CACX,KAAK,EAAE,YAAY,EACnB,MAAM,EAAE,YAAY,EACpB,YAAY,EAAE,OAAO,GACpB,YAAY,GAAG,IAAI;IAmBtB,gBAAgB,CAAC,MAAM,EAAE,YAAY,GAAG,IAAI;IAO5C;;OAEG;IACH,aAAa,IAAI,IAAI;IAOrB;;;;OAIG;IACH,UAAU,CACR,IAAI,EACA,KAAK,CAAC,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,GACtD,wBAAwB,CAAC,SAAS,EAAE,UAAU,CAAC,GAClD,kCAAkC,EAAE;IA+DvC,YAAY,CACV,aAAa,EAAE,kCAAkC,EAAE,GAClD,2BAA2B,EAAE;IA+BhC;;;;;;;;;OASG;IACH,IAAI,IAAI,IAAI;IAIZ;;OAEG;IACH,MAAM,IAAI,gBAAgB;IAgD1B,MAAM,CAAC,QAAQ,CACb,IAAI,EAAE,gBAAgB,EACtB,QAAQ,CAAC,EAAE,CACT,SAAS,EAAE,UAAU,EACrB,WAAW,CAAC,EAAE,MAAM,EACpB,WAAW,CAAC,EAAE,MAAM,KACjB,MAAM,GACV,WAAW;IAsEd;;;OAGG;IACH,UAAU,IAAI,IAAI;CAKnB"}