{"name": "cli-color", "version": "1.4.0", "description": "Colors, formatting and other tools for the console", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["ansi", "color", "console", "terminal", "cli", "shell", "log", "logging", "xterm"], "repository": {"type": "git", "url": "git://github.com/medikoo/cli-color.git"}, "dependencies": {"ansi-regex": "^2.1.1", "d": "1", "es5-ext": "^0.10.46", "es6-iterator": "^2.0.3", "memoizee": "^0.4.14", "timers-ext": "^0.1.5"}, "devDependencies": {"eslint": "^5.4", "eslint-config-medikoo-es5": "^1.6.1", "tad": "^0.2.7"}, "eslintConfig": {"extends": "medikoo-es5", "root": true, "env": {"node": true}, "rules": {"id-length": "off"}, "overrides": [{"files": "examples/**", "rules": {"no-console": "off"}}]}, "scripts": {"lint": "eslint --ignore-path=.gitignore .", "test": "node ./node_modules/tad/bin/tad"}, "license": "ISC"}