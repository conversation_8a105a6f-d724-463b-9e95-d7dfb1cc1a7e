{"version": 3, "file": "_patch-base.js", "sourceRoot": "", "sources": ["../src/_patch-base.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;;;;AAE3D,wEAAwE;AACxE,EAAE;AACF,oHAAoH;AACpH,EAAE;AACF,kEAAkE;AAClE,EAAE;AAEF,gDAAwB;AAExB,MAAM,uBAAuB,GAA6B,CAAC,EAAE,EAAE,EAAE,CAC/D,OAAO,EAAE,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAE,IAAI,MAAM,IAAI,EAAE,IAAK,EAAwB,CAAC,IAAI,KAAK,kBAAkB,CAAC;AAoOxG,0DAAuB;AAlOzB,+BAA+B;AAC/B,oDAAoD;AACpD,IAAI,kBAAkB,GAAuB,SAAS,CAAC;AAEvD,0CAA0C;AAC1C,2DAA2D;AAC3D,IAAI,sBAAsB,GAAuB,SAAS,CAAC;AAE3D,8CAA8C;AAC9C,sEAAsE;AACtE,IAAI,kBAAkB,GAAuB,SAAS,CAAC;AAEvD,4BAA4B;AAC5B,oDAAoD;AACpD,IAAI,UAAU,GAAuB,SAAS,CAAC;AAE/C,uDAAuD;AACvD,qCAAqC;AACrC,IAAI,YAAY,GAAuB,SAAS,CAAC;AA2M/C,oCAAY;AAzMd,uCAAuC;AACvC,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;IAChD,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACxB,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACpD,yEAAyE;YACzE,2CAA2C;YAC3C,IAAI,CAAC;gBACH,MAAM,kBAAkB,GAAW,cAAI,CAAC,OAAO,CAC7C,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,CAClF,CAAC;gBAEF,6DAA6D;gBAC7D,0CAA0C;gBAC1C,MAAM,0BAA0B,GAAW,cAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,mBAAmB,CAAC,CAAC;gBAC9F,IAAI,0BAA0B,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC1D,kBAAkB,GAAG,0BAA0B,CAAC;gBAClD,CAAC;YACH,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,4DAA4D;gBAC5D,4DAA4D;gBAC5D,iBAAiB;gBACjB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;SAAM,CAAC;QACN,0CAA0C;QAC1C,4CAA4C;QAC5C,IAAI,CAAC;YACH,MAAM,qBAAqB,GAAW,cAAI,CAAC,OAAO,CAChD,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;gBACrC,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;aAC5B,CAAC,CACH,CAAC;YAEF,6DAA6D;YAC7D,0CAA0C;YAC1C,IAAI,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,qBAAqB,GAAG,cAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACxE,uBAAA,YAAY,GAAG,qBAAqB,CAAC;gBACrC,MAAM;YACR,CAAC;QACH,CAAC;QAAC,OAAO,EAAW,EAAE,CAAC;YACrB,4DAA4D;YAC5D,4DAA4D;YAC5D,iBAAiB;YACjB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;gBACjC,MAAM,EAAE,CAAC;YACX,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAM;IACR,CAAC;IACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;AACvC,CAAC;AAED,IAAI,CAAC,YAAY,EAAE,CAAC;IAClB,wCAAwC;IACxC,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;QAChD,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAC5B,gEAAgE;YAChE,qDAAqD;YACrD,IAAI,CAAC;gBACH,MAAM,cAAc,GAAW,cAAI,CAAC,OAAO,CACzC,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE;oBAC/C,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;gBAEF,MAAM,8BAA8B,GAAW,cAAI,CAAC,IAAI,CACtD,cAAc,EACd,8BAA8B,CAC/B,CAAC;gBACF,IAAI,8BAA8B,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9D,sBAAsB,GAAG,8BAA8B,CAAC;oBACxD,kBAAkB,GAAG,GAAG,cAAc,sCAAsC,CAAC;oBAC7E,UAAU,GAAG,GAAG,cAAc,oBAAoB,CAAC;gBACrD,CAAC;YACH,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,4DAA4D;gBAC5D,4DAA4D;gBAC5D,iBAAiB;gBACjB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5D,0CAA0C;YAC1C,4CAA4C;YAC5C,IAAI,CAAC;gBACH,MAAM,qBAAqB,GAAW,cAAI,CAAC,OAAO,CAChD,OAAO,CAAC,OAAO,CAAC,qBAAqB,EAAE;oBACrC,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;gBAEF,IAAI,cAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,8BAA8B,CAAC,KAAK,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAChG,uBAAA,YAAY,GAAG,qBAAqB,CAAC;oBACrC,MAAM;gBACR,CAAC;YACH,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,4DAA4D;gBAC5D,4DAA4D;gBAC5D,gBAAgB;gBAChB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,MAAM;QACR,CAAC;QACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;IACvC,CAAC;AACH,CAAC;AAED,IAAI,CAAC,YAAY,EAAE,CAAC;IAClB,gCAAgC;IAChC,KAAK,IAAI,aAAa,GAAe,MAAM,IAAM,CAAC;QAChD,gEAAgE;QAChE,sDAAsD;QACtD,IAAI,mEAAmE,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YACrG,uBAAA,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,cAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;YACxE,sBAAsB,GAAG,GAAG,YAAY,sCAAsC,CAAC;YAC/E,kBAAkB,GAAG,GAAG,YAAY,sCAAsC,CAAC;YAE3E,2GAA2G;YAC3G,4GAA4G;YAC5G,IAAI,cAAkC,CAAC;YACvC,IAAI,CAAC;gBACH,cAAc,GAAG,cAAI,CAAC,OAAO,CAC3B,OAAO,CAAC,OAAO,CAAC,+BAA+B,EAAE;oBAC/C,KAAK,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC;iBAC5B,CAAC,CACH,CAAC;YACJ,CAAC;YAAC,OAAO,EAAW,EAAE,CAAC;gBACrB,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC;oBACjC,MAAM,EAAE,CAAC;gBACX,CAAC;YACH,CAAC;YAED,UAAU,GAAG,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,YAAY,oBAAoB,CAAC;YACnE,MAAM;QACR,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;YAC1B,+CAA+C;YAC/C,MAAM,IAAI,KAAK,CACb,yEAAyE;gBACvE,kGAAkG;gBAClG,+CAA+C,CAClD,CAAC;QACJ,CAAC;QACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;IACvC,CAAC;AACH,CAAC;AAED,oCAAoC;AACpC,MAAM,qBAAqB,GAAW,GAAG,YAAY,eAAe,CAAC;AACrE,MAAM,mBAAmB,GAAwB,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACnE,QAAA,oBAAoB,GAAW,mBAAmB,CAAC,OAAO,CAAC;AACxE,MAAM,oBAAoB,GAAW,QAAQ,CAAC,4BAAoB,EAAE,EAAE,CAAC,CAAC;AAyCtE,oDAAoB;AAxCtB,IAAI,KAAK,CAAC,oBAAoB,CAAC,EAAE,CAAC;IAChC,MAAM,IAAI,KAAK,CACb,mCAAmC,4BAAoB,cAAc,qBAAqB,GAAG,CAC9F,CAAC;AACJ,CAAC;AAED,IAAI,CAAC,CAAC,oBAAoB,IAAI,CAAC,IAAI,oBAAoB,IAAI,CAAC,CAAC,EAAE,CAAC;IAC9D,MAAM,IAAI,KAAK,CACb,0FAA0F;QACxF,mBAAmB,4BAAoB,KAAK;QAC5C,sCAAsC;QACtC,+CAA+C,CAClD,CAAC;AACJ,CAAC;AAED,8DAA8D;AAC9D,IAAI,kBAAuB,CAAC;AAC5B,IAAI,oBAAoB,IAAI,CAAC,EAAE,CAAC;IAC9B,6BAAA,kBAAkB,GAAG,OAAO,CAAC,kBAAmB,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC;AAC9E,CAAC;KAAM,CAAC;IACN,6BAAA,kBAAkB,GAAG,OAAO,CAAC,sBAAuB,CAAC,CAAC,kBAAkB,CAAC;AAC3E,CAAC;AAED,8DAA8D;AAC9D,IAAI,cAAgC,CAAC;AACrC,8DAA8D;AAC9D,IAAI,MAAqC,CAAC;AAC1C,IAAI,oBAAoB,IAAI,CAAC,EAAE,CAAC;IAC9B,yBAAA,cAAc,GAAG,OAAO,CAAC,kBAAmB,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;IACpE,iBAAA,MAAM,GAAG,OAAO,CAAC,kBAAmB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;AACtD,CAAC;KAAM,CAAC;IACN,yBAAA,cAAc,GAAG,OAAO,CAAC,kBAAmB,CAAC,CAAC;IAC9C,iBAAA,MAAM,GAAG,OAAO,CAAC,UAAW,CAAC,CAAC;AAChC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n// This is a workaround for https://github.com/eslint/eslint/issues/3458\n//\n// To correct how ESLint searches for plugin packages, add this line to the top of your project's .eslintrc.js file:\n//\n//    require(\"@rushstack/eslint-patch/modern-module-resolution\");\n//\n\nimport path from 'path';\n\nconst isModuleResolutionError: (ex: unknown) => boolean = (ex) =>\n  typeof ex === 'object' && !!ex && 'code' in ex && (ex as { code: unknown }).code === 'MODULE_NOT_FOUND';\n\n// Module path for eslintrc.cjs\n// Example: \".../@eslint/eslintrc/dist/eslintrc.cjs\"\nlet eslintrcBundlePath: string | undefined = undefined;\n\n// Module path for config-array-factory.js\n// Example: \".../@eslint/eslintrc/lib/config-array-factory\"\nlet configArrayFactoryPath: string | undefined = undefined;\n\n// Module path for relative-module-resolver.js\n// Example: \".../@eslint/eslintrc/lib/shared/relative-module-resolver\"\nlet moduleResolverPath: string | undefined = undefined;\n\n// Module path for naming.js\n// Example: \".../@eslint/eslintrc/lib/shared/naming\"\nlet namingPath: string | undefined = undefined;\n\n// Folder path where ESLint's package.json can be found\n// Example: \".../node_modules/eslint\"\nlet eslintFolder: string | undefined = undefined;\n\n// Probe for the ESLint >=8.0.0 layout:\nfor (let currentModule: NodeModule = module; ; ) {\n  if (!eslintrcBundlePath) {\n    if (currentModule.filename.endsWith('eslintrc.cjs')) {\n      // For ESLint >=8.0.0, all @eslint/eslintrc code is bundled at this path:\n      //   .../@eslint/eslintrc/dist/eslintrc.cjs\n      try {\n        const eslintrcFolderPath: string = path.dirname(\n          require.resolve('@eslint/eslintrc/package.json', { paths: [currentModule.path] })\n        );\n\n        // Make sure we actually resolved the module in our call path\n        // and not some other spurious dependency.\n        const resolvedEslintrcBundlePath: string = path.join(eslintrcFolderPath, 'dist/eslintrc.cjs');\n        if (resolvedEslintrcBundlePath === currentModule.filename) {\n          eslintrcBundlePath = resolvedEslintrcBundlePath;\n        }\n      } catch (ex: unknown) {\n        // Module resolution failures are expected, as we're walking\n        // up our require stack to look for eslint. All other errors\n        // are re-thrown.\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n    }\n  } else {\n    // Next look for a file in ESLint's folder\n    //   .../eslint/lib/cli-engine/cli-engine.js\n    try {\n      const eslintCandidateFolder: string = path.dirname(\n        require.resolve('eslint/package.json', {\n          paths: [currentModule.path]\n        })\n      );\n\n      // Make sure we actually resolved the module in our call path\n      // and not some other spurious dependency.\n      if (currentModule.filename.startsWith(eslintCandidateFolder + path.sep)) {\n        eslintFolder = eslintCandidateFolder;\n        break;\n      }\n    } catch (ex: unknown) {\n      // Module resolution failures are expected, as we're walking\n      // up our require stack to look for eslint. All other errors\n      // are re-thrown.\n      if (!isModuleResolutionError(ex)) {\n        throw ex;\n      }\n    }\n  }\n\n  if (!currentModule.parent) {\n    break;\n  }\n  currentModule = currentModule.parent;\n}\n\nif (!eslintFolder) {\n  // Probe for the ESLint >=7.12.0 layout:\n  for (let currentModule: NodeModule = module; ; ) {\n    if (!configArrayFactoryPath) {\n      // For ESLint >=7.12.0, config-array-factory.js is at this path:\n      //   .../@eslint/eslintrc/lib/config-array-factory.js\n      try {\n        const eslintrcFolder: string = path.dirname(\n          require.resolve('@eslint/eslintrc/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n\n        const resolvedConfigArrayFactoryPath: string = path.join(\n          eslintrcFolder,\n          '/lib/config-array-factory.js'\n        );\n        if (resolvedConfigArrayFactoryPath === currentModule.filename) {\n          configArrayFactoryPath = resolvedConfigArrayFactoryPath;\n          moduleResolverPath = `${eslintrcFolder}/lib/shared/relative-module-resolver`;\n          namingPath = `${eslintrcFolder}/lib/shared/naming`;\n        }\n      } catch (ex: unknown) {\n        // Module resolution failures are expected, as we're walking\n        // up our require stack to look for eslint. All other errors\n        // are re-thrown.\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n    } else if (currentModule.filename.endsWith('cli-engine.js')) {\n      // Next look for a file in ESLint's folder\n      //   .../eslint/lib/cli-engine/cli-engine.js\n      try {\n        const eslintCandidateFolder: string = path.dirname(\n          require.resolve('eslint/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n\n        if (path.join(eslintCandidateFolder, 'lib/cli-engine/cli-engine.js') === currentModule.filename) {\n          eslintFolder = eslintCandidateFolder;\n          break;\n        }\n      } catch (ex: unknown) {\n        // Module resolution failures are expected, as we're walking\n        // up our require stack to look for eslint. All other errors\n        // are rethrown.\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n    }\n\n    if (!currentModule.parent) {\n      break;\n    }\n    currentModule = currentModule.parent;\n  }\n}\n\nif (!eslintFolder) {\n  // Probe for the <7.12.0 layout:\n  for (let currentModule: NodeModule = module; ; ) {\n    // For ESLint <7.12.0, config-array-factory.js was at this path:\n    //   .../eslint/lib/cli-engine/config-array-factory.js\n    if (/[\\\\/]eslint[\\\\/]lib[\\\\/]cli-engine[\\\\/]config-array-factory\\.js$/i.test(currentModule.filename)) {\n      eslintFolder = path.join(path.dirname(currentModule.filename), '../..');\n      configArrayFactoryPath = `${eslintFolder}/lib/cli-engine/config-array-factory`;\n      moduleResolverPath = `${eslintFolder}/lib/shared/relative-module-resolver`;\n\n      // The naming module was moved to @eslint/eslintrc in ESLint 7.8.0, which is also when the @eslint/eslintrc\n      // package was created and added to ESLint, so we need to probe for whether it's in the old or new location.\n      let eslintrcFolder: string | undefined;\n      try {\n        eslintrcFolder = path.dirname(\n          require.resolve('@eslint/eslintrc/package.json', {\n            paths: [currentModule.path]\n          })\n        );\n      } catch (ex: unknown) {\n        if (!isModuleResolutionError(ex)) {\n          throw ex;\n        }\n      }\n\n      namingPath = `${eslintrcFolder ?? eslintFolder}/lib/shared/naming`;\n      break;\n    }\n\n    if (!currentModule.parent) {\n      // This was tested with ESLint 6.1.0 .. 7.12.1.\n      throw new Error(\n        'Failed to patch ESLint because the calling module was not recognized.\\n' +\n          'If you are using a newer ESLint version that may be unsupported, please create a GitHub issue:\\n' +\n          'https://github.com/microsoft/rushstack/issues'\n      );\n    }\n    currentModule = currentModule.parent;\n  }\n}\n\n// Detect the ESLint package version\nconst eslintPackageJsonPath: string = `${eslintFolder}/package.json`;\nconst eslintPackageObject: { version: string } = require(eslintPackageJsonPath);\nexport const eslintPackageVersion: string = eslintPackageObject.version;\nconst ESLINT_MAJOR_VERSION: number = parseInt(eslintPackageVersion, 10);\nif (isNaN(ESLINT_MAJOR_VERSION)) {\n  throw new Error(\n    `Unable to parse ESLint version \"${eslintPackageVersion}\" in file \"${eslintPackageJsonPath}\"`\n  );\n}\n\nif (!(ESLINT_MAJOR_VERSION >= 6 && ESLINT_MAJOR_VERSION <= 9)) {\n  throw new Error(\n    'The ESLint patch script has only been tested with ESLint version 6.x, 7.x, 8.x, and 9.x.' +\n      ` (Your version: ${eslintPackageVersion})\\n` +\n      'Consider reporting a GitHub issue:\\n' +\n      'https://github.com/microsoft/rushstack/issues'\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet configArrayFactory: any;\nif (ESLINT_MAJOR_VERSION >= 8) {\n  configArrayFactory = require(eslintrcBundlePath!).Legacy.ConfigArrayFactory;\n} else {\n  configArrayFactory = require(configArrayFactoryPath!).ConfigArrayFactory;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet ModuleResolver: { resolve: any };\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nlet Naming: { normalizePackageName: any };\nif (ESLINT_MAJOR_VERSION >= 8) {\n  ModuleResolver = require(eslintrcBundlePath!).Legacy.ModuleResolver;\n  Naming = require(eslintrcBundlePath!).Legacy.naming;\n} else {\n  ModuleResolver = require(moduleResolverPath!);\n  Naming = require(namingPath!);\n}\n\nexport {\n  eslintFolder,\n  configArrayFactory,\n  ModuleResolver,\n  Naming,\n  ESLINT_MAJOR_VERSION,\n  isModuleResolutionError\n};\n"]}