/**
 * @license
 * Copyright 2020 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-core/dist/util_base" />
import { BackendValues, DataType, DataTypeMap, FlatVector, NumericDataType, TensorLike, TypedArray, WebGLData, WebGPUData } from './types';
/**
 * Shuffles the array in-place using <PERSON><PERSON><PERSON> algorithm.
 *
 * ```js
 * const a = [1, 2, 3, 4, 5];
 * tf.util.shuffle(a);
 * console.log(a);
 * ```
 *
 * @param array The array to shuffle in-place.
 *
 * @doc {heading: 'Util', namespace: 'util'}
 */
export declare function shuffle(array: any[] | Uint32Array | Int32Array | Float32Array): void;
/**
 * Shuffles two arrays in-place the same way using Fisher-Yates algorithm.
 *
 * ```js
 * const a = [1,2,3,4,5];
 * const b = [11,22,33,44,55];
 * tf.util.shuffleCombo(a, b);
 * console.log(a, b);
 * ```
 *
 * @param array The first array to shuffle in-place.
 * @param array2 The second array to shuffle in-place with the same permutation
 *     as the first array.
 *
 * @doc {heading: 'Util', namespace: 'util'}
 */
export declare function shuffleCombo(array: any[] | Uint32Array | Int32Array | Float32Array, array2: any[] | Uint32Array | Int32Array | Float32Array): void;
/** Clamps a value to a specified range. */
export declare function clamp(min: number, x: number, max: number): number;
export declare function nearestLargerEven(val: number): number;
export declare function swap<T>(object: {
    [index: number]: T;
}, left: number, right: number): void;
export declare function sum(arr: number[]): number;
/**
 * Returns a sample from a uniform [a, b) distribution.
 *
 * @param a The minimum support (inclusive).
 * @param b The maximum support (exclusive).
 * @return A pseudorandom number on the half-open interval [a,b).
 */
export declare function randUniform(a: number, b: number): number;
/** Returns the squared Euclidean distance between two vectors. */
export declare function distSquared(a: FlatVector, b: FlatVector): number;
/**
 * Asserts that the expression is true. Otherwise throws an error with the
 * provided message.
 *
 * ```js
 * const x = 2;
 * tf.util.assert(x === 2, 'x is not 2');
 * ```
 *
 * @param expr The expression to assert (as a boolean).
 * @param msg A function that returns the message to report when throwing an
 *     error. We use a function for performance reasons.
 *
 * @doc {heading: 'Util', namespace: 'util'}
 */
export declare function assert(expr: boolean, msg: () => string): void;
export declare function assertShapesMatch(shapeA: number[], shapeB: number[], errorMessagePrefix?: string): void;
export declare function assertNonNull(a: TensorLike): void;
/**
 * Returns the size (number of elements) of the tensor given its shape.
 *
 * ```js
 * const shape = [3, 4, 2];
 * const size = tf.util.sizeFromShape(shape);
 * console.log(size);
 * ```
 *
 * @doc {heading: 'Util', namespace: 'util'}
 */
export declare function sizeFromShape(shape: number[]): number;
export declare function isScalarShape(shape: number[]): boolean;
export declare function arraysEqualWithNull(n1: number[], n2: number[]): boolean;
export declare function arraysEqual(n1: FlatVector, n2: FlatVector): boolean;
export declare function isInt(a: number): boolean;
export declare function tanh(x: number): number;
export declare function sizeToSquarishShape(size: number): [number, number];
/**
 * Creates a new array with randomized indices to a given quantity.
 *
 * ```js
 * const randomTen = tf.util.createShuffledIndices(10);
 * console.log(randomTen);
 * ```
 *
 * @param number Quantity of how many shuffled indices to create.
 *
 * @doc {heading: 'Util', namespace: 'util'}
 */
export declare function createShuffledIndices(n: number): Uint32Array;
export declare function rightPad(a: string, size: number): string;
export declare function repeatedTry(checkFn: () => boolean, delayFn?: (counter: number) => number, maxCounter?: number, scheduleFn?: (functionRef: Function, delay: number) => void): Promise<void>;
/**
 * Given the full size of the array and a shape that may contain -1 as the
 * implicit dimension, returns the inferred shape where -1 is replaced.
 * E.g. For shape=[2, -1, 3] and size=24, it will return [2, 4, 3].
 *
 * @param shape The shape, which may contain -1 in some dimension.
 * @param size The full size (number of elements) of the array.
 * @return The inferred shape where -1 is replaced with the inferred size.
 */
export declare function inferFromImplicitShape(shape: number[], size: number): number[];
export declare function parseAxisParam(axis: number | number[], shape: number[]): number[];
/** Reduces the shape by removing all dimensions of shape 1. */
export declare function squeezeShape(shape: number[], axis?: number[]): {
    newShape: number[];
    keptDims: number[];
};
export declare function getTypedArrayFromDType<D extends NumericDataType>(dtype: D, size: number): DataTypeMap[D];
export declare function getArrayFromDType<D extends DataType>(dtype: D, size: number): DataTypeMap[D];
export declare function checkConversionForErrors<D extends DataType>(vals: DataTypeMap[D] | number[], dtype: D): void;
/** Returns true if the dtype is valid. */
export declare function isValidDtype(dtype: DataType): boolean;
/**
 * Returns true if the new type can't encode the old type without loss of
 * precision.
 */
export declare function hasEncodingLoss(oldType: DataType, newType: DataType): boolean;
export declare function bytesPerElement(dtype: DataType): number;
/**
 * Returns the approximate number of bytes allocated in the string array - 2
 * bytes per character. Computing the exact bytes for a native string in JS
 * is not possible since it depends on the encoding of the html page that
 * serves the website.
 */
export declare function bytesFromStringArray(arr: Uint8Array[]): number;
/** Returns true if the value is a string. */
export declare function isString(value: {}): value is string;
export declare function isBoolean(value: {}): boolean;
export declare function isNumber(value: {}): boolean;
export declare function inferDtype(values: TensorLike | WebGLData | WebGPUData): DataType;
export declare function isFunction(f: Function): boolean;
export declare function nearestDivisor(size: number, start: number): number;
export declare function computeStrides(shape: number[]): number[];
export declare function toNestedArray(shape: number[], a: TypedArray, isComplex?: boolean): number | any[];
export declare function convertBackendValuesAndArrayBuffer(data: BackendValues | ArrayBuffer, dtype: DataType): Float32Array | Int32Array | Uint8Array | Uint8Array[];
export declare function makeOnesTypedArray<D extends DataType>(size: number, dtype: D): DataTypeMap[D];
export declare function makeZerosTypedArray<D extends DataType>(size: number, dtype: D): DataTypeMap[D];
/**
 * Make nested `TypedArray` filled with zeros.
 * @param shape The shape information for the nested array.
 * @param dtype dtype of the array element.
 */
export declare function makeZerosNestedTypedArray<D extends DataType>(shape: number[], dtype: D): number | any[];
export declare function assertNonNegativeIntegerDimensions(shape: number[]): void;
/**
 * Computes flat index for a given location (multidimentionsal index) in a
 * Tensor/multidimensional array.
 *
 * @param locs Location in the tensor.
 * @param rank Rank of the tensor.
 * @param strides Tensor strides.
 */
export declare function locToIndex(locs: number[], rank: number, strides: number[]): number;
/**
 * Computes the location (multidimensional index) in a
 * tensor/multidimentional array for a given flat index.
 *
 * @param index Index in flat array.
 * @param rank Rank of tensor.
 * @param strides Strides of tensor.
 */
export declare function indexToLoc(index: number, rank: number, strides: number[]): number[];
/**
 * This method asserts whether an object is a Promise instance.
 * @param object
 */
export declare function isPromise(object: any): object is Promise<unknown>;
