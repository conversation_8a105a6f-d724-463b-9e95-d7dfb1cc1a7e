{"name": "source-map-js", "description": "Generates and consumes source maps", "version": "1.2.1", "homepage": "https://github.com/7rulnik/source-map-js", "author": "Valentin 7rulnik Semirulnik <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "usrbincc <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> Smith <<EMAIL>>", "<PERSON> <<EMAIL>>", "azu <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@walmartlabs.com>", "<PERSON> <jeff<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "djchie <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "7rulnik/source-map-js", "main": "./source-map.js", "files": ["source-map.js", "source-map.d.ts", "lib/"], "engines": {"node": ">=0.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"test": "npm run build && node test/run-tests.js", "build": "webpack --color", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"clean-publish": "^3.1.0", "doctoc": "^0.15.0", "webpack": "^1.12.0"}, "clean-publish": {"cleanDocs": true}, "typings": "source-map.d.ts"}