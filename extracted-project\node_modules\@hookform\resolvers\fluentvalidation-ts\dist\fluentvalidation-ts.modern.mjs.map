{"version": 3, "file": "fluentvalidation-ts.modern.mjs", "sources": ["../src/fluentvalidation-ts.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  AsyncValidator,\n  ValidationErrors,\n  Validator,\n} from 'fluentvalidation-ts';\nimport { FieldError, FieldValues, Resolver } from 'react-hook-form';\n\nfunction traverseObject<T>(\n  object: ValidationErrors<T>,\n  errors: Record<string, FieldError>,\n  parentIndices: (string | number)[] = [],\n) {\n  for (const key in object) {\n    const currentIndex = [...parentIndices, key];\n    const currentValue = object[key];\n\n    if (Array.isArray(currentValue)) {\n      currentValue.forEach((item: any, index: number) => {\n        traverseObject(item, errors, [...currentIndex, index]);\n      });\n    } else if (typeof currentValue === 'object' && currentValue !== null) {\n      traverseObject(currentValue, errors, currentIndex);\n    } else if (typeof currentValue === 'string') {\n      errors[currentIndex.join('.')] = {\n        type: 'validation',\n        message: currentValue,\n      };\n    }\n  }\n}\n\nconst parseErrorSchema = <T>(\n  validationErrors: ValidationErrors<T>,\n  validateAllFieldCriteria: boolean,\n) => {\n  if (validateAllFieldCriteria) {\n    // TODO: check this but i think its always one validation error\n  }\n\n  const errors: Record<string, FieldError> = {};\n  traverseObject(validationErrors, errors);\n\n  return errors;\n};\n\nexport function fluentValidationResolver<TFieldValues extends FieldValues>(\n  validator: Validator<TFieldValues>,\n): Resolver<TFieldValues> {\n  return async (values, _context, options) => {\n    const validationResult = validator.validate(values);\n    const isValid = Object.keys(validationResult).length === 0;\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return isValid\n      ? {\n          values: values,\n          errors: {},\n        }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validationResult,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n}\n\nexport function fluentAsyncValidationResolver<\n  TFieldValues extends FieldValues,\n  TValidator extends AsyncValidator<TFieldValues>,\n>(validator: TValidator): Resolver<TFieldValues> {\n  return async (values, _context, options) => {\n    const validationResult = await validator.validateAsync(values);\n    const isValid = Object.keys(validationResult).length === 0;\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    return isValid\n      ? {\n          values: values,\n          errors: {},\n        }\n      : {\n          values: {},\n          errors: toNestErrors(\n            parseErrorSchema(\n              validationResult,\n              !options.shouldUseNativeValidation &&\n                options.criteriaMode === 'all',\n            ),\n            options,\n          ),\n        };\n  };\n}\n"], "names": ["traverseObject", "object", "errors", "parentIndices", "key", "currentIndex", "currentValue", "Array", "isArray", "for<PERSON>ach", "item", "index", "join", "type", "message", "parseErrorSchema", "validationErrors", "validateAllFieldCriteria", "fluentValidationResolver", "validator", "async", "values", "_context", "options", "validationResult", "validate", "<PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "length", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors", "fluentAsyncValidationResolver", "validateAsync"], "mappings": "+EAQA,SAASA,EACPC,EACAC,EACAC,EAAqC,IAErC,IAAK,MAAMC,KAAOH,EAAQ,CACxB,MAAMI,EAAe,IAAIF,EAAeC,GAClCE,EAAeL,EAAOG,GAExBG,MAAMC,QAAQF,GAChBA,EAAaG,QAAQ,CAACC,EAAWC,KAC/BX,EAAeU,EAAMR,EAAQ,IAAIG,EAAcM,GAAM,GAEtB,iBAAjBL,GAA8C,OAAjBA,EAC7CN,EAAeM,EAAcJ,EAAQG,GACJ,iBAAjBC,IAChBJ,EAAOG,EAAaO,KAAK,MAAQ,CAC/BC,KAAM,aACNC,QAASR,GAGf,CACF,CAEA,MAAMS,EAAmBA,CACvBC,EACAC,KAMA,MAAMf,EAAqC,CAAA,EAG3C,OAFAF,EAAegB,EAAkBd,GAE1BA,YAGOgB,EACdC,GAEA,OAAOC,MAAOC,EAAQC,EAAUC,KAC9B,MAAMC,EAAmBL,EAAUM,SAASJ,GACtCK,EAAmD,IAAzCC,OAAOC,KAAKJ,GAAkBK,OAI9C,OAFAN,EAAQO,2BAA6BC,EAAuB,CAAE,EAAER,GAEzDG,EACH,CACEL,OAAQA,EACRnB,OAAQ,CAAA,GAEV,CACEmB,OAAQ,CAAE,EACVnB,OAAQ8B,EACNjB,EACES,GAIFD,IAIZ,CAEgB,SAAAU,EAGdd,GACA,OAAcE,MAAAA,EAAQC,EAAUC,KAC9B,MAAMC,QAAyBL,EAAUe,cAAcb,GACjDK,EAAmD,IAAzCC,OAAOC,KAAKJ,GAAkBK,OAI9C,OAFAN,EAAQO,2BAA6BC,EAAuB,CAAE,EAAER,GAEzDG,EACH,CACEL,OAAQA,EACRnB,OAAQ,CACT,GACD,CACEmB,OAAQ,CAAA,EACRnB,OAAQ8B,EACNjB,EACES,GAIFD,IAIZ"}