/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-converter/dist/operations/operation_executor" />
import * as tfc from '@tensorflow/tfjs-core';
import { NamedTensorsMap } from '../data/types';
import { ExecutionContext } from '../executor/execution_context';
import { ResourceManager } from '../executor/resource_manager';
import { Node } from './types';
/**
 * Executes the op defined by the node object.
 * @param node
 * @param tensorMap contains tensors for executed nodes and weights
 * @param context contains tensors and information for running the current node.
 * @param resourceManager Optional. Contains global resources of the model.
 */
export declare function executeOp(node: Node, tensorMap: NamedTensorsMap, context: ExecutionContext, resourceManager?: ResourceManager, tidy?: typeof tfc.tidy): tfc.Tensor[] | Promise<tfc.Tensor[]>;
