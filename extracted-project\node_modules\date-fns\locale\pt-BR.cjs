"use strict";
exports.ptBR = void 0;
var _index = require("./pt-BR/_lib/formatDistance.cjs");
var _index2 = require("./pt-BR/_lib/formatLong.cjs");
var _index3 = require("./pt-BR/_lib/formatRelative.cjs");
var _index4 = require("./pt-BR/_lib/localize.cjs");
var _index5 = require("./pt-BR/_lib/match.cjs");

/**
 * @category Locales
 * @summary Portuguese locale (Brazil).
 * @language Portuguese
 * @iso-639-2 por
 * <AUTHOR> [@duailibe](https://github.com/duailibe)
 * <AUTHOR> [@yagocarballo](https://github.com/YagoCarballo)
 */
const ptBR = (exports.ptBR = {
  code: "pt-BR",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
