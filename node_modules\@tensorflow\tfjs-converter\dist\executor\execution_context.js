/**
 * ExecutionContext captures the runtime environment of the node. It keeps
 * track of the current frame and iteration for the control flow ops.
 *
 * For example, typical Dynamic RNN model may contain loops, for which
 * TensorFlow will generate graphs with Enter/Exit nodes to control the
 * current execution frame, and NextIteration Nodes for iteration id increment.
 * For model with branch logic, TensorFLow will generate Switch/Merge ops.
 */
export class ExecutionContext {
    constructor(weightMap = {}, tensorArrayMap = {}, tensorListMap = {}, functionMap = {}, parseNodeNameCache) {
        this.weightMap = weightMap;
        this.tensorArrayMap = tensorArrayMap;
        this.tensorListMap = tensorListMap;
        this.functionMap = functionMap;
        this.parseNodeNameCache = parseNodeNameCache;
        this.rootContext = { id: 0, frameName: '', iterationId: 0 };
        this.contexts = [this.rootContext];
        this.lastId = 0;
        this.generateCurrentContextIds();
    }
    newFrame(id, frameName) {
        return { id, frameName, iterationId: 0 };
    }
    /**
     * Set the current context
     * @param contexts: ExecutionContextInfo[] the current path of execution
     * frames
     */
    set currentContext(contexts) {
        if (this.contexts !== contexts) {
            this.contexts = contexts;
            this.generateCurrentContextIds();
        }
    }
    get currentContext() {
        return this.contexts;
    }
    /**
     * Returns the current context in string format.
     */
    get currentContextId() {
        return this._currentContextIds[0];
    }
    /**
     * Returns the current context and all parent contexts in string format.
     * This allow access to the nodes in the current and parent frames.
     */
    get currentContextIds() {
        return this._currentContextIds;
    }
    generateCurrentContextIds() {
        const names = [];
        for (let i = 0; i < this.contexts.length - 1; i++) {
            const contexts = this.contexts.slice(0, this.contexts.length - i);
            names.push(this.contextIdforContexts(contexts));
        }
        names.push('');
        this._currentContextIds = names;
    }
    contextIdforContexts(contexts) {
        return contexts ?
            contexts
                .map(context => (context.id === 0 && context.iterationId === 0) ?
                '' :
                `${context.frameName}-${context.iterationId}`)
                .join('/') :
            '';
    }
    /**
     * Enter a new frame, a new context is pushed on the current context list.
     * @param frameId new frame id
     */
    enterFrame(frameId) {
        if (this.contexts) {
            this.lastId++;
            this.contexts = this.contexts.slice();
            this.contexts.push(this.newFrame(this.lastId, frameId));
            this._currentContextIds.unshift(this.contextIdforContexts(this.contexts));
        }
    }
    /**
     * Exit the current frame, the last context is removed from the current
     * context list.
     */
    exitFrame() {
        if (this.contexts && this.contexts.length > 1) {
            this.contexts = this.contexts.slice();
            this.contexts.splice(-1);
            this.currentContextIds.shift();
        }
        else {
            throw new Error('Cannot exit frame, the context is empty');
        }
    }
    /**
     * Enter the next iteration of a loop, the iteration id of last context is
     * increased.
     */
    nextIteration() {
        if (this.contexts && this.contexts.length > 0) {
            this.contexts = this.contexts.slice();
            this.lastId++;
            const context = Object.assign({}, this.contexts[this.contexts.length - 1]);
            context.iterationId += 1;
            context.id = this.lastId;
            this.contexts.splice(-1, 1, context);
            this._currentContextIds.splice(0, 1, this.contextIdforContexts(this.contexts));
        }
        else {
            throw new Error('Cannot increase frame iteration, the context is empty');
        }
    }
    getWeight(name) {
        return this.weightMap[name];
    }
    addTensorArray(tensorArray) {
        this.tensorArrayMap[tensorArray.id] = tensorArray;
    }
    getTensorArray(id) {
        return this.tensorArrayMap[id];
    }
    addTensorList(tensorList) {
        this.tensorListMap[tensorList.id] = tensorList;
    }
    getTensorList(id) {
        return this.tensorListMap[id];
    }
    dispose(keepIds) {
        for (const key in this.tensorArrayMap) {
            this.tensorArrayMap[key].clearAndClose(keepIds);
        }
        for (const key in this.tensorListMap) {
            this.tensorListMap[key].clearAndClose(keepIds);
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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